PODS:
  - audio_session (0.0.1):
    - FlutterMacOS
  - audioplayers_darwin (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - flutter_platform_alert (0.0.1):
    - FlutterMacOS
  - flutter_udid (0.0.1):
    - FlutterMacOS
    - SAMKeychain
  - FlutterMacOS (1.0.0)
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SAMKeychain (1.5.3)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - audio_session (from `Flutter/ephemeral/.symlinks/plugins/audio_session/macos`)
  - audioplayers_darwin (from `Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - flutter_platform_alert (from `Flutter/ephemeral/.symlinks/plugins/flutter_platform_alert/macos`)
  - flutter_udid (from `Flutter/ephemeral/.symlinks/plugins/flutter_udid/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - in_app_purchase_storekit (from `Flutter/ephemeral/.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  "******************:app/cocoapods-spec/cocoapods-spec.git":
    - SAMKeychain

EXTERNAL SOURCES:
  audio_session:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_session/macos
  audioplayers_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  flutter_platform_alert:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_platform_alert/macos
  flutter_udid:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_udid/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  in_app_purchase_storekit:
    :path: Flutter/ephemeral/.symlinks/plugins/in_app_purchase_storekit/darwin
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  audio_session: 48ab6500f7a5e7c64363e206565a5dfe5a0c1441
  audioplayers_darwin: 761f2948df701d05b5db603220c384fb55720012
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  flutter_platform_alert: 0373c95e18cdeda110eddcc3cb78fdd64f59dce0
  flutter_udid: d26e455e8c06174e6aff476e147defc6cae38495
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  in_app_purchase_storekit: e126ef1b89e4a9fdf07e28f005f82632b4609437
  package_info_plus: a8a591e70e87ce97ce5d21b2594f69cea9e0312f
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  SAMKeychain: b4d75feafc13c4925bf6b27a2936520efa8a2422
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite: c35dad70033b8862124f8337cc994a809fcd9fa3
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673

PODFILE CHECKSUM: 236401fc2c932af29a9fcf0e97baeeb2d750d367

COCOAPODS: 1.11.0
