{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "flutter_app_kouyu_dev",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_dev.dart",
            "args": [
                "--verbose"
            ]
        },
        {
            "name": "attach ios",
            "request": "attach",
            "type": "dart",
            "program": "lib/main_dev.dart",
            "args": [
                "--app-id",
                "com.xinqu.kouyu"
            ]
        },
        {
            "name": "attach android",
            "request": "attach",
            "type": "dart",
            "program": "lib/main_dev.dart",
            "args": [
                "--verbose"
            ]
        },
        {
            "name": "flutter_app_kouyu_product",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.dart"
        },
        {
            "name": "flutter_app_kouyu (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter_app_kouyu (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}