## v0.0.12
- 更新iOS SDK至v3.1.22
- 修复自定义音频源缺失数据问题

## v0.0.11
- 更新Android SDK至v3.1.19

## v0.0.10
- 更新iOS SDK至v3.1.16
- 更新Android SDK至v3.1.18

## v0.0.9
- 增加临时密钥认证方式
- 修复部分回调信息错误
- 更新iOS Framework版本至3.1.15

## v0.0.8
- 修复一句话识别与录音文件识别极速版获取时间戳问题

## v0.0.7
- 一句话识别Demo增加录音权限获取

## v0.0.6
- 增加一句话识别接口
- 增加录音文件识别极速版接口

## v0.0.5
- 增加内置数据源录音功能

## v0.0.4
- 支持自定义音源

## v0.0.3
- 增加noise_threshold参数

## v0.0.2
- 修复Android平台空指针问题

## v0.0.1

- 新增flutter SDK Android: v3.1.2 iOS: v3.1.3
