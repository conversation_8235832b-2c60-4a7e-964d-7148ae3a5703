import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/config/env.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/modules/chat/view/chat_report_feedback.dart';
import 'package:flutter_app_kouyu/modules/chat/view/chat_report_page.dart';
import 'package:flutter_app_kouyu/modules/chat/view/new_report_page.dart';
import 'package:flutter_app_kouyu/my_app.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

void main() {
  //打包测试环境
  //flutter build ipa --export-method development --target=lib/main_dev.dart
  //sh ./pgyer_upload.sh -k cff5ed5278442901496c6a3c396d0aca build/ios/ipa/flutter_app_kouyu.ipa

  WidgetsFlutterBinding.ensureInitialized();

  realRunApp();
}

void realRunApp() async {
  await CommonUtils.init();
  EnvConfig.init(Env.develop);
  //延时1s
  await Future.delayed(const Duration(seconds: 1));
  runApp(ScreenUtilInit(
      designSize: const Size(750, 1100),
      child: MaterialApp(
        home: ChatReportFeedback(),
      )));
  LogUtil.v("开发环境欢迎你");
}
