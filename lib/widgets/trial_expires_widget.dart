import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/my_app.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TrialExpiresWidget extends StatelessWidget {
  final void Function() close;
  const TrialExpiresWidget({super.key, required this.close});

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomLeft,
      children: [
        Container(
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24.0),
                  topRight: Radius.circular(24.0))),
          child: Image.asset(
            "images/expried_bg.png",
            width: double.infinity,
            height: 270,
            fit: BoxFit.fill,
          ),
        ),
        Column(
          children: [
            const Spacer(),
            SizedBox(
              height: 270,
              child: Column(
                children: [
                  const SizedBox(
                    height: 24,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 32.w + 48.w,
                      ),
                      const Spacer(),
                      const Text(
                        "体验到期",
                        style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w900,
                            color: Color(0xFF061B1F)),
                      ),
                      const Spacer(),
                      SizedBox(
                        width: 48.w,
                        height: 48.w,
                        child: GestureDetector(
                          child: const Icon(Icons.close),
                          onTap: () {
                            close();
                          },
                        ),
                      ),
                      SizedBox(
                        width: 32.w,
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  const Text(
                    "体验时长已用完，订阅后可继续使用",
                    style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        fontStyle: FontStyle.italic,
                        color: Color(0xFF646E70)),
                  ),
                  Container(
                    padding: const EdgeInsets.all(12),
                    child: Image.asset(
                      "images/vip_content.png",
                      width: double.infinity,
                      fit: BoxFit.fitWidth,
                    ),
                  ),
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      Container(
                        height: 52,
                        margin: const EdgeInsetsDirectional.only(
                            start: 20, end: 20, bottom: 24),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          gradient: const LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [Color(0xFFFFE4CC), Color(0xFFFFE2B8)],
                          ),
                        ),
                        child: TextButton(
                            style: const ButtonStyle(
                              textStyle: MaterialStatePropertyAll(TextStyle(
                                  fontSize: 20, fontWeight: FontWeight.w700)),
                            ),
                            onPressed: () {
                              close();
                              globalNavigatorKey.currentState
                                  ?.pushNamed("/open_vip");
                            },
                            child: const Center(
                              child: Text(
                                "查看订阅方案",
                                style: TextStyle(
                                    color: Color(0xFF061B1F),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w800),
                              ),
                            )),
                      ),
                      Positioned(
                        right: 20,
                        top: -4,
                        child: FutureBuilder(
                            future: Api.getPromotionConfig(),
                            builder: (context, snapshot) {
                              return CachedNetworkImage(
                                  fit: BoxFit.fitHeight,
                                  height: 16,
                                  imageUrl:
                                      snapshot.data?.data?.lowerLeftLabelUrl ??
                                          "");
                            }),
                      )
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ],
    );
  }
}
