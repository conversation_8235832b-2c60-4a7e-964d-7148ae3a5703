import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';

typedef AppbarBuilder = AppBar Function(double i);

class CustomeScaffold extends StatefulWidget {
  final AppbarBuilder? appbarBuilder;
  final Widget body;
  final Color? backgroundColor;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  const CustomeScaffold(
      {super.key,
      this.backgroundColor,
      this.floatingActionButton,
      this.floatingActionButtonLocation,
      this.appbarBuilder,
      required this.body});

  @override
  State<CustomeScaffold> createState() => _CustomeScaffoldState();
}

class _CustomeScaffoldState extends State<CustomeScaffold> {
  final APPBAR_SCROLL_OFFSET = 100;
  _onScroll(offset) {
    //offset滚动距离
    double alpha = offset / APPBAR_SCROLL_OFFSET;
    if (alpha < 0) {
      alpha = 0;
    } else if (alpha > 1) {
      alpha = 1;
    }
    setState(() {
      appBarAlpha = alpha;
    });
    LogUtil.d('透明度: $appBarAlpha.toString()');
  }

  double appBarAlpha = 0;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.appbarBuilder != null
          ? widget.appbarBuilder!(appBarAlpha)
          : null,
      floatingActionButton: widget.floatingActionButton,
      floatingActionButtonLocation: widget.floatingActionButtonLocation,
      backgroundColor: widget.backgroundColor,
      body: NotificationListener(
          onNotification: (scrollNotification) {
            if (scrollNotification is ScrollUpdateNotification &&
                scrollNotification.depth == 0) {
              //发生滚动并且是第0个元素
              _onScroll(scrollNotification.metrics.pixels);
            }
            return false;
          },
          child: widget.body),
    );
  }
}
