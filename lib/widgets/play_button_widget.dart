import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/common/utils/log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gif_view/gif_view.dart';
import 'package:lottie/lottie.dart';

enum PlayButtonStyle { white, blue, opacity }

class PlayButtonWidget extends StatelessWidget {
  final String? audioUrl;
  final String? text;
  final PlayButtonStyle style;
  final double? width;
  final double? height;
  final Color? bgColor;
  final Image? image;
  final Widget? gifImage;

  const PlayButtonWidget(
      {super.key,
      this.audioUrl,
      required this.style,
      this.text,
      this.image,
      this.bgColor,
      this.width,
      this.height,
      this.gifImage});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (audioUrl != null) {
          CommonUtils.playVideo(audioUrl, key: audioUrl);
        } else {
          if (text != null) {
            CommonUtils.playTts(text, key: text);
          }
        }
      },
      child: Container(
        decoration: BoxDecoration(
            color: bgColor ??
                (style == PlayButtonStyle.white
                    ? Colors.white
                    : style == PlayButtonStyle.opacity
                        ? null
                        : const Color(0xFFF0F8FA)),
            borderRadius: BorderRadius.circular(16.w)),
        width: width ?? 48.w,
        height: height ?? 48.w,
        child: Center(
          child: StreamBuilder<PlayState>(
              stream: CommonUtils.playStatus,
              builder: (context, snapshot) {
                Log.d("playingButtonState: ${snapshot.data}");

                if (audioUrl != null && CommonUtils.url == audioUrl ||
                    (text != null && CommonUtils.key == text)) {
                  if (snapshot.data == PlayState.ready || false) {
                    return gifImage ??
                        Lottie.asset(
                            style == PlayButtonStyle.opacity
                                ? 'lottie/play_dark.json'
                                : 'lottie/play.json',
                            width: width ?? 48.w,
                            height: height ?? 48.w,
                            repeat: true);
                  }
                  if (CommonUtils.isBuffering) {
                    return Lottie.asset(
                        style == PlayButtonStyle.opacity
                            ? 'lottie/play_buffering_dark.json'
                            : 'lottie/play_buffering.json',
                        width: width ?? 48.w,
                        height: height ?? 48.w,
                        repeat: true);
                  }
                }
                return image ??
                    Image(
                      image: AssetImage(style == PlayButtonStyle.opacity
                          ? "images/chat_play_dark_logo.png"
                          : "images/chat_play_black_logo.png"),
                      width: 48.w,
                      height: 48.w,
                      fit: BoxFit.fill,
                    );
              }),
        ),
      ),
    );
  }
}

class PlayButtonWidget2 extends StatelessWidget {
  final String? audioUrl;
  final String? text;
  const PlayButtonWidget2({super.key, this.audioUrl, this.text});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<PlayState>(
        stream: CommonUtils.playStatus,
        builder: (context, snapshot) {
          if (audioUrl != null && CommonUtils.url == audioUrl ||
              (text != null && CommonUtils.key == text)) {
            if (snapshot.data == PlayState.ready) {
              return GifView.asset(
                "images/chat_play_black_48.gif",
                fit: BoxFit.fill,
                width: 36.w,
                height: 36.w,
                repeat: ImageRepeat.repeat,
              );
            }
            if (snapshot.data == PlayState.loading ||
                snapshot.data == PlayState.buffering) {
              return GifView.asset(
                "images/buffering.gif",
                fit: BoxFit.fill,
                width: 36.w,
                height: 36.w,
                repeat: ImageRepeat.repeat,
              );
            }
          }
          return Image(
            image: const AssetImage("images/chat_play_black_logo.png"),
            width: 36.w,
            height: 36.w,
            fit: BoxFit.fill,
          );
        });
  }
}
