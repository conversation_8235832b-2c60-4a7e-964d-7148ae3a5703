import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/modules/chat/view/follow_read_widget.dart';
import 'package:flutter_app_kouyu/modules/my/pages/sentence_alert.dart';
import 'package:flutter_app_kouyu/modules/my/pages/vocabulary_alert.dart';
import 'package:flutter_app_kouyu/my_app.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SelectableTextUtil {
  static Widget editableText(BuildContext context,
      {required String text, TextStyle? style}) {
    return SelectableText.rich(
        TextSpan(text: "", children: getTextSpan(context, text, style)),
        style: TextStyle(fontWeight: FontWeight.w400, fontSize: 32.w),
        contextMenuBuilder: SelectableTextUtil.chatTextMenuBuilder(context));
  }

  static Widget editableRichText(BuildContext context,
      {required TextSpan textSpan, TextStyle? style}) {
    return SelectableText.rich(textSpan,
        style: TextStyle(fontWeight: FontWeight.w400, fontSize: 32.w),
        contextMenuBuilder: SelectableTextUtil.chatTextMenuBuilder(context));
  }

  static List<InlineSpan>? getTextSpan(
      BuildContext context, String text, TextStyle? style) {
    List<InlineSpan> list = [];
    final textList = text.split(" ");
    for (int i = 0; i < textList.length; i++) {
      list.add(TextSpan(
          text: textList[i],
          style: style,
          recognizer: TapGestureRecognizer()
            ..onTap = () {
              showVocabularyAlertWidget(context, textList[i]);
            }));
      if (i != textList.length - 1) {
        list.add(TextSpan(text: " ", style: style));
      }
    }
    return list;
  }

  static EditableTextContextMenuBuilder chatTextMenuBuilder(
      BuildContext buildContext,
      {num? conversationId}) {
    return (context, editableTextState) {
      return AdaptiveTextSelectionToolbar(
        anchors: editableTextState.contextMenuAnchors,
        children: [
          SelectionTooBarItem(
            label: 'AI 发音',
            onPressed: () {
              final currentText = editableTextState.textEditingValue.selection
                  .textInside(editableTextState.textEditingValue.text);

              hiddenToobar(buildContext);
              showAIAlertWidget(buildContext, currentText);
            },
          ),
          const SelectionToolBarLine(),
          SelectionTooBarItem(
            label: '复制',
            onPressed: () {
              final currentText = editableTextState.textEditingValue.selection
                  .textInside(editableTextState.textEditingValue.text);
              hiddenToobar(buildContext);
              Clipboard.setData(ClipboardData(text: currentText));
              EasyLoading.showToast("已复制");
            },
          ),
          const SelectionToolBarLine(),
          SelectionTooBarItem(
            label: '跟读',
            onPressed: () {
              final currentText = editableTextState.textEditingValue.selection
                  .textInside(editableTextState.textEditingValue.text);
              hiddenToobar(buildContext);
              gotoFollowRead(buildContext, currentText, conversationId);
            },
          ),
          const SelectionToolBarLine(),
          SelectionTooBarItem(
            label: '收藏',
            onPressed: () async {
              final currentText = editableTextState.textEditingValue.selection
                  .textInside(editableTextState.textEditingValue.text);
              Api.collectVocabulary(currentText);
              hiddenToobar(buildContext);
              EasyLoading.showToast("已收藏");
            },
          ),
          const SelectionToolBarLine(),
          SelectionTooBarItem(
            label: '翻译',
            onPressed: () {
              final currentText = editableTextState.textEditingValue.selection
                  .textInside(editableTextState.textEditingValue.text);
              hiddenToobar(buildContext);
              showSentenceAlertWidget(buildContext, currentText);
            },
          )
        ],
      );
    };
  }

  static void hiddenToobar(BuildContext context) {
    FocusScope.of(context).unfocus(); // 点击其他地方收起键盘
  }

  static void showAIAlertWidget(
    BuildContext context,
    String text,
  ) {
    showModalBottomSheet(
        isScrollControlled: false,
        useSafeArea: true,
        constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height -
                MediaQuery.of(context).padding.top),
        backgroundColor: Colors.transparent,
        clipBehavior: Clip.antiAlias,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(40.w),
                topRight: Radius.circular(40.w))),
        context: context,
        builder: (context) {
          return SentenceAlert(
            sentence: text,
            showTranslate: false,
          );
        });
  }

  static void showSentenceAlertWidget(
    BuildContext context,
    String text,
  ) {
    showModalBottomSheet(
        isScrollControlled: false,
        useSafeArea: true,
        constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height -
                MediaQuery.of(context).padding.top),
        backgroundColor: Colors.transparent,
        clipBehavior: Clip.antiAlias,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(40.w),
                topRight: Radius.circular(40.w))),
        context: context,
        builder: (context) {
          return SentenceAlert(
            sentence: text,
            showTranslate: true,
          );
        });
  }

  static void showVocabularyAlertWidget(
    BuildContext context,
    String text,
  ) {
    showModalBottomSheet(
        isScrollControlled: false,
        useSafeArea: true,
        constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height -
                MediaQuery.of(context).padding.top),
        backgroundColor: Colors.transparent,
        clipBehavior: Clip.antiAlias,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(40.w),
                topRight: Radius.circular(40.w))),
        context: context,
        builder: (context) {
          return VocabularyAlertWidget(
            vocabulary: text,
          );
        });
  }

  static void gotoFollowRead(
      BuildContext context, String text, num? conversationId) {
    showModalBottomSheet(
        isScrollControlled: true,
        useSafeArea: true,
        constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height -
                MediaQuery.of(context).padding.top),
        backgroundColor: Colors.transparent,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20))),
        context: context,
        builder: (context) {
          return FollowReadWidget(
            text: text,
            conversationId: conversationId ?? 0,
          );
        });
  }
}

class SelectionToolBarLine extends StatelessWidget {
  const SelectionToolBarLine({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1.w,
      height: double.infinity,
      color: const Color(0xffCEDDE0),
    );
  }
}

class SelectionTooBarItem extends StatelessWidget {
  /// The callback to be called when the button is pressed.
  final VoidCallback? onPressed;
  final String? label;
  const SelectionTooBarItem({
    required this.onPressed,
    this.label,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
          color: Colors.transparent,
          padding: EdgeInsets.only(left: 32.w, right: 32.w),
          child: Column(
            children: [
              const Spacer(),
              Text(
                label!,
                style:
                    TextStyle(color: const Color(0xff272733), fontSize: 28.w),
              ),
              const Spacer(),
            ],
          )),
    );
  }
}
