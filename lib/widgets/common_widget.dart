// 很多通用的组件

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CommonTabItem {
  final String title;
  final int count;

  CommonTabItem({required this.title, int? count}) : count = count ?? 0;
}

/// 水平标签列表组件 [人气(3)][话题(2)]
class Common_TabListWidget extends StatelessWidget {
  final List<CommonTabItem> items;
  final Function(String) onTabSelected;
  final int selectedIndex;

  /// 是否显示数量
  final bool showCount;

  const Common_TabListWidget(
      {super.key,
      required this.items,
      required this.onTabSelected,
      required this.selectedIndex,
      bool? showCount})
      : showCount = showCount ?? false;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          return _buildTab(item, index);
        }).toList(),
      ),
    );
  }

  Widget _buildTab(CommonTabItem item, int index) {
    return GestureDetector(
      onTap: () => onTabSelected(item.title),
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
        decoration: BoxDecoration(
          color: selectedIndex == index
              ? const Color.fromRGBO(65, 192, 255, 0.12)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: selectedIndex == index
                ? const Color.fromRGBO(65, 192, 255, 0.12)
                : const Color.fromRGBO(0, 0, 0, 0.12),
          ),
        ),
        child: Text(
          showCount ? '${item.title}(${item.count})' : item.title,
          style: TextStyle(
            color: selectedIndex == index
                ? const Color(0xFF2693FF)
                : const Color(0xFF272733),
            fontSize: 28.sp,
            fontWeight:
                selectedIndex == index ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}

/// 底部确认按钮组件, [--确认--][--发布--]等等
class Common_BottomConfirmButton extends StatelessWidget {
  final String text;

  /// 按钮背景颜色
  final Color bgColor;

  /// 按钮文字颜色
  final Color textColor;

  /// 水波纹颜色
  final Color splashColor;
  final VoidCallback onTap;

  Common_BottomConfirmButton(
      {super.key,
      required this.text,
      Color? bgColor,
      Color? splashColor,
      Color? textColor,
      VoidCallback? onTap})
      : onTap = onTap ?? (() {}),
        bgColor = bgColor ?? const Color(0xFF84E9FF),
        textColor = textColor ?? const Color(0xFF061B1F),
        splashColor = splashColor ?? const Color(0xFF9BE1FF);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: bgColor,
      borderRadius: BorderRadius.circular(8),
      child: InkWell(
        splashColor: splashColor, // 设置水波纹颜色
        borderRadius: BorderRadius.circular(8),
        onTap: onTap,
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 24.w),
          alignment: Alignment.center,
          child: Center(
            child: Text(
              text,
              style: style_1_32.copyWith(color: textColor),
            ),
          ),
        ),
      ),
    );
  }
}

/// 一个信息标签 icon 和 text [(icon)高级] 【(icon)中级】
class Common_InfoTag extends StatelessWidget {
  /// 图标
  final IconData? icon;
  /// 图片路径
  final String? imagePath;

  /// 文字
  final String text;

  /// 文字颜色
  final Color textColor;

  /// 图标颜色
  final Color iconColor;

  Common_InfoTag(
      {super.key,
      required this.text,
      IconData? icon,
      String? imagePath,
      Color? textColor,
      Color? iconColor})
      : textColor = textColor ?? const Color(0xFF748A99),
        iconColor = iconColor ?? const Color(0xFF748A99),
        icon = icon ?? null,
        imagePath = imagePath ?? null;

  @override
  Widget build(BuildContext context) {
    return Row(mainAxisSize: MainAxisSize.min, children: [
      if (icon != null) ...[
        Icon(icon, size: 16, color: iconColor),
        const SizedBox(width: 4),
      ],
      if (imagePath != null) ...[
        Image.asset(imagePath!, width: 12, height: 12),
        const SizedBox(width: 4),
      ],
      Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: textColor,
        ),
      ),
    ]);
  }
}

/// 等级标签, 根据场景难度等级, 显示不同的图标 [初级][中级][高级]
class Common_LevelTag extends StatelessWidget {
  final String levelName;
  final Color textColor;
  final Color iconColor;

  Common_LevelTag(
      {super.key, required this.levelName, Color? textColor, Color? iconColor})
      : textColor = textColor ?? const Color(0xFF748A99),
        iconColor = iconColor ?? const Color(0xFF748A99);

  @override
  Widget build(BuildContext context) {
    IconData? getIcon() {
      switch (levelName) {
        case '初级':
          return Icons.star_border;
        case '中级':
          return Icons.star_half;
        case '高级':
          return Icons.star;
        default:
          return null;
      }
    }

    IconData? iconData = getIcon();

    return Common_InfoTag(
      icon: iconData,
      imagePath: iconData == null ? 'images/level-basic-icon.png' : null,
      text: levelName,
    );
  }
}

/// 网络图片加载， 如果图片地址为空，则显示默认图片, 避免页面显示错误
class Common_NetImage extends StatelessWidget {
  final String imageUrl;
  final double width;
  final double height;
  final BoxFit fit;

  Common_NetImage(
      {super.key,
      required this.imageUrl,
      double? width,
      double? height,
      BoxFit? fit})
      : width = width ?? 200.w,
        height = height ?? 250.w,
        fit = fit ?? BoxFit.cover;

  @override
  Widget build(BuildContext context) {
    return imageUrl.isEmpty
        ? Image.asset('images/home_bg.png',
            width: width, height: height, fit: fit)
        : Image.network(
            imageUrl,
            width: width,
            height: height,
            fit: fit,
          );
  }
}

/// 举报, 反馈等等组件, 适用于一行3个或者多个选项
class Common_DialogSelectionItemWidget extends StatelessWidget {
  final String text;

  /// 文字颜色
  final Color fontColor;

  /// 背景颜色
  final Color bgColor;

  /// 边框颜色
  final Color borderColor;

  /// 宽度
  final double width;

  Common_DialogSelectionItemWidget(
      {super.key,
      required this.text,
      Color? fontColor,
      Color? bgColor,
      Color? borderColor,
      double? width})
      : width = width ?? 212.w,
        fontColor = fontColor ?? const Color(0xFF272733),
        bgColor = bgColor ?? Colors.white,
        borderColor = borderColor ?? const Color(0xFFEDF6F7);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      alignment: Alignment.center,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(24.w),
        border: Border.all(
          color: borderColor,
          width: 1.w,
        ),
      ),
      child: Text(text, style: style_1_28_500.copyWith(color: fontColor)),
    );
  }
}
