
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'share-item-little.dart';

/// 图片分享的弹窗封装
class ImageDialog extends StatefulWidget {
  final List<String> imageUrls;
  /// 底部操作的小图标组件
  final List<String> littleItems;
  final int initialIndex; // 添加初始索引参数

  const ImageDialog({super.key, required this.imageUrls, required this.littleItems, this.initialIndex = 0});

  @override
  State<ImageDialog> createState() => _ImageDialogState();
}

class _ImageDialogState extends State<ImageDialog> {
  late PageController _pageController;
  late int _currentPage;

    // 添加一个方法来计算缩放值
  double _getScaleValue(int index) {
    if (_currentPage == index) return 1.0;
    return 0.85; // 非当前页面的初始缩放值
  }

  @override
  void initState() {
    super.initState();
    _currentPage = widget.initialIndex;
    _pageController = PageController(
      initialPage: widget.initialIndex,
      viewportFraction: 0.75,
    );
  }

    @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height,
      child: Stack(
        children: [
          Positioned.fill(
            top: 200.w,
            bottom: 400.w,
            child: PageView.builder(
              controller: _pageController,
              padEnds: true, // 允许在两端添加额外空间
              clipBehavior: Clip.none, // 允许超出边界
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
              },
              itemCount: widget.imageUrls.length,
              itemBuilder: (context, index) => AnimatedBuilder(
                animation: _pageController,
                builder: (context, child) {
                  double value = 1.0;
                    
                  // 如果 controller 已经附加到 position，使用滚动位置计算缩放
                  if (_pageController.position.haveDimensions) {
                    value = _pageController.page! - index;
                    value = (1 - (value.abs() * 0.15)).clamp(0.85, 1.0);
                  } else {
                    // 初始状态，使用 _currentPage 计算缩放
                    value = _getScaleValue(index);
                  }
                  
                  return Center(
                    child: Transform.scale(
                        scale: value,
                        child: Container(
                          margin: EdgeInsets.symmetric(horizontal: 5.w),
                          // height: MediaQuery.of(context).size.height * 0.8,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(16.w),
                            child: SingleChildScrollView(
                                physics: const ClampingScrollPhysics(), // 使用 ClampingScrollPhysics 提供更好的滚动体验
                                child: Image.network(
                                  widget.imageUrls[index],
                                  fit: BoxFit.contain,
                                  loadingBuilder: (context, child, loadingProgress) {
                                    if (loadingProgress == null) return child;
                                    return Center(
                                      child: CircularProgressIndicator(
                                        value: loadingProgress.expectedTotalBytes != null
                                            ? loadingProgress.cumulativeBytesLoaded /
                                                loadingProgress.expectedTotalBytes!
                                            : null,
                                        color: Colors.white,
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ),
                        ),
                  );
                },
              ),
            ),
          ),
          // 关闭按钮
          Positioned(
            top: 120.w,
            right: 20.w,
            child: Row(
              children: [
                Text('分享', style: style_1_32_400),
                IconButton(
                  icon: Icon(
                    Icons.close,
                    color: Colors.black,
                    size: 48.w,
                  ),
                  onPressed: () => Navigator.pop(context),
                )
              ],
            ),
          ),

          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).padding.bottom + 40.w,
                top: 48.w,
                left: 40.w,
                right: 40.w,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(32.w),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('分享至', style: style_1_32_600),
                  SizedBox(height: 32.w),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children:
                        widget.littleItems.map((item) => ShareItemLittleView(
                          label: item,
                          imageUrl: widget.imageUrls[_currentPage],
                        )).toList(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
