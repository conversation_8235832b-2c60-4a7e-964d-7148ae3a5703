import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'utils.dart';

/// 分享时, 底部操作的小图标组件
class ShareItemLittleView extends StatelessWidget {
  final String label;
  final String imageUrl;
  const ShareItemLittleView({super.key, required this.label, required this.imageUrl});

  /// 通过 label 获取对应的 SHARE_ITEM_LIST 中的 item
  getShareItemLittle(String label) {
    return SHARE_ITEM_LIST.firstWhere((item) => item["label"] == label);
  }

  @override
  Widget build(BuildContext context) {
    final item = getShareItemLittle(label);
    return GestureDetector(
      onTap: () => {
        if (item["label"] != null) {
          item["onTap"]?.call(imageUrl),
        }
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            item["icon"],
            width: 80.w,
            height: 80.w,
          ),
          SizedBox(height: 16.w),
          Text(
            label,
            style: TextStyle(
              fontSize: 24.w,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }
}