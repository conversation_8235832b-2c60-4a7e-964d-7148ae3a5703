import 'dart:io';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/utils/wx_sdk.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';

/// 分享的功能列表
final SHARE_ITEM_LIST = [
  {
    "label": "保存到本地",
    "icon": "images/保存到本地.png",
    "onTap": saveImageToLocalGallery,
  },
  {
    "label": "微信好友",
    "icon": "images/微信好友.png",
    "onTap": shareToWeChatFriend,
  },
  {
    "label": "朋友圈",
    "icon": "images/微信朋友圈.png",
    "onTap": shareToWeChatMoments,
  },
  // {
  //   "label": "小红书",
  //   "icon": "images/小红书.png",
  //   "onTap": () {},
  // },
  // {
  //   "label": "QQ好友",
  //   "icon": "images/QQ好友.png",
  //   "onTap": () {},
  // },
];

/// 读取图片(网络｜本地)成字节数据
Future<Uint8List> readImageToBytes(String imageUrl) async {
  // 如果是本地图片, 读取本地图片
  if (imageUrl.startsWith('file://')) {
    return File(imageUrl).readAsBytesSync();
  }
  final response = await Dio().get(imageUrl, options: Options(responseType: ResponseType.bytes));
  return response.data;
}

/// 将图片字节数据保存到临时文件
Future<String> saveImageByBytesToTempFile(Uint8List bytes) async {
  final tempDir = await getTemporaryDirectory();
  final tempFile = File('${tempDir.path}/image_${DateTime.now().millisecondsSinceEpoch}.png');
  await tempFile.writeAsBytes(bytes);
  return tempFile.path;
}

/// 保存图片到本地相册
void saveImageToLocalGallery(String imageUrl) async {
  final bytes = await readImageToBytes(imageUrl);
  // 保存图片到相册
  final result = await ImageGallerySaver.saveImage(
    bytes,
    quality: 100, // 图片质量
    name: 'image_${DateTime.now().millisecondsSinceEpoch}', // 图片名称
  );

  if (result['isSuccess']) {
    Fluttertoast.showToast(msg: '保存成功', gravity: ToastGravity.CENTER);
  } else {
    Fluttertoast.showToast(msg: '保存失败', gravity: ToastGravity.CENTER);
  }
}


  /// 捕获截图并保存到本地
  Future<String> captureAndSaveScreenshot(ScreenshotController screenshotController, String scene) async {
    // 捕获截图
    final Uint8List? imageBytes = await screenshotController.capture(
      pixelRatio: 3.0,
    );

    // 保存临时文件到本地
    final tempFilePath = await saveImageByBytesToTempFile(imageBytes!);

    // 上传图片获取网络地址
    var uploadResult = await Api.uploadFile(tempFilePath,
        topicCode: '', scene: scene);

    final String imageUrl = uploadResult.data!.fileUrl!;

    return imageUrl;
  }

/// 分享到微信好友
void shareToWeChatFriend(String imageUrl) async {
  final bytes = await readImageToBytes(imageUrl);
  final path = await saveImageByBytesToTempFile(bytes);
  WXSDK.shareImageToWeChatFriend(path);
}

/// 分享到微信朋友圈
void shareToWeChatMoments(String imageUrl) async {
  final bytes = await readImageToBytes(imageUrl);
  final path = await saveImageByBytesToTempFile(bytes);
  WXSDK.shareImageToWeChatMoments(path);
}
