// 分享组件
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'image-dialog.dart';
import 'utils.dart';

class ShareImageWidget extends StatelessWidget {
  final Color iconColor;
  final Future<List<String>> Function()? getImageFunc;

  const ShareImageWidget({super.key, Color? iconColor, Future<List<String>> Function()? getImageFunc}): 
    iconColor = iconColor ?? const Color(0xff000000),
    getImageFunc = getImageFunc;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _onTap(context),
      child: Image.asset(
        "images/share.png",
        width: 44.w,
        height: 40.w,
        color: iconColor,
      ),
    );
  }

  /// 点击分享时, 显示 loading 调用 getImageFunc 获取图片
  Future<void> _onTap(BuildContext context) async {
    LogUtil.d("getImageFunc: user click");
    LogUtil.d("getImageFunc: ${getImageFunc?.call()}");

    // 保存 context 的引用
    final currentContext = context;

    showDialog(
      context: currentContext,
      builder: (context) => const Center(
        child: CircularProgressIndicator(
          color: Colors.grey,
      )),
    );

    final List<String>? imageUrls = await getImageFunc?.call();

    // 检查 Widget 是否还在树中
    if (!currentContext.mounted) return;

    // 关闭 loading
    Navigator.pop(currentContext);

    if (imageUrls != null && imageUrls.isNotEmpty) {
      // 弹窗分享
      showModalBottomSheet(
        context: currentContext,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => Container(
          decoration: const BoxDecoration(
          color: Color(0xFFEDF6F7),
          // 添加背景图
          image: DecorationImage(
            image: AssetImage("images/scene_introduce_bg.png"), // 替换为你的背景图路径
            fit: BoxFit.contain,
            opacity: 1, // 设置背景图透明度
            // 背景图在最上方
            alignment: Alignment.topCenter,
          ),
        ),
          child: ImageDialog(
            imageUrls: imageUrls,
            littleItems: SHARE_ITEM_LIST.map((item) => item["label"] as String).toList(),
          ),
        ),
      );
    }
  }
}