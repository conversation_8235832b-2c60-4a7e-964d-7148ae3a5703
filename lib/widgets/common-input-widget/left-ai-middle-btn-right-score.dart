import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


/// 常见的跟读输入框组件, 左边是AI发音, 中间是跟读按钮, 右边是评分
/// icon(AI发音) 按钮(跟读按钮) 评分(我的发音)
class LeftAiMiddleBtnRightScore extends StatelessWidget {
  
  final String aiUrl;
  final String userUrl;
  final String score;
  final Function() btnOnTap;
  final String btnText;

  const LeftAiMiddleBtnRightScore({super.key, 
    required this.aiUrl, 
    required this.btnOnTap,
    String? score,
    String? userUrl,
    String? btnText
  }) : 
       userUrl = userUrl ?? "",
       btnText = btnText ?? "再读一遍",
       score = score ?? "";

  Color getColor(int score) {
    if (score > 0 && score <= 60) {
      return const Color.fromARGB(255, 249, 50, 0);
    } else if (score <= 80) {
      return const Color(0xFF061B1F);
    } else if (score <= 100) {
      return const Color.fromARGB(255, 42, 201, 92);
    }

    return const Color(0xFF061B1F);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
            onTap: () async {
              if (CommonUtils.url == aiUrl && CommonUtils.isPlaying == true) {
                CommonUtils.stopPlay();
              } else {
                CommonUtils.playVideo(aiUrl);
              }
            },
            child: StreamBuilder<PlayState>(
                stream: CommonUtils.playStatus,
                builder: (context, snapshot) {
                  final assertName = (CommonUtils.url == aiUrl && CommonUtils.isPlaying == true) ? 
                    "images/btn_pause.png" : "images/bt_play.png";
                  return Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image(
                          image: AssetImage(assertName),
                          width: 80.w,
                          height: 80.w,
                        ),
                        SizedBox(height: 4.w),
                        Text(
                          "AI发音",
                          style: style_1_24,
                        ),
                      ],
                    ); 
                })),
        SizedBox(
          width: 40.w,
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              btnOnTap();
            },
            child: Container(
              height: 88.w,
              width: double.infinity,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.w),
                  color: const Color(0xFF2693FF)),
              child: Center(
                child: Text(
                  btnText,
                  style: style_1_28.copyWith(color: Colors.white),
                ),
              ),
            ),
          ),
        ),
        SizedBox(
          width: 40.w,
        ),
        Column(
          children: [
            GestureDetector(
              onTap: () {
                if (userUrl != null) {
                  CommonUtils.playVideo(userUrl);
                }
              },
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(40.w), color: btnColor),
                width: 80.w,
                height: 80.w,
                child: Center(
                    child: Text(
                  score.isNotEmpty ? score : "-",
                  style:
                      style_1_28.copyWith(color: getColor(int.parse(score.isNotEmpty ? score : "0"))),
                )),
              ),
            ),
            SizedBox(height: 4.w),
            Text(
              "我的发音",
              style: style_1_24,
            ),
          ],
        ),
      ],
    );
  
  }
}