import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';
import 'package:path/path.dart';

class PlayerContainer extends StatefulWidget {
  final Widget child;
  const PlayerContainer({super.key, required this.child});

  @override
  State<PlayerContainer> createState() => PlayerContainerState();
}

class PlayerContainerState extends State<PlayerContainer> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Stack(
        children: [
          VlcPlayer(
              controller: CommonUtils.videoPlayerController, aspectRatio: 1000),
          widget.child
        ],
      ),
    );
  }

  @override
  void dispose() async {
    super.dispose();
  }
}
