import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/http/models/check_udpate_model/check_udpate_model.dart';
import 'package:flutter_app_kouyu/my_app.dart';
import 'package:url_launcher/url_launcher.dart';

class AppUpdateWidget extends StatelessWidget {
  final void Function() close;
  final CheckUdpateModel checkUdpateModel;
  const AppUpdateWidget(
      {super.key, required this.close, required this.checkUdpateModel});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0x80000000),
      padding: const EdgeInsets.fromLTRB(12, 24, 12, 40),
      child: Column(
        children: [
          const Spacer(),
          Stack(
            children: [
              Container(
                margin: const EdgeInsets.only(top: 45),
                decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(
                      Radius.circular(20.0),
                    )),
                child: Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                      color: Colors.white,
                      gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [Color(0xFF98E2FF), Color(0x0098E2FF)]),
                      borderRadius: BorderRadius.all(
                        Radius.circular(20.0),
                      )),
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(20, 24, 20, 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        const Text(
                          '发现新版本',
                          style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w900,
                              color: Color(0xff061B1F)),
                        ),
                        Text(
                          checkUdpateModel.data?.latestVersion ?? "_",
                          style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: Color(0xff646E70)),
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        const Text(
                          "更新内容",
                          style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w500,
                              color: Color(0xff061B1F)),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Text(
                          checkUdpateModel.data?.updateContent ?? "_",
                          style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: Color(0xff646E70)),
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        Container(
                          height: 48,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            gradient: const LinearGradient(
                              begin: Alignment.centerRight,
                              end: Alignment.centerLeft,
                              colors: [
                                Color(0xFF98ECEC),
                                Color(0xFF84E9FF),
                                Color(0xFF9BE1FF)
                              ],
                            ),
                          ),
                          child: TextButton(
                              onPressed: () {
                                close();
                                gotoAppstore(
                                    checkUdpateModel.data?.appDownloadUrl ??
                                        "");
                              },
                              child: const Center(
                                child: Text(
                                  "立即体验",
                                  style: TextStyle(
                                      color: Color(0xff061B1F),
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500),
                                ),
                              )),
                        ),
                        Visibility(
                          visible: checkUdpateModel.data?.forceUpdate == false,
                          child: SizedBox(
                            height: 48,
                            child: TextButton(
                                onPressed: () {
                                  close();
                                },
                                child: const Center(
                                  child: Text(
                                    "暂不更新",
                                    style: TextStyle(
                                        color: Color(0xff646E70),
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500),
                                  ),
                                )),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                  right: -12,
                  top: 0,
                  child: Image.asset(
                    "images/update_arrow.png",
                    width: 148,
                  ))
            ],
          ),
        ],
      ),
    );
  }

  gotoAppstore(String url) {
    launchUrl(Uri.parse(url));
  }
}
