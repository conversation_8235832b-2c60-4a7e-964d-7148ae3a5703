import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// 更多设置组件, 点击会在旁边出现一个弹窗, 弹窗中可以设置不同的功能

// 功能列表类
class MoreSettingItem {
  // 本地图片
  final String? iconImage;
  // 图标
  final IconData? icon;
  // 图标和文案颜色
  final Color color;

  final String title;
  final Function() onTap;

  MoreSettingItem(
      {this.iconImage,
      this.icon,
      required this.title,
      required this.onTap,
      this.color = const Color(0xff000000)});
}

class MoreSettingWidget extends StatelessWidget {
  final double size;
  final Color iconColor;
  final List<MoreSettingItem> items;

  const MoreSettingWidget(
      {super.key,
      this.size = 24,
      this.iconColor = const Color(0xff000000),
      required this.items});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () => {_showSettingAlert(context, items)},
        child: Icon(Icons.more_horiz, size: size, color: iconColor));
  }
}

_showSettingAlert(BuildContext context, List<MoreSettingItem> items) {
  showDialog(
      context: context,
      barrierColor: Colors.transparent,
      builder: (context) {
        return GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Container(
                color: Colors.transparent,
                child: Align(
                    alignment: Alignment.topRight,
                    child: Container(
                        margin: EdgeInsets.only(right: 40.w, top: 100.w),
                        padding: EdgeInsets.only(left: 24.w, right: 24.w),
                        width: 220.w,
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(32.w)),
                        child: Column(
                            mainAxisSize: MainAxisSize.min, // 使用最小高度
                            children: items
                                .asMap()
                                .entries
                                .map((item) => GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () => {
                                        Navigator.pop(context),
                                        item.value.onTap()
                                      },
                                      child: Container(
                                        height: 88.w,
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                          border: Border(
                                            // 最后一个元素没有下边框
                                            bottom: item.key == items.length - 1
                                                ? BorderSide.none
                                                : const BorderSide(
                                                    color: Color.fromRGBO(
                                                        0, 0, 0, 0.1),
                                                  ),
                                          ),
                                        ),
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            const Spacer(),
                                            item.value.iconImage != null
                                                ? Image.asset(
                                                    item.value.iconImage!,
                                                    width: 36.w,
                                                    height: 36.w,
                                                  )
                                                : Icon(
                                                    item.value.icon!,
                                                    size: 36.w,
                                                    color: item.value.color,
                                                  ),
                                            SizedBox(
                                              width: 24.w,
                                            ),
                                            Text(
                                              item.value.title,
                                              style: TextStyle(
                                                fontSize: 28.w,
                                                color: item.value.color,
                                              ),
                                            ),
                                            const Spacer(),
                                          ],
                                        ),
                                      ),
                                    ))
                                .toList() // 确保这里是一个 List<Widget>
                            )))));
      });
}
