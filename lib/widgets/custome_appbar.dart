import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomAppbar {
  static Widget leftWidget(BuildContext context,
      {required String text, void Function()? ontap}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(
          width: 17,
        ),
        GestureDetector(
          onTap: () {
            if (ontap != null) {
              ontap();
            } else {
              Navigator.pop(context);
            }
          },
          child: Image.asset(
            "images/navigation_back.png",
            width: 24,
            height: 24,
          ),
        ),
        SizedBox(width: 10.w),
        Text(
          text,
          style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 18,
              color: Color(0xFF061B1F)),
        ),
      ],
    );
  }

  static Widget leftWidgetWithNoIcon(BuildContext context,
      {required String text}) {
    return Row(
      children: [
        const SizedBox(
          width: 17,
        ),
        Text(
          text,
          style: const TextStyle(
              fontWeight: FontWeight.w800,
              fontSize: 20,
              color: Color(0xFF061B1F)),
        ),
      ],
    );
  }

  static Widget leftWhiteWidget(BuildContext context,
      {required String text, void Function()? ontap}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(
          width: 17,
        ),
        GestureDetector(
          onTap: () {
            if (ontap != null) {
              ontap();
            } else {
              Navigator.pop(context);
            }
          },
          child: Image.asset(
            "images/navigation_back_white.png",
            width: 24,
            height: 24,
          ),
        ),
        Text(
          text,
          style: const TextStyle(
              fontWeight: FontWeight.w800, fontSize: 18, color: Colors.white),
        ),
      ],
    );
  }

  static Widget leftCloseWidget(BuildContext context,
      {void Function()? ontap}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 40.w,
        ),
        GestureDetector(
          onTap: () {
            if (ontap != null) {
              ontap();
            } else {
              Navigator.pop(context);
            }
          },
          child: Image.asset(
            "images/close_black.png",
            width: 24.w,
            height: 24.w,
          ),
        ),
      ],
    );
  }
}
