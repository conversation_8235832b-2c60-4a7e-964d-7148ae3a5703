import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebviewWidget extends StatefulWidget {
  const WebviewWidget({super.key, required this.url});
  final String url;

  @override
  State<WebviewWidget> createState() => _WebviewWidgetState();
}

class _WebviewWidgetState extends State<WebviewWidget> {
  late final WebViewController _controller;
  @override
  void initState() {
    _controller = WebViewController()..loadRequest(Uri.parse(widget.url));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leadingWidth: 300,
        backgroundColor: const Color(0xFFF3FBFD),
        leading: CustomAppbar.leftWidget(context, text: ""),
      ),
      backgroundColor: const Color(0xFFF3FBFD),
      body: WebViewWidget(controller: _controller),
    );
  }
}
