import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/loading_status.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_hint_model/chat_hint_model.dart';
import 'package:flutter_app_kouyu/common/http/models/collect_sentence.dart';
import 'package:flutter_app_kouyu/common/http/models/custome_scene_model/get_share_image_model.dart';
import 'package:flutter_app_kouyu/common/http/models/en_to_ch_model.dart';
import 'package:flutter_app_kouyu/common/http/models/audio_upload_model/audio_upload_model.dart';
import 'package:flutter_app_kouyu/common/http/models/explore_chat_model/explore_chat_model.dart';
import 'package:flutter_app_kouyu/common/http/request_error.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/common/utils/log.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';
import 'package:flutter_app_kouyu/modules/find/pages/explore_talk_page.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../common/http/models/chat_hint_model/data.dart';

enum ScrollowDirection { top, bottom }

class ExploreChatProvider extends ChangeNotifier implements VoiceEvent {
  final FreeTalkRepository freeTalkRepository = FreeTalkRepository();
  final ExploreTalkArgument argument;
  final BuildContext context;
  final List<ExploreTalkModel> list = [];
  ScrollowDirection scrollowDirection = ScrollowDirection.top;
  final ScrollController scrollController = ScrollController();

  LoadingStatus? chatHintStatus;
  List<Data> chatHintList = [];
  int totalQuestionCount = 0;
  int currentQuestionIndex = 0;
  bool finished = false;
  @override
  dispose() {
    scrollController.dispose();
    super.dispose();
  }

  ExploreChatProvider(
    this.context, {
    required this.argument,
  }) {
    // scrollController.addListener(() {
    //   if (scrollController.position.pixels > 0) {
    //     // 向下滚动

    //     if (scrollowDirection != ScrollowDirection.bottom) {
    //       scrollowDirection = ScrollowDirection.bottom;
    //       notifyListeners();
    //     }
    //   } else if (scrollController.offset == 0) {
    //     // 滚动到顶部
    //   } else {
    //     // 向上滚动
    //     if (scrollowDirection != ScrollowDirection.top) {
    //       scrollowDirection = ScrollowDirection.top;
    //       notifyListeners();
    //     }
    //   }
    // });
  }

  Future<void> intiGreetingStream() async {
    //取最新的model
    int index = list.length;
    ExploreTalkModel initModel = ExploreTalkModel(
        type: 0, replyStatus: LoadingState(status: LoadingStatus.inProgress));
    list.add(initModel);
    notifyListeners();
    try {
      ExploreChatModel greetingModel = await Api.exploreChat(
          argument.topicCode, argument.topicData.exploreType ?? "", null, null);
      ExploreTalkModel model = ExploreTalkModel(
          type: 0,
          conversationId: greetingModel.data?.conversationId,
          answer: greetingModel.data?.reply,
          audioUrl: greetingModel.data?.audioUrl,
          showTranslateText: true,
          replyStatus: const LoadingState(status: LoadingStatus.success));
      list.clear();
      list.add(model);
      for (String text in greetingModel.data?.userQuickReply ?? []) {
        ExploreTalkModel model = ExploreTalkModel(
            type: 2,
            conversationId: greetingModel.data?.conversationId,
            answer: text,
            showTranslateText: false,
            replyStatus: const LoadingState(status: LoadingStatus.success));
        list.add(model);
      }
      notifyListeners();
      CommonUtils.playVideo(model.audioUrl ?? "");
    } catch (e) {
      initModel = ExploreTalkModel(
          type: 0, replyStatus: LoadingState(status: LoadingStatus.failure));
      list[index] = initModel;
      notifyListeners();
    }
  }

  //不显示提示
  cleanHit() {
    chatHintList.clear();
    chatHintStatus = null;
    notifyListeners();
  }

  chat(String? question, String filePath) async {
    Log.d("准备 发送语音");
    if (question == null || question.isEmpty) {
      return;
    }
    // AudioUploadModel value = await uploadVoice(filePath);
    //添加问话
    ExploreTalkModel questionModel = ExploreTalkModel(
        type: 1,
        conversationId: list.last.conversationId,
        // audioUrl: value.data?.fileUrl,
        answer: question);
    if (questionModel.conversationId == null) {
      return;
    }
    //移除提示对话
    list.removeWhere((element) => element.type == 2);
    cleanHit();
    notifyListeners();
    list.add(questionModel);
    //获取对话
    sendChat(questionModel);
  }

  Future<void> sendChat(
    ExploreTalkModel question,
  ) async {
    //取最新的model
    int index = list.length;
    ExploreTalkModel initModel = ExploreTalkModel(
        type: 0,
        replyStatus: const LoadingState(status: LoadingStatus.inProgress));
    list.add(initModel);
    notifyListeners();
    try {
      ExploreChatModel greetingModel = await Api.exploreChat(
          argument.topicCode,
          argument.topicData.exploreType ?? "",
          question.answer,
          question.conversationId);
      ExploreTalkModel model = ExploreTalkModel(
          type: 0,
          conversationId: greetingModel.data?.conversationId,
          answer: greetingModel.data?.reply,
          audioUrl: greetingModel.data?.audioUrl,
          showTranslateText: true,
          replyStatus: const LoadingState(status: LoadingStatus.success));
      list[index] = model;
      for (String text in greetingModel.data?.userQuickReply ?? []) {
        ExploreTalkModel model = ExploreTalkModel(
            type: 2,
            conversationId: greetingModel.data?.conversationId,
            answer: text,
            showTranslateText: false,
            replyStatus: const LoadingState(status: LoadingStatus.success));
        list.add(model);
      }
      notifyListeners();
      CommonUtils.playVideo(model.audioUrl ?? "");
    } catch (e) {
      initModel = ExploreTalkModel(
          type: 0,
          replyStatus: const LoadingState(status: LoadingStatus.failure));
      list[index] = initModel;
      notifyListeners();
    }
  }

  Future<AudioUploadModel> uploadVoice(String filePath) async {
    return freeTalkRepository.upload(filePath,
        topicCode: argument.topicCode,
        sence: argument.topicData.exploreType ?? "");
  }

  void translate(ExploreTalkModel model, int index) async {
    model.translateStatus =
        const LoadingState(status: LoadingStatus.inProgress);
    notifyListeners();

    freeTalkRepository.translate(model.answer).then((value) {
      model.translateText = value.data;
      model.translateStatus = const LoadingState(status: LoadingStatus.success);

      notifyListeners();
    }).catchError((_) {
      model.translateStatus = const LoadingState(status: LoadingStatus.success);
      notifyListeners();
    });
  }

  void collection(ExploreTalkModel model, int index) async {
    if (model.collected == true) {
      await freeTalkRepository.cancelCollectionSentence(model.answer);
      model.collected = false;
      notifyListeners();
      return;
    } else {
      await freeTalkRepository.collectionSentence(model.answer, "explore", "0");
      model.collected = true;
      notifyListeners();
      return;
    }
  }

  void collectionHitModel(Data data) async {
    if (data.collected == true) {
      data.collected = false;
      notifyListeners();
      return;
    }
    await freeTalkRepository.collectionSentence(data.english, "explore", "0");
    data.collected = true;
    notifyListeners();
  }

  void update<T>(int index, Future<T> Function() request) async {
    //取最新的model
    if (T == EnToCnModel) {
      list[index].translateStatus =
          const LoadingState(status: LoadingStatus.inProgress);
    } else if (T == CollectionSentence) {
      list[index].collected = true;
    } else {
      return;
    }
    notifyListeners();
    try {
      T result = await request();
      //取最新的model
      if (T == EnToCnModel) {
        list[index].translateStatus =
            const LoadingState(status: LoadingStatus.success);
        list[index].translateText = (result as EnToCnModel).data;
      } else {
        return;
      }
      notifyListeners();
    } catch (e) {
      //取最新的model
      String? errorMessage;

      if (e is RequestError) {
        errorMessage = e.message;
        if ((e).code == "0019" || (e).code == "0020") {
          //中文不能进行语音评测 不支持中文语法检测
          errorMessage = null;
        }
      }

      if (T == EnToCnModel) {
        list[index].translateStatus =
            LoadingState(status: LoadingStatus.failure, message: errorMessage);
      } else {
        return;
      }
      notifyListeners();
    }
  }

  void getChatHint() async {
    String? text = list.last.answer;
    if (text == null || text.isEmpty) {
      return;
    }
    if (chatHintList.isNotEmpty) {
      return;
    }
    chatHintStatus = LoadingStatus.inProgress;
    notifyListeners();
    try {
      ChatHintModel result = await freeTalkRepository.generateNextSentence("", "explore", list.last.conversationId?.toInt() ?? 0);
      chatHintList = result.data ?? [];
      chatHintStatus = LoadingStatus.success;
      notifyListeners();
    } catch (_) {
      chatHintStatus = LoadingStatus.failure;
      notifyListeners();
    }
  }

  void playVideo(String? url) async {
    CommonUtils.playVideo(url ?? "");
  }

  /// 获取用户分享的图片 url
  Future<List<String>> getShareImageUrls() async {
    GetShareImageModel result = await Api.getShareImageList(list.last.conversationId ?? 0, "explore");
    if (result.data != null) {
      return result.data!.map((e) => e.poster_url ?? "").toList();
    }
    return [];
  }

  showFinishTaskDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      routeSettings: const RouteSettings(arguments: {"stopPlay": false}),
      builder: (context) {
        Future.delayed(const Duration(milliseconds: 1000), () {
          Navigator.of(context).pop();
        });
        return GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: Container(
            width: double.infinity,
            height: double.infinity,
            padding: EdgeInsets.only(top: 200.w),
            child: Align(
              alignment: Alignment.topCenter,
              child: Image.asset(
                "images/finish_task_alert.png",
                width: 397.w,
                fit: BoxFit.fitWidth,
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  recordFinish(String? question, String filePath) {
    chat(question, filePath);
  }
}

class ExploreTalkModel {
  final String? question;
  final String? answer;

  String? audioUrl;
  String? translateText;
  bool showTranslateText;
  //0 question  1  reply 2 leftHit
  final int type;
  final LoadingState? replyStatus;
  LoadingState? translateStatus;
  bool? collected;
  final int? conversationId;

  ExploreTalkModel(
      {required this.type,
      this.question,
      this.answer,
      this.audioUrl,
      this.translateText,
      this.translateStatus,
      this.collected,
      this.replyStatus,
      this.conversationId,
      this.showTranslateText = false});
}
