// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_try_times_model/tool_user_try_times_model.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_translate_page.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ToolTranslatePageProvider extends ChangeNotifier {
  ToolUserTryTimesModel? toolUserTryTimesModel;
  RefreshController refreshController = RefreshController();
  bool isVip = false;

  /// 翻译模式
  TranslateModeEnum translateMode = TranslateModeEnum.chineseToEnglish;

  /// 翻译内容
  String? translateContent = '';

  /// 输入框内容
  String inputContent = '';

  /// 是否正在请求
  bool isRequesting = false;

  loadData() async {
    try {
      toolUserTryTimesModel = await Api.getUserToolTryTimes();
      await checkIsVip();
    } catch (e) {
      refreshController.refreshFailed();
    } finally {
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }

  setInputContent(String value) {
    inputContent = value;
    notifyListeners();
  }

  checkIsVip() async {
    final userInfo = await LoginUtil.userInfoModel();
    isVip = userInfo?.data?.memberType == 1;
  }

  switchTranslateMode() {
    if (translateMode == TranslateModeEnum.chineseToEnglish) {
      translateMode = TranslateModeEnum.englishToChinese;
    } else {
      translateMode = TranslateModeEnum.chineseToEnglish;
    }
    notifyListeners();
  }

  translate() async {
    // 非会员, 剩余次数不足, 弹出提示
    if (!isVip &&
        (toolUserTryTimesModel?.data?.toolTryTimesTranslated ?? 0) <= 0) {
      EasyLoading.showToast('剩余次数不足');
      return;
    }

    if (isRequesting) return;
    isRequesting = true;

    // 显示toast, 翻译中
    EasyLoading.show(status: '翻译中...');

    final from =
        translateMode == TranslateModeEnum.chineseToEnglish ? 'cn' : 'en';
    final to =
        translateMode == TranslateModeEnum.chineseToEnglish ? 'en' : 'cn';

    try {
      final result = await Api.translate(inputContent, from, to);
      
      // 检查结果是否有效
      if (result == false) {
        // API调用失败
        EasyLoading.showToast('翻译失败，请稍后再试');
        translateContent = '';
      } else {
        // 确保数据不为空
        translateContent = result.data ?? '';
        if (translateContent!.isEmpty) {
          EasyLoading.showToast('没有翻译结果');
        }
      }
    } catch (e) {
      // 处理异常
      EasyLoading.showToast('翻译出错：${e.toString()}');
      translateContent = '';
    } finally {
      isRequesting = false;
      // 隐藏toast
      EasyLoading.dismiss();
      notifyListeners();
    }
  }

  // 进入会员购买页面
  goVipPage(BuildContext context) {
    Navigator.of(context).pushNamed('/open_vip');
  }
}
