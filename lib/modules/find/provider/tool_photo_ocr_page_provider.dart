// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:camera/camera.dart';

class ToolPhotoOcrPageProvider extends ChangeNotifier {
  RefreshController refreshController = RefreshController();

  List<CameraDescription> cameras = [];

  /// 定义一个 cameraController
  late CameraController? cameraController;
  late CameraController? cameraControllerTest;

  /// 需要回传的参数
  String cbParam = '';

  /// 拍照或者从相册选择的临时图片文件
  String tempPictureFile = '';

  /// 是否正在ocr
  bool ocring = false;

  /// 识别结果
  String ocrResult = '';

  /// 初始化控制器的 Future
  late Future<void> initializeControllerFuture;

  final ImagePicker picker = ImagePicker();

  ToolPhotoOcrPageProvider({this.cbParam = ''});

  // initState() async {
  //   cameras = await availableCameras();
  //   cameraController = CameraController(cameras[0], ResolutionPreset.high);
  //   initializeControllerFuture =
  //       cameraController!.initialize(); //初始化是异步的所以用一个Future来赋值
  //   notifyListeners();
  // }

  initCamera() async {
    try {
      cameras = await availableCameras();
    } on CameraException catch (e) {
      print('获取相机设备失败：$e');
    }

    cameraControllerTest =
        CameraController(cameras[0], ResolutionPreset.medium);

    try {
      await cameraControllerTest!.initialize();
    } on CameraException catch (e) {
      print('初始化相机失败：$e');
    }
  }

  // @override
  dispose() {
    // print("销毁==");
    cameraControllerTest!.dispose();
    // super.dispose();
  }

  /// 请求相机权限
  Future<void> askCameraPermission() async {
    // 请求权限
    var status = await Permission.camera.request();
    print(status);
    if (!status.isGranted) {
      // EasyLoading.showToast('请打开相机权限', duration: Duration(seconds: 15));
      // await Permission.camera.request();
    }
  }

  // 捕获图片
  Future<void> captureImage() async {
    if (cameraControllerTest == null ||
        !cameraControllerTest!.value.isInitialized) {
      return;
    }
    if (cameraControllerTest!.value.isTakingPicture) {
      return;
    }
    try {
      // await _askCameraPermission();
      var result = await cameraControllerTest!.takePicture();
      tempPictureFile = result!.path;
      notifyListeners();
    } catch (e) {
      print(e);
    }

    // XFile? pickedFile =
    //     await picker.pickImage(source: ImageSource.camera, imageQuality: 50);

    // if (pickedFile != null) {
    //   tempPictureFile = pickedFile!.path;
    //   notifyListeners();
    // }
  }

  // 从相册选择图片
  Future<void> pickImage() async {
    // final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    if (image == null) {
      return;
    }
    tempPictureFile = image.path;
    notifyListeners();
  }

  /// 重置图片
  resetImage() {
    tempPictureFile = '';
    notifyListeners();
  }

  ocr(BuildContext context) async {
    if (ocring || tempPictureFile.isEmpty) {
      return;
    }
    EasyLoading.showToast('正在识别', duration: Duration(seconds: 15));

    ocring = true;

    // 上传图片获取网络地址
    var uploadResult = await Api.uploadFile(tempPictureFile,
        topicCode: '', scene: 'writing_correction');

    var result =
        await Api.ocr(uploadResult.data!.fileUrl! + "?imageMogr2/format/jpeg");
    // var result = await Api.ocr("https://cos.xinquai.com/writing_correction/000/20241221/718/user/7181734774572.8960633image_picker_A1BD455B-F6FD-4677-9CAA-28892EE05FF9-28789-0000047BF88218AB.jpg?imageMogr2/format/jpeg");
    ocrResult = result.data ?? '';

    ocring = false;
    EasyLoading.dismiss();

    // 返回上一页面, 带上识别结果和回调参数
    Navigator.pop(context, {'result': ocrResult, 'cbParam': cbParam});
  }
}
