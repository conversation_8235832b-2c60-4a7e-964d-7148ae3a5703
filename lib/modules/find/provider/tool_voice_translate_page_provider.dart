// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_try_times_model/tool_user_try_times_model.dart';
import 'package:flutter_app_kouyu/common/provider/voice_model.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_translate_page.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ToolVoiceTranslatePageProvider extends VoiceModel {
  ToolUserTryTimesModel? toolUserTryTimesModel;
  RefreshController refreshController = RefreshController();
  bool isVip = false;

  /// 翻译模式
  TranslateModeEnum translateMode = TranslateModeEnum.chineseToEnglish;

  /// 语音识别结果
  String sttResult = '';
  String lastRequestContent = '';

  /// 翻译内容
  String translateContent = '';

  /// 翻译内容经过分割后的数组
  List<String> translateContentTextArr = [];

  /// 是否正在翻译
  bool translating = false;
  Timer? translateTimer;

  /// 是否正在录音
  bool recording = false;

  loadData() async {
    try {
      toolUserTryTimesModel = await Api.getUserToolTryTimes();
      await checkIsVip();
      checkTranslate();
    } catch (e) {
      refreshController.refreshFailed();
    } finally {
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }

  checkIsVip() async {
    final userInfo = await LoginUtil.userInfoModel();
    isVip = userInfo?.data?.memberType == 1;
  }

  switchTranslateMode() {
    if (translateMode == TranslateModeEnum.chineseToEnglish) {
      translateMode = TranslateModeEnum.englishToChinese;
    } else {
      translateMode = TranslateModeEnum.chineseToEnglish;
    }
    notifyListeners();
  }

  /// dart 代码 2s 检查一次是否需要翻译
  checkTranslate() {
    // 每隔2s检查一次是否需要翻译
    translateTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      // print({sttResult, translating});
      if (sttResult.isEmpty) return;
      if (lastRequestContent == sttResult) return;
      if (translating) return;

      translating = true;
      lastRequestContent = sttResult;
      final from =
          translateMode == TranslateModeEnum.chineseToEnglish ? 'cn' : 'en';
      final to =
          translateMode == TranslateModeEnum.chineseToEnglish ? 'en' : 'cn';
      Api.voiceTranslate(sttResult, from, to).then((data) {
        translating = false;
        var dataStr = data.data ?? '';
        if (dataStr.isEmpty) return;

        var spilitMark = ['?', '!', '.', '。', '？', '！'];
        List<String> textArr = [];
        var temp = '';
        for (var i = 0; i < dataStr.length; i++) {
          var chat = dataStr.characters.elementAt(i);
          temp += chat;
          if (spilitMark.contains(chat)) {
            textArr.add(temp);
            temp = '';
          }
        }
        translateContent = dataStr;
        translateContentTextArr = textArr;
        notifyListeners();
      });
    });
  }

  /// 开始录音
  _startRecord(BuildContext context) {
    recording = true;
    translateMode == TranslateModeEnum.chineseToEnglish
        ? speakZh(cb: recordCb)
        : speakEn(cb: recordCb);
  }

  recordCb(String result) {
    /// 如果没有在录音，不处理
    print({recording, result});
    if (!recording) return;
    sttResult = result;
    notifyListeners();
  }

  /// 停止录音
  /// [context] 上下文
  _stopRecord(BuildContext context) async {
    recording = false;
    await stopRecording();
  }

  clickLeftBtn(BuildContext context) {
    recording ? _stopRecord(context) : _startRecord(context);
    notifyListeners();
  }

  clickFinishBtn(BuildContext context) {
    _stopRecord(context);
    translateContent = '';
    sttResult = '';
    translateContentTextArr = [];
    notifyListeners();
  }

  // 进入会员购买页面
  goVipPage(BuildContext context) {
    Navigator.of(context).pushNamed('/open_vip');
  }

  // 退出页面需要释放资源
  @override
  void dispose() {
    translateTimer?.cancel();
    super.dispose();
  }
}
