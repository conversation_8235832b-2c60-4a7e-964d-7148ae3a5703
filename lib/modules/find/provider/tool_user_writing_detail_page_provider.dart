// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_try_times_model/tool_user_try_times_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_writing_detail_model/tool_user_writing_detail_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_writing_page_model/tool_user_writing_page_model.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ToolUserWritingDetailPageProvider extends ChangeNotifier {
  ToolUserWritingDetailModel? toolUserWritingDetailModel;
  RefreshController refreshController = RefreshController();
  late int id;

  // ToolUserWritingDetailPageProvider() {
  //   loadData();
  //   // notifyListeners();
  // }

  loadData(id) async {
    try {
      toolUserWritingDetailModel =
          await Api.getUserCompositionAssessmentDetail(id);
    } catch (e) {
      refreshController.refreshFailed();
    } finally {
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }
}
