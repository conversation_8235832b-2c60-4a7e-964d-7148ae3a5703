// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_try_times_model/tool_user_try_times_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_writing_detail_model/tool_user_writing_detail_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_writing_page_model/tool_user_writing_page_model.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ToolUserWritingOptimizationPageProvider extends ChangeNotifier {
  Stream<List<int>>? streamText;

  String? accumulatedText = '';

  RefreshController refreshController = RefreshController();
  late int id;

  // ToolUserWritingDetailPageProvider() {
  //   loadData();
  //   // notifyListeners();
  // }

  loadData(id) async {
    try {
      streamText = await Api.compositionAssessmentOptimize(id);
      processStreamResponse(streamText!);
    } catch (e) {
      refreshController.refreshFailed();
    } finally {
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }

  void processStreamResponse(Stream stream) async {
// 用于每个阶段的对话结果
    final StringBuffer buffer = StringBuffer();

    // 处理流式响应
    await for (var data in stream) {
      final bytes = data as List<int>;
      final decodedData = utf8.decode(bytes);
      accumulatedText = (accumulatedText ?? '') + decodedData;
      notifyListeners();
    }
  }
}
