// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_try_times_model/tool_user_try_times_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_writing_composition_assessment_model/tool_user_writing_composition_assessment_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_writing_detail_model/tool_user_writing_detail_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_writing_page_model/tool_user_writing_page_model.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ToolUserWritingPageProvider extends ChangeNotifier {
  ToolUserTryTimesModel? toolUserTryTimesModel;
  ToolUserWritingCompositionAssessmentModel?
      toolUserWritingCompositionAssessmentModel;

  ToolUserWritingDetailModel? toolUserWritingDetailModel;
  RefreshController refreshController = RefreshController();
  ToolUserWritingPageModel? toolUserWritingPageModel;
  ScrollController scrollController = ScrollController();
  bool isVip = true;
  final int initPageNum = 1;
  final int pageSize = 20;

  final int maxLengthInput = 3000;

  String tempPictureFile = '';

  bool ocring = false;

  String ocrResult = '';

  String inputText = '';

  bool compositionAssessmentOnClick = false;

  ToolUserWritingPageProvider() {
    loadData();
    // notifyListeners();
  }

  // inputTextChange(String value) {
  //   inputText   = value;
  //   // notifyListeners();
  // }

  Future<bool> compositionAssessment(text) async {
    try {
      if (compositionAssessmentOnClick) {
        return false;
      }
      compositionAssessmentOnClick = true;
      notifyListeners();
      EasyLoading.showToast('批改中...', duration: Duration(seconds: 15));
      toolUserWritingCompositionAssessmentModel =
          await Api.compositionAssessment(text);
      EasyLoading.dismiss();
      return true;
    } catch (e) {
      refreshController.refreshFailed();
      return false;
    } finally {
      compositionAssessmentOnClick = false;
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }

  loadData() async {
    try {
      toolUserWritingPageModel =
          await Api.getUserCompositionAssessmentPage(initPageNum, pageSize);
    } catch (e) {
      refreshController.refreshFailed();
    } finally {
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }

  loadMore() async {
    try {
      ToolUserWritingPageModel value =
          await Api.getUserCompositionAssessmentPage(
              toolUserWritingPageModel!.data!.pageNum! + 1, pageSize);

      _append(value);
    } catch (e) {
      refreshController.loadComplete();
    } finally {
      refreshController.loadComplete();
      notifyListeners();
    }
  }

  void _append(ToolUserWritingPageModel newItemData) {
    if (toolUserWritingPageModel == null) {
      toolUserWritingPageModel = newItemData;
      return;
    }
    if (newItemData.data?.recods?.isEmpty == true) {
      return;
    }
    toolUserWritingPageModel!.data!.pageNum = newItemData.data!.pageNum;
    toolUserWritingPageModel!.data!.pageSize = newItemData.data!.pageSize;

    toolUserWritingPageModel!.data!.recods!
        .addAll(newItemData.data?.recods ?? []);
  }

  tryTimes() async {
    try {
      toolUserTryTimesModel = await Api.getUserToolTryTimes();
    } catch (e) {
      refreshController.refreshFailed();
    } finally {
      refreshController.refreshCompleted();
      // notifyListeners();
    }
  }

  checkIsVip() async {
    final userInfo = await LoginUtil.userInfoModel();
    isVip = userInfo?.data?.memberType == 1;
    // return isVip;
  }

  getUserCompositionAssessmentDetail(id) async {
    try {
      toolUserWritingDetailModel =
          await Api.getUserCompositionAssessmentDetail(id);
    } catch (e) {
      refreshController.refreshFailed();
    } finally {
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }

  Future<String> ocr(BuildContext context) async {
    if (ocring || tempPictureFile.isEmpty) {
      return '';
    }
    EasyLoading.showToast('正在识别', duration: Duration(seconds: 15));

    ocring = true;

    // 上传图片获取网络地址
    var uploadResult = await Api.uploadFile(tempPictureFile,
        topicCode: '', scene: 'writing_correction');

    // print(uploadResult.data!.fileUrl!);

    // 小程序
    // var result = await Api.ocr("https://cos.xinquai.com/writing_correction/000/20241208/4704/user/47041733648072.9437456tmp_b63c93820b5641e2ffd61a943a55aec6.jpg");
    // app
    // var result = await Api.ocr("https://cos.xinquai.com/writing_correction/000/20241208/718/user/7181733648763.9764168image_picker_0CFA8C6C-2D32-4446-893F-11791922409A-50589-00001839398DBF9C.jpg");
    var result = await Api.ocr(uploadResult.data!.fileUrl!);
    ocrResult = result.data ?? '';
    inputText = ocrResult;
    ocring = false;
    EasyLoading.dismiss();
    return ocrResult;
  }
}
