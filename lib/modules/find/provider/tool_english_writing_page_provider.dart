// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_english_writing_detail_model/tool_user_english_writing_detail_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_english_writing_recods_model/tool_user_english_writing_recods_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_try_times_model/tool_user_try_times_model.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_translate_page.dart';
import 'package:flutter_app_kouyu/modules/my/pages/report_record_page.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ToolEnglishWritingPageProvider extends ChangeNotifier {
  ToolUserTryTimesModel? toolUserTryTimesModel;
  ToolUserEnglishWritingRecodsModel? toolUserEnglishWritingRecodsModel;
  ToolUserEnglishWritingDetailModel? toolUserEnglishWritingDetailModel;
  RefreshController refreshController = RefreshController();
  bool isVip = true;

  final int initPageNum = 1;
  final int pageSize = 20;
  Stream<List<int>>? streamText;
  String tempPictureFile = '';

  bool ocring = false;

  String ocrResult = '';

  String? accumulatedText = '数据加载中...';

  // 写作类型
  String compositionType = '';
  // 描述
  String directions = '';

  // 标题
  String title = '';

  //紫薯
  String wordCount = '';

  // ignore: non_constant_identifier_names
  ToolUserTryTimesProvider() {
    loadData();
  }

  loadData() async {
    try {
      toolUserTryTimesModel = await Api.getUserToolTryTimes();
    } catch (e) {
      refreshController.refreshFailed();
    } finally {
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }

  checkIsVip() async {
    final userInfo = await LoginUtil.userInfoModel();
    isVip = userInfo?.data?.memberType == 1;
    // return isVip;
  }

  submit() async {
    try {
      streamText = await Api.englistWriting(
          compositionType, directions, title, wordCount);
      processStreamResponse(streamText!);
    } catch (e) {
      refreshController.refreshFailed();
    } finally {
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }

  void processStreamResponse(Stream stream) async {
    accumulatedText = '';
// 用于每个阶段的对话结果
    final StringBuffer buffer = StringBuffer();

    // 处理流式响应
    await for (var data in stream) {
      final bytes = data as List<int>;
      final decodedData = utf8.decode(bytes);
      accumulatedText = (accumulatedText ?? '') + decodedData;
      notifyListeners();
    }
  }

  recods() async {
    try {
      toolUserEnglishWritingRecodsModel =
          await Api.getUserEnglishWritingPage(initPageNum, pageSize);
    } catch (e) {
      refreshController.refreshFailed();
    } finally {
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }

  recodsLoadMore() async {
    try {
      ToolUserEnglishWritingRecodsModel value =
          await Api.getUserEnglishWritingPage(
              toolUserEnglishWritingRecodsModel!.data!.pageNum! + 1, pageSize);

      _append(value);
    } catch (e) {
      refreshController.loadComplete();
    } finally {
      refreshController.loadComplete();
      notifyListeners();
    }
  }

  void _append(ToolUserEnglishWritingRecodsModel newItemData) {
    if (toolUserEnglishWritingRecodsModel == null) {
      toolUserEnglishWritingRecodsModel = newItemData;
      return;
    }
    if (newItemData.data?.recods?.isEmpty == true) {
      return;
    }
    toolUserEnglishWritingRecodsModel!.data!.pageNum =
        newItemData.data!.pageNum;
    toolUserEnglishWritingRecodsModel!.data!.pageSize =
        newItemData.data!.pageSize;

    toolUserEnglishWritingRecodsModel!.data!.recods!
        .addAll(newItemData.data?.recods ?? []);
  }

  detail(int id) async {
    try {
      toolUserEnglishWritingDetailModel =
          await Api.getUserEnglishWritingDetail(id);
    } catch (e) {
      refreshController.refreshFailed();
    } finally {
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }

  Future<String> ocr(BuildContext context) async {
    if (ocring || tempPictureFile.isEmpty) {
      return '';
    }
    EasyLoading.showToast('正在识别', duration: Duration(seconds: 15));

    ocring = true;

    // 上传图片获取网络地址
    var uploadResult = await Api.uploadFile(tempPictureFile,
        topicCode: '', scene: 'writing_correction');

    // print(uploadResult.data!.fileUrl!);

    // 小程序
    // var result = await Api.ocr("https://cos.xinquai.com/writing_correction/000/20241208/4704/user/47041733648072.9437456tmp_b63c93820b5641e2ffd61a943a55aec6.jpg");
    // app
    // var result = await Api.ocr("https://cos.xinquai.com/writing_correction/000/20241208/718/user/7181733648763.9764168image_picker_0CFA8C6C-2D32-4446-893F-11791922409A-50589-00001839398DBF9C.jpg");
    var result = await Api.ocr(uploadResult.data!.fileUrl!);
    ocrResult = result.data ?? '';
    ocring = false;
    EasyLoading.dismiss();
    return ocrResult;
  }
}
