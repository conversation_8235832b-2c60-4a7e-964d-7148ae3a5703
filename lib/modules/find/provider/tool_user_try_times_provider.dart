// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_try_times_model/tool_user_try_times_model.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/my/pages/report_record_page.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ToolUserTryTimesProvider extends ChangeNotifier {
  ToolUserTryTimesModel? toolUserTryTimesModel;
  RefreshController refreshController = RefreshController();
  bool isVip = true;

  ToolUserTryTimesProvider() {
    loadData();
    // notifyListeners();
  }

  loadData() async {
    try {
      toolUserTryTimesModel = await Api.getUserToolTryTimes();
    } catch (e) {
      refreshController.refreshFailed();
    } finally {
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }

  checkIsVip() async {
    final userInfo = await LoginUtil.userInfoModel();
    isVip = userInfo?.data?.memberType == 1;
    // return isVip;
  }
}
