// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/explore_topic_list_model/explore_topic_list_model.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';

class TypeItem {
  final String title;
  final String key;
  TypeItem({required this.title, required this.key});
}

class FindPageProvider extends ChangeNotifier {
  ExploreTopicListModel? exploreTopicListModel;
  List<TypeItem> typeList = [
    TypeItem(title: '推荐', key: 'recommend'),
    TypeItem(title: '趣味', key: 'fun'),
    TypeItem(title: '工具', key: 'tool'),
    TypeItem(title: '角色', key: 'role'),
  ];
  String _selectTypeTab = 'recommend';
  FindPageProvider() {
    getExploreTopicList();
  }

  set selectTypeTab(String selectTypeTab) {
    _selectTypeTab = selectTypeTab;
    getExploreTopicList();
    notifyListeners();
  }

  String get selectTypeTab => _selectTypeTab;

  getExploreTopicList() async {
    exploreTopicListModel = await Api.exploreTopicList(_selectTypeTab);
    notifyListeners();
  }
}
