import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/modules/find/pages/find_page.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_photo_ocr_page.dart';
import 'package:flutter_app_kouyu/modules/find/provider/tool_translate_page_provider.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

/// 翻译模式枚举
enum TranslateModeEnum {
  /// 中文翻译成英文
  chineseToEnglish,

  /// 英文翻译成中文
  englishToChinese
}

class ToolTranslatePage extends StatelessWidget {
  final content;
  final cbParam;
  const ToolTranslatePage({super.key, this.content = '', this.cbParam = ''});

  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const ToolTranslatePage());
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<ToolTranslatePageProvider>(
      create: (_) => ToolTranslatePageProvider()..loadData(),
      child: Consumer<ToolTranslatePageProvider>(
        builder: (context, provider, child) {
          return GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus(); // 点击其他地方收起键盘
            },
            child: Stack(
              children: [
                Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: const Color(0xFFF3FBFD),
                ),
                Scaffold(
                  backgroundColor: Colors.transparent,
                  appBar: AppBar(
                    leadingWidth: 300,
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    leading: CustomAppbar.leftWidget(context, text: "随身翻译"),
                  ),
                  floatingActionButtonLocation:
                      FloatingActionButtonLocation.centerDocked,
                  body: Padding(
                      padding: EdgeInsets.all(32.w),
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            _modeSwitchWidget(context, provider),
                            SizedBox(height: 10.w),
            
                            // 一个大的column布局, 内边距为32.w. 边框线为蓝色
                            // 里面包含三个元素, 高度随着翻译结果的高度变化
                            // 一个高度为最为700的输入框,
                            // 右下角有一个拍照icon, 用于进入拍照ocr页面
                            // 最下面如果有翻译结果, 展示翻译结果, 没有就隐藏
                            Container(
                              padding: EdgeInsets.all(24.w).copyWith(top: 0.w),
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  border: Border.all(
                                      color: const Color(0xFF41C0FF), width: 1),
                                  borderRadius: BorderRadius.circular(18.w)),
                              child: Column(
                                children: [
                                  // 输入框
                                  TextField(
                                      controller: TextEditingController(
                                          text: provider.inputContent),
                                      onChanged: (value) {
                                        provider.inputContent = value;
                                      },
                                      maxLines: 8,
                                      maxLength: 1000,
                                      decoration: InputDecoration(
                                          border: InputBorder.none, // 没有边框样式
                                          hintText: "请输入要翻译的内容",
                                          hintStyle: style_1_28_400),
                                      style: style_1_32_400),
            
                                  // 拍照icon, 在右下角
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.of(context)
                                              .push(
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  ToolPhotoOcrPage(
                                                toolType: ToolType.translation,
                                                cbParam: provider.translateMode ==
                                                        TranslateModeEnum
                                                            .chineseToEnglish
                                                    ? 'z2e'
                                                    : 'e2z',
                                              ),
                                            ),
                                          )
                                              .then((result) {
                                            if (result != null) {
                                              print(result);
                                              provider.setInputContent(
                                                  result['result']);
                                            }
                                          });
                                        },
                                        child: Image.asset(
                                          "images/camera-line.png",
                                          width: 50.w,
                                          height: 50.w,
                                        ),
                                      )
                                    ],
                                  ),
            
                                  // 如果存在翻译结果, 就展示的内容
                                  if (provider.translateContent != '')
                                    Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // 分割线
                                          const Divider(color: Color(0xFFDDEDF0)),
                                          Text('译文', style: style_1_24),
                                          SizedBox(height: 10.w),
                                          // 翻译结果
                                          Text(provider.translateContent ?? "",
                                              style: style_1_32_400),
                                          // 分割线
                                          const Divider(color: Color(0xFFDDEDF0)),
                                          Row(
                                            children: [
                                              _voicePlayWidget(provider),
                                              SizedBox(width: 20.w),
                                              _copyIconWidget(provider),
                                            ],
                                          )
                                        ])
                                ],
                              ),
                            ),
                            _translateButton(provider),
                            SizedBox(height: 20.w),
                            if (!provider.isVip)
                              _leftTimeTipWidget(context, provider)
                          ],
                        ),
                      )),
                )
              ],
            ),
          );
        },
      ),
    );
  }

  /// 一个中英文切换的组件, 左边是中文, 中间是切换图标, 右边是英文
  Widget _modeSwitchWidget(
      BuildContext context, ToolTranslatePageProvider model) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
            model.translateMode == TranslateModeEnum.chineseToEnglish
                ? "中文"
                : "英文",
            style: style_1_32_400),
        SizedBox(width: 20.w),
        // 一个点击就会切换中英文的图片
        GestureDetector(
          onTap: () {
            model.switchTranslateMode();
          },
          child: Image.network(
            'https://cos.xinquai.com/user_upload/<EMAIL>',
            width: 70.w,
            height: 50.w,
          ),
        ),
        SizedBox(width: 20.w),
        Text(
            model.translateMode == TranslateModeEnum.chineseToEnglish
                ? "英文"
                : "中文",
            style: style_1_32_400),
      ],
    );
  }

  /// 复制小图标组件
  Widget _copyIconWidget(ToolTranslatePageProvider provider) {
    return GestureDetector(
        onTap: () {
          Clipboard.setData(
              ClipboardData(text: provider.translateContent ?? ""));
          EasyLoading.showToast("已复制");
        },
        child: Image(
          image: const AssetImage("images/chat_copy_blue.png"),
          width: 48.w,
          height: 48.w,
        ));
  }

  /// 语音播放组件
  Widget _voicePlayWidget(ToolTranslatePageProvider provider) {
    return PlayButtonWidget(
      style: PlayButtonStyle.blue,
      text: provider.translateContent,
    );
  }

  /// 翻译按钮
  Widget _translateButton(ToolTranslatePageProvider provider) {
    return GestureDetector(
      onTap: () {
        provider.translate();
      },
      child: Container(
        width: 680.w,
        height: 102.w,
        margin: EdgeInsets.fromLTRB(0, 24.w, 0, 0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: const Color(0xFF2693FF),
        ),
        child: Center(
            child: Text(
          "翻译",
          style: style_1_32.copyWith(color: Colors.white),
        )),
      ),
    );
  }

  /// 翻译按钮下方的剩余次数和vip提示
  Widget _leftTimeTipWidget(
      BuildContext context, ToolTranslatePageProvider provider) {
    var leftTimes =
        provider.toolUserTryTimesModel?.data?.toolTryTimesTranslated ?? 0;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "剩余${leftTimes}次免费使用,",
          style: style_1_24,
        ),
        GestureDetector(
          onTap: () {
            provider.goVipPage(context);
          },
          child: Text(
            "开通学习会员畅快使用",
            style: style_bue_24,
          ),
        )
      ],
    );
  }
}
