// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:math';
import 'dart:ui';
import 'dart:io' show Platform;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_hint_model/data.dart';
import 'package:flutter_app_kouyu/widgets/selected_text.dart';
import 'package:flutter_app_kouyu/widgets/share-widget/share_image_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gif_view/gif_view.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';

import 'package:flutter_app_kouyu/common/http/loading_status.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';
import 'package:flutter_app_kouyu/modules/chat/view/speed_settings_dialog.dart';
import 'package:flutter_app_kouyu/modules/find/provider/explore_chat_cubit.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_app_kouyu/common/http/models/explore_topic_list_model/datum.dart'
    as topic_model;

enum ExploreTalkTypeEnum {
  unknown("default", "默认"),
  fun("fun", "趣味"),
  tool("tool", "工具"),
  role("role", "角色");

  final String type;
  final String name;
  const ExploreTalkTypeEnum(this.type, this.name);

  static ExploreTalkTypeEnum from(String? type) {
    return values.firstWhere(
      (element) => element.type == type,
      orElse: () => ExploreTalkTypeEnum.unknown,
    );
  }
}

class ExploreTalkArgument {
  late final ExploreTalkTypeEnum type;
  late final String topicCode;
  final topic_model.Datum topicData;
  // 0: 英文呢 1: 中英
  late final int model;
  late final Theme theme;

  ExploreTalkArgument({required this.topicData}) {
    topicCode = topicData.topicCode ?? "";
    type = ExploreTalkTypeEnum.from(topicData.exploreType);
    if (type == ExploreTalkTypeEnum.tool) {
      model = 1;
    } else {
      model = 0;
    }
    theme = Theme(
        themeStyle: topicData.isBackgroundStyle == 1
            ? ThemeStyle.dark
            : ThemeStyle.light,
        bgImageUrl: topicData.bgImageUrl);
  }
}

class Theme {
  ThemeStyle themeStyle;
  String? bgImageUrl;
  Theme({required this.themeStyle, this.bgImageUrl});
}

enum ThemeStyle { dark, light }

class ExploreTalkPage extends StatelessWidget {
  final ExploreTalkArgument argument;
  const ExploreTalkPage({super.key, required this.argument});
  static Route<void> route({required ExploreTalkArgument argument}) {
    return MaterialPageRoute<void>(
        builder: (_) => ExploreTalkPage(
              argument: argument,
            ));
  }

  void pop(BuildContext context) async {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (didPop) {
          return;
        }
        pop(context);
      },
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) =>
                VoiceCubit(showTextInout: true, model: argument.model),
          ),
          ChangeNotifierProvider(
            create: (context) => ExploreChatProvider(
              context,
              argument: argument,
            )..intiGreetingStream(),
          ),
        ],
        child: Consumer<ExploreChatProvider>(builder: (context, model, _) {
          return Scaffold(
            extendBodyBehindAppBar: true,
            backgroundColor: argument.theme.themeStyle == ThemeStyle.dark
                ? const Color(0xFF151515)
                : const Color(0xFFF0F8FA),
            appBar: TalkAPPBar(
              argument: argument,
              finishTask: false,
              conversationId: 0,
              canCreateReport: model.list.length >= 6 && argument.model == 0,
              currentQuestionIndex: model.currentQuestionIndex,
              totalQuestionCount: model.totalQuestionCount,
              pop: (p0) => pop(context),
            ),
            body: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                // 触摸收起键盘
                FocusScope.of(context).requestFocus(FocusNode());
              },
              child: Stack(
                children: [
                  if (argument.theme.bgImageUrl != null)
                    Positioned.fill(
                      child: CachedNetworkImage(
                        imageUrl: argument.theme.bgImageUrl!,
                        fit: BoxFit.cover,
                      ),
                    ),
                  SafeArea(
                    top: true,
                    bottom: false,
                    child: Column(
                      children: [
                        Expanded(
                          child: Align(
                            alignment: Alignment.topCenter,
                            child: _chatListView(),
                          ),
                        ),
                        BlocBuilder<VoiceCubit, VoiceState>(
                          builder: (context, state) {
                            return state.voiceType == VoiceTypeEnum.init ||
                                    state.voiceType == VoiceTypeEnum.textInput
                                ? _chatWidget()
                                : VoiceWidget(
                                    model: argument.model,
                                    argument: argument,
                                  );
                          },
                        )
                      ],
                    ),
                  ),
                  // if (argument.theme.bgImageUrl != null)
                  //   Visibility(
                  //     visible: model.scrollowDirection == ScrollowDirection.top,
                  //     child: ClipRRect(
                  //       child: Align(
                  //         alignment: Alignment.topCenter,
                  //         heightFactor: 0.35,
                  //         child: CachedNetworkImage(
                  //           imageUrl: argument.theme.bgImageUrl!,
                  //           fit: BoxFit.cover,
                  //         ),
                  //       ),
                  //     ),
                  //   ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _inputWidget(BuildContext context) {
    VoiceCubit voiceCubit = context.read<VoiceCubit>();
    return TextField(
        onChanged: (value) {
          voiceCubit.updateInputText(value);
        },
        keyboardType: TextInputType.visiblePassword, // 强制英文键盘
        inputFormatters: [
          if (argument.model == 0)
            FilteringTextInputFormatter.allow(
                RegExp(r'[^\u4e00-\u9fa5]')), // 不允许中文
        ],
        onSubmitted: (value) async {
          await context
              .read<ExploreChatProvider>()
              .chat(voiceCubit.state.text, "");
          voiceCubit.clearInputText();
        },
        controller: voiceCubit.inputTextEditingController,
        autofocus: true,
        style: argument.theme.themeStyle == ThemeStyle.dark
            ? style_1_32_400
                .copyWith(color: Colors.white)
                .copyWith(fontSize: 16)
            : style_1_32_400.copyWith(fontSize: 16),
        maxLines: 2,
        minLines: 1, //最少多少行
        decoration: InputDecoration(
          hintText: "请输入您想说的话",

          hintStyle: TextStyle(
              color: argument.theme.themeStyle == ThemeStyle.dark
                  ? Colors.white
                  : const Color(0xFFA7D3DB),
              fontSize: 32.w),
          suffixIconConstraints:
              BoxConstraints(minWidth: 48.w, minHeight: 48.w),
          isCollapsed: true,
          contentPadding: EdgeInsets.symmetric(
              horizontal: 24.w, vertical: 24.w), //内容内边距，影响高度

          suffixIcon: Padding(
            padding: EdgeInsets.only(right: 24.w),
            child: voiceCubit.inputTextEditingController.text.isNotEmpty
                ? GestureDetector(
                    onTap: () async {
                      FocusScope.of(context).requestFocus(FocusNode());

                      await context
                          .read<ExploreChatProvider>()
                          .chat(voiceCubit.state.text, "");
                      voiceCubit.clearInputText();
                    },
                    child: Image.asset(
                      "images/input_send.png",
                      width: 48.w,
                      height: 48.w,
                    ),
                  )
                : GestureDetector(
                    onTap: () {
                      //教学模式支持中英文
                      context.read<VoiceCubit>().speakEn(
                          isZh: argument.model == 1,
                          chatCubit: context.read<ExploreChatProvider>());
                      context.read<ExploreChatProvider>().cleanHit();
                    },
                    child: Image.asset(
                      "images/input_voice.png",
                      width: 48.w,
                      height: 48.w,
                      color: argument.theme.themeStyle == ThemeStyle.dark
                          ? Colors.white
                          : null,
                    ),
                  ),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(32.w),
            borderSide: BorderSide(color: const Color(0xFFDDEDF0), width: 2.w),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(32.w),
            borderSide: BorderSide(color: const Color(0xFFDDEDF0), width: 2.w),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(32.w),
            borderSide: BorderSide(color: const Color(0xFFDDEDF0), width: 2.w),
          ),
        ));
  }

  Widget _chatListView() {
    return Consumer<ExploreChatProvider>(
      builder: (context, state, _) {
        return ListView.builder(
          reverse: true,
          shrinkWrap: true,
          controller: state.scrollController,
          itemBuilder: (BuildContext context, int index) {
            int total = state.list.length +
                ((state.chatHintStatus == LoadingStatus.inProgress ||
                        state.chatHintList.isNotEmpty)
                    ? 1
                    : 0);
            if (index == 0) {
              if (state.chatHintList.isNotEmpty) {
                //最后一个且有跟读
                return _followListWidget(state.chatHintList);
              } else if (state.chatHintStatus == LoadingStatus.inProgress) {
                return _followListLoadingWidget();
              }
            }

            ExploreTalkModel model;
            int chatIndex = total - index - 1;
            model = state.list[chatIndex];
            if (model.type == 0) {
              //question
              return Padding(
                padding: EdgeInsets.only(left: 40.w, right: 64.w, bottom: 24.w),
                child: Column(
                  children: [
                    if (index == total - 1 &&
                        argument.theme.themeStyle == ThemeStyle.dark)
                      SizedBox(
                        height: 400.w,
                      ),
                    ChatListItemLeft(
                      argument: argument,
                      ieltsTalkModel: model,
                      index: chatIndex,
                    ),
                  ],
                ),
              );
            } else if (model.type == 1) {
              return Padding(
                padding: EdgeInsets.only(left: 64.w, right: 40.w, bottom: 24.w),
                child: ChatListItemRight(
                  ieltsTalkModel: model,
                  index: chatIndex,
                  argument: argument,
                ),
              );
            } else if (model.type == 2) {
              return Padding(
                padding: EdgeInsets.only(left: 48.w, right: 64.w, bottom: 24.w),
                child: _leftHitWidget(context, model.answer ?? ""),
              );
            }
          },
          itemCount: state.list.length +
              ((state.chatHintStatus == LoadingStatus.inProgress ||
                      state.chatHintList.isNotEmpty)
                  ? 1
                  : 0),
        );
      },
    );
  }

  Widget _leftHitWidget(BuildContext context, String text) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

        context.read<ExploreChatProvider>().chat(text, "");
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ConstrainedBox(
            constraints: BoxConstraints(
                maxWidth: ScreenUtil().screenWidth - 64.w - 48.w),
            child: Container(
              padding: EdgeInsets.all(24.w),
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: const BorderRadius.all(Radius.circular(12)),
                border: Border.all(
                  color: const Color(0xFFDDEDF0),
                  width: 2.w,
                ),
              ),
              child: Text(
                text,
                style: argument.theme.themeStyle == ThemeStyle.dark
                    ? style_1_28_400.copyWith(color: Colors.white)
                    : style_1_28_400,
              ),
            ),
          ),
          const Expanded(child: SizedBox()),
        ],
      ),
    );
  }

  Widget _followListLoadingWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 6,
        ),
        Padding(
          padding: const EdgeInsets.only(left: 20),
          child: Text(
            '你可以尝试这样回答',
            style: style_2_28,
          ),
        ),
        SizedBox(
          height: 24.w,
        ),
        Container(
          height: 48,
          width: double.infinity,
          margin: const EdgeInsets.only(left: 20, right: 20),
          decoration: BoxDecoration(
            color: argument.theme.themeStyle == ThemeStyle.dark
                ? Colors.transparent
                : Colors.white,
            borderRadius: const BorderRadius.all(Radius.circular(12)),
            border: Border.all(
              color: const Color(0xFF84E9FF),
              width: 3,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(
                width: 12,
              ),
              argument.theme.themeStyle == ThemeStyle.dark
                  ? Lottie.asset('lottie/reply_loading_dart.json',
                      width: 36, height: 27, repeat: true)
                  : GifView.asset(
                      "images/hint_loading.gif",
                      height: 27,
                    ),
            ],
          ),
        ),
        const SizedBox(
          height: 12,
        ),
      ],
    );
  }

  Widget _followListWidget(List<Data> chatHintList) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 6,
        ),
        Padding(
          padding: const EdgeInsets.only(left: 20),
          child: Text(
            '你可以尝试这样回答',
            style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                color: Color(0xFF646E70)),
          ),
        ),
        const SizedBox(
          height: 12,
        ),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: IntrinsicHeight(
            child: Row(
              children: [
                const SizedBox(
                  width: 16,
                ),
                ...List.generate(chatHintList.length, (index) {
                  var data = chatHintList[index];
                  return ChatHitItemWidget(
                    argument: argument,
                    hitModel: data,
                  );
                }),
                const SizedBox(
                  width: 16,
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _chatWidget() {
    return BlocBuilder<VoiceCubit, VoiceState>(
      builder: (context, state) {
        return Container(
          padding: EdgeInsets.all(8.w),
          margin: EdgeInsets.only(bottom: 48.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.w),
          ),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(bottom: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 40.w,
                    ),
                    GestureDetector(
                      onTap: () {
                        FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                        if (LoadingStatus.inProgress ==
                            context
                                .read<ExploreChatProvider>()
                                .list
                                .last
                                .replyStatus
                                ?.status) {
                          EasyLoading.showToast('正在回复...');

                          return;
                        }
                        context
                            .read<VoiceCubit>()
                            .chineseHelp(context.read<ExploreChatProvider>());
                        context.read<ExploreChatProvider>().cleanHit();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.w),
                            border: Border.all(
                                color:
                                    argument.theme.themeStyle == ThemeStyle.dark
                                        ? Colors.white38
                                        : separated,
                                width: 2.w)),
                        child: Image.asset(
                          argument.theme.themeStyle == ThemeStyle.dark
                              ? "images/chinese_help_btn_white.png"
                              : "images/chinese_help_btn.png",
                          width: 199.w,
                          height: 72.w,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 16.w,
                    ),
                    GestureDetector(
                      onTap: () {
                        FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                        context.read<ExploreChatProvider>().getChatHint();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.w),
                            border: Border.all(
                                color:
                                    argument.theme.themeStyle == ThemeStyle.dark
                                        ? Colors.white38
                                        : separated,
                                width: 2.w)),
                        child: Image.asset(
                          argument.theme.themeStyle == ThemeStyle.dark
                              ? "images/hit_btn_white.png"
                              : "images/hit_btn.png",
                          width: 199.w,
                          height: 72.w,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 40.w,
                    ),
                  ],
                ),
              ),
              state.voiceType == VoiceTypeEnum.textInput
                  ? Padding(
                      padding: EdgeInsets.only(left: 40.w, right: 40.w),
                      child: _inputWidget(context),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 40.w,
                        ),
                        _speakWidget(context),
                        SizedBox(
                          width: 40.w,
                        ),
                      ],
                    ),
            ],
          ),
        );
      },
    );
  }

  Expanded _speakWidget(BuildContext context) {
    ExploreChatProvider model = context.read<ExploreChatProvider>();
    return Expanded(
      child: Container(
        height: Platform.isIOS ? 96.w : 96.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: argument.theme.themeStyle == ThemeStyle.dark
              ? Colors.white.withOpacity(0.16)
              : const Color(0xFF2693FF),
        ),
        child: TextButton(
            onPressed: () {
              if (LoadingStatus.inProgress ==
                  model.list.last.replyStatus?.status) {
                EasyLoading.showToast('正在回复...');

                return;
              }
              //教学模式支持中英文
              context.read<VoiceCubit>().speakEn(
                  isZh: argument.model == 1,
                  chatCubit: context.read<ExploreChatProvider>());
              context.read<ExploreChatProvider>().cleanHit();
            },
            style: const ButtonStyle(
              textStyle: MaterialStatePropertyAll(
                  TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
            ),
            child: Row(
              children: [
                if (context.read<VoiceCubit>().showTextInout)
                  SizedBox(
                    width: 48.w,
                  ),
                const Spacer(),
                Text("点击说话", style: style_1_28.copyWith(color: Colors.white)),
                const Spacer(),
                if (context.read<VoiceCubit>().showTextInout)
                  GestureDetector(
                    child: Image.asset(
                      "images/text_input_icon.png",
                      width: 48.w,
                      height: 48.w,
                      color: argument.theme.themeStyle == ThemeStyle.dark
                          ? Colors.white
                          : null,
                    ),
                    onTap: () {
                      context.read<VoiceCubit>().showTextInput(context);
                    },
                  ),
              ],
            )),
      ),
    );
  }
}

class ChatHitItemWidget extends StatelessWidget {
  final Data? hitModel;
  final ExploreTalkArgument argument;
  const ChatHitItemWidget(
      {super.key, required this.hitModel, required this.argument});

  @override
  Widget build(BuildContext context) {
    return Container(
        width: 300,
        decoration: BoxDecoration(
          color: argument.theme.themeStyle == ThemeStyle.light
              ? Colors.white
              : Colors.transparent,
          borderRadius: const BorderRadius.all(Radius.circular(12)),
          border: Border.all(
            color: argument.theme.themeStyle == ThemeStyle.light
                ? Colors.white
                : const Color(0xFF84E9FF),
            width: 2.w,
          ),
        ),
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // 使Column自适应内容大小
          children: [
            // 文本内容区域 - 自适应高度
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    hitModel?.english ?? "",
                    style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 28.w,
                        color: argument.theme.themeStyle == ThemeStyle.light
                            ? null
                            : Colors.white),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    hitModel?.chinese ?? "",
                    style: style_2_24.copyWith(
                        color: argument.theme.themeStyle == ThemeStyle.light
                            ? null
                            : Colors.white),
                  ),
                ],
              ),
            ),
            // 底部区域 - 与底边框间距更小
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 8), // 减小与上方内容的间距
                _lineWidget(
                    color: argument.theme.themeStyle == ThemeStyle.dark
                        ? Colors.transparent
                        : ColorUtil.separated),
                SizedBox(height: 16.w), // 减小与底部按钮的间距
                Row(
                  children: [
                    PlayButtonWidget(
                        audioUrl: hitModel?.audioUrl ?? "",
                        style: argument.theme.themeStyle == ThemeStyle.dark
                            ? PlayButtonStyle.opacity
                            : PlayButtonStyle.blue),
                    SizedBox(width: 20.w),
                    GestureDetector(
                        onTap: () {
                          FocusScope.of(context).unfocus(); // 点击其他地方收起键盘
                          context
                              .read<ExploreChatProvider>()
                              .collectionHitModel(hitModel!);
                        },
                        child: Image(
                          image: AssetImage(hitModel!.collected == true
                              ? argument.theme.themeStyle == ThemeStyle.dark
                                  ? "images/chat_collected_opacity.png"
                                  : "images/chat_collected.png"
                              : argument.theme.themeStyle == ThemeStyle.dark
                                  ? "images/chat_collect_opacity.png"
                                  : "images/chat_collect_blue.png"),
                          width: 48.w,
                          height: 48.w,
                        )),
                    SizedBox(width: 20.w),
                    GestureDetector(
                      onTap: () {
                        FocusScope.of(context).unfocus(); // 点击其他地方收起键盘
                        Clipboard.setData(
                            ClipboardData(text: hitModel?.english ?? ""));
                        EasyLoading.showToast("已复制");
                      },
                      child: Image(
                        image: AssetImage(
                            argument.theme.themeStyle == ThemeStyle.dark
                                ? "images/chat_copy_opacity.png"
                                : "images/chat_copy_blue.png"),
                        width: 48.w,
                        height: 48.w,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        FocusScope.of(context).unfocus(); // 点击其他地方收起键盘
                        context.read<VoiceCubit>().followRead(
                            hitModel?.english, context.read<ExploreChatProvider>());
                        context.read<ExploreChatProvider>().cleanHit();
                      },
                      child: Image.asset(
                        "images/fllow_btn.png",
                        height: 28,
                        color: argument.theme.themeStyle == ThemeStyle.dark
                            ? Colors.white
                            : null,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ));
  }
}

class ChatListItemLeft extends StatelessWidget {
  final ExploreTalkModel? ieltsTalkModel;
  final ExploreTalkArgument argument;
  final int index;
  const ChatListItemLeft(
      {super.key,
      required this.ieltsTalkModel,
      required this.index,
      required this.argument});

  @override
  Widget build(BuildContext context) {
    if (ieltsTalkModel?.replyStatus?.status == LoadingStatus.inProgress) {
      return Container(
          // margin: const EdgeInsets.only(top: 6, bottom: 6),
          padding:
              const EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: argument.theme.themeStyle == ThemeStyle.dark
                ? Colors.black.withOpacity(0.5)
                : Colors.white,
          ),
          child: Row(
            children: [
              argument.theme.themeStyle == ThemeStyle.dark
                  ? Lottie.asset('lottie/reply_loading_dart.json',
                      width: 36, height: 27, repeat: true)
                  : GifView.asset(
                      "images/reply_loading2.gif",
                      fit: BoxFit.fill,
                      width: 36,
                      height: 27,
                      repeat: ImageRepeat.repeat,
                    ),
            ],
          ));
    }
    return ClipRRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(
            sigmaX: argument.theme.themeStyle == ThemeStyle.dark ? 5 : 0,
            sigmaY: argument.theme.themeStyle == ThemeStyle.dark ? 5 : 0),
        child: Container(
            // margin: const EdgeInsets.only(top: 6, bottom: 0),
            padding:
                const EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: argument.theme.themeStyle == ThemeStyle.dark
                  ? Colors.black.withOpacity(0.5)
                  : Colors.white,
            ),
            child: ieltsTalkModel?.replyStatus?.status ==
                    LoadingStatus.inProgress
                ? GifView.asset(
                    "images/reply_loading.gif",
                    fit: BoxFit.fitWidth,
                    width: 16,
                    height: 16,
                    repeat: ImageRepeat.repeat,
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SelectableTextUtil.editableText(
                        context,
                        text: ieltsTalkModel?.answer ?? "",
                        style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontSize: Platform.isIOS ? 16 : 14,
                            color: argument.theme.themeStyle == ThemeStyle.dark
                                ? Colors.white
                                : const Color(0xFF061B1F)),
                      ),
                      ieltsTalkModel?.translateStatus?.status ==
                              LoadingStatus.inProgress
                          ? Lottie.asset(
                              argument.theme.themeStyle == ThemeStyle.dark
                                  ? 'lottie/play_buffering_dark.json'
                                  : 'lottie/play_buffering.json',
                              width: 16,
                              height: 16,
                              repeat: true)
                          : Visibility(
                              visible:
                                  ieltsTalkModel?.showTranslateText == true &&
                                      ieltsTalkModel?.translateText != null &&
                                      ieltsTalkModel!.translateText!.isNotEmpty,
                              child: Text(
                                ieltsTalkModel?.translateText ?? "",
                                style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: Platform.isIOS ? 12 : 10,
                                    color: argument.theme.themeStyle ==
                                            ThemeStyle.dark
                                        ? Colors.white
                                        : const Color(0xFF646E70)),
                              ),
                            ),
                      SizedBox(
                        height: Platform.isIOS ? 46.w : 35.w,
                      ),
                      _lineWidget(
                          color: argument.theme.themeStyle == ThemeStyle.dark
                              ? Colors.white.withOpacity(0.5)
                              : ColorUtil.separated),
                      SizedBox(
                        height: Platform.isIOS ? 46.w : 35.w,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          PlayButtonWidget(
                              audioUrl: ieltsTalkModel?.audioUrl ?? "",
                              style:
                                  argument.theme.themeStyle == ThemeStyle.dark
                                      ? PlayButtonStyle.opacity
                                      : PlayButtonStyle.blue),
                          SizedBox(
                            width: 20.w,
                          ),
                          GestureDetector(
                              onTap: () {
                                FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                                context
                                    .read<ExploreChatProvider>()
                                    .translate(ieltsTalkModel!, index);
                              },
                              child: Image(
                                image: AssetImage(
                                    argument.theme.themeStyle == ThemeStyle.dark
                                        ? "images/chat_translate_opacity.png"
                                        : "images/chat_translate_blue.png"),
                                width: 48.w,
                                height: 48.w,
                              )),
                          SizedBox(
                            width: 20.w,
                          ),
                          Visibility(
                            //工具不显示收藏
                            visible: argument.type != ExploreTalkTypeEnum.tool,
                            child: GestureDetector(
                                onTap: () {
                                  FocusScope.of(context)
                                      .unfocus(); // 点击其他地方收起键盘

                                  context
                                      .read<ExploreChatProvider>()
                                      .collection(ieltsTalkModel!, index);
                                },
                                child: Image(
                                  image: AssetImage(ieltsTalkModel!.collected ==
                                          true
                                      ? argument.theme.themeStyle ==
                                              ThemeStyle.dark
                                          ? "images/chat_collected_opacity.png"
                                          : "images/chat_collected.png"
                                      : argument.theme.themeStyle ==
                                              ThemeStyle.dark
                                          ? "images/chat_collect_opacity.png"
                                          : "images/chat_collect_blue.png"),
                                  width: 48.w,
                                  height: 48.w,
                                )),
                          ),
                          Visibility(
                            visible: argument.type != ExploreTalkTypeEnum.tool,
                            child: SizedBox(
                              width: 20.w,
                            ),
                          ),
                          GestureDetector(
                              onTap: () {
                                FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                                Clipboard.setData(ClipboardData(
                                    text: ieltsTalkModel?.answer ?? ""));
                                EasyLoading.showToast("已复制");
                              },
                              child: Image(
                                image: AssetImage(
                                    argument.theme.themeStyle == ThemeStyle.dark
                                        ? "images/chat_copy_opacity.png"
                                        : "images/chat_copy_blue.png"),
                                width: 48.w,
                                height: 48.w,
                              )),
                          SizedBox(
                            width: 20.w,
                          ),
                        ],
                      ),
                    ],
                  )),
      ),
    );
  }
}

Container _lineWidget({double? padding, required Color color}) {
  return Container(
    height: 1.w,
    margin: EdgeInsets.only(left: padding ?? 0, right: padding ?? 0),
    width: double.infinity,
    color: color,
  );
}

class ChatListItemRight extends StatelessWidget {
  final ExploreTalkModel ieltsTalkModel;
  final int index;
  final ExploreTalkArgument argument;

  const ChatListItemRight(
      {super.key,
      required this.ieltsTalkModel,
      required this.index,
      required this.argument});

  @override
  Widget build(
    BuildContext context,
  ) {
    return Container(
        // margin: const EdgeInsets.only(top: 6, bottom: 6),
        padding:
            const EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: const Color(0xFF41C0FF),
        ),
        child: SelectableTextUtil.editableText(
          context,
          text: ieltsTalkModel.answer ?? "",
          style: TextStyle(
              fontWeight: FontWeight.w400, 
              fontSize: Platform.isIOS ? 16 : 14, 
              color: Colors.white),
        ));
  }
}

class VoiceWidget extends StatelessWidget {
  // model 0: 自由聊天 1: 教学模式
  final int model;
  final ExploreTalkArgument argument;

  const VoiceWidget({
    super.key,
    required this.model,
    required this.argument,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VoiceCubit, VoiceState>(
      builder: (context, state) {
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          ScrollController scrollController =
              context.read<VoiceCubit>().scrollController;
          scrollController.jumpTo(scrollController.position.maxScrollExtent);
        });
        final maxSeconds = context.read<VoiceCubit>().maxSeconds;
        return Offstage(
          offstage: state.voiceType == VoiceTypeEnum.init,
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Column(
                children: [
                  Visibility(
                    visible: state.voiceType == VoiceTypeEnum.follow,
                    child: Container(
                      padding: Platform.isIOS 
                        ? const EdgeInsets.fromLTRB(12, 40, 12, 40)
                        : const EdgeInsets.fromLTRB(12, 30, 12, 30),
                      margin: Platform.isIOS
                        ? const EdgeInsets.fromLTRB(30, 0, 40, 23)
                        : const EdgeInsets.fromLTRB(30, 0, 40, 18),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: argument.theme.themeStyle == ThemeStyle.dark
                            ? null
                            : LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                    Color(0xFF98ECEC),
                                    Color(0xFF84E9FF),
                                    Color(0xFF9BE1FF)
                                  ]),
                      ),
                      child: Center(
                        child: Text(state.followTest ?? "_",
                            style: TextStyle(
                                color:
                                    argument.theme.themeStyle == ThemeStyle.dark
                                        ? Colors.white
                                        : const Color(0xFF061B1F),
                                fontSize: Platform.isIOS ? 16 : 14,
                                fontWeight: FontWeight.w400)),
                      ),
                    ),
                  ),
                  Container(
                    color: Colors.transparent,
                    height: 2.w,
                    width: double.infinity,
                    child: Center(
                      child: LinearProgressIndicator(
                        value: state.seconds / maxSeconds,
                        minHeight: 2.w,
                        backgroundColor: Colors.white,
                        color: Colors.cyan,
                      ),
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.only(left: 20, right: 20, top: 12),
                    color: Colors.transparent,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        TextField(
                          maxLines: Platform.isIOS ? 5 : 4,
                          readOnly: true,
                          controller:
                              context.read<VoiceCubit>().textEditingController,
                          scrollController:
                              context.read<VoiceCubit>().scrollController,
                          style: TextStyle(
                              fontSize: Platform.isIOS ? 16 : 14,
                              fontWeight: FontWeight.w400,
                              color:
                                  argument.theme.themeStyle == ThemeStyle.dark
                                      ? Colors.white
                                      : const Color(0xFF061B1F)),
                          textAlign: TextAlign.center,
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            focusedBorder: InputBorder.none,
                          ),
                        ),
                        Visibility(
                            visible:
                                state.voiceType == VoiceTypeEnum.cnTanslated,
                            child: const SizedBox(height: 10)),
                        Visibility(
                            visible:
                                state.voiceType == VoiceTypeEnum.cnTanslated,
                            child: Align(
                              alignment: Alignment.center,
                              child: GestureDetector(
                                onTap: () => CommonUtils.playVideo(state
                                    .chToEnAudioModel?.data?.replyAudioUrl),
                                child: RichText(
                                    maxLines: 3,
                                    text: TextSpan(
                                        text: state.chToEnAudioModel?.data
                                                ?.reply ??
                                            "",
                                        style: TextStyle(
                                            color: argument.theme.themeStyle ==
                                                    ThemeStyle.dark
                                                ? Colors.white
                                                : const Color(0xFF061B1F),
                                            fontWeight: FontWeight.w400,
                                            fontSize: Platform.isIOS ? 16 : 14),
                                        children: [
                                          WidgetSpan(
                                              alignment:
                                                  PlaceholderAlignment.middle,
                                              child: PlayButtonWidget(
                                                style: PlayButtonStyle.white,
                                                audioUrl: state.chToEnAudioModel
                                                    ?.data?.replyAudioUrl,
                                              )),
                                        ])),
                              ),
                            )),
                        const SizedBox(height: 20),
                        Visibility(
                          visible: state.voiceType == VoiceTypeEnum.cnTanslated,
                          child: Container(
                            height: Platform.isIOS ? 52 : 45,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              gradient:
                                  argument.theme.themeStyle == ThemeStyle.dark
                                      ? null
                                      : const LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                              Color(0xFF98ECEC),
                                              Color(0xFF84E9FF),
                                              Color(0xFF9BE1FF)
                                            ]),
                            ),
                            child: TextButton(
                                onPressed: () {
                                  context.read<VoiceCubit>().followRead(
                                      state.chToEnAudioModel?.data?.reply,
                                      context.read<ExploreChatProvider>());
                                  context
                                      .read<ExploreChatProvider>()
                                      .cleanHit();
                                },
                                style: const ButtonStyle(
                                  textStyle: MaterialStatePropertyAll(TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.w700)),
                                ),
                                child: Center(
                                  child: Text("点击跟读",
                                      style: TextStyle(
                                          color: argument.theme.themeStyle ==
                                                  ThemeStyle.dark
                                              ? Colors.white
                                              : const Color(0xFF061B1F),
                                          fontSize: Platform.isIOS ? 18 : 16,
                                          fontWeight: FontWeight.w800)),
                                )),
                          ),
                        ),
                        Visibility(
                          visible: state.voiceType != VoiceTypeEnum.cnTanslated,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              const SizedBox(
                                width: 20,
                              ),
                              GestureDetector(
                                onTap: () {
                                  context.read<VoiceCubit>().stop();
                                },
                                child: Image(
                                  image: AssetImage(argument.theme.themeStyle ==
                                          ThemeStyle.dark
                                      ? "images/chat_close_white2.png"
                                      : "images/chat_close_white.png"),
                                  width: 40,
                                  height: 40,
                                ),
                              ),
                              const SizedBox(
                                width: 20,
                              ),
                              Expanded(
                                child: Container(
                                  height: Platform.isIOS ? 88.w : 72.w,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    color: argument.theme.themeStyle ==
                                            ThemeStyle.dark
                                        ? Colors.white.withOpacity(0.16)
                                        : const Color(0xFF2693FF),
                                  ),
                                  child: TextButton(
                                      onPressed: () {
                                        context.read<VoiceCubit>().action(
                                            context
                                                .read<ExploreChatProvider>());
                                      },
                                      style: const ButtonStyle(
                                        textStyle: MaterialStatePropertyAll(
                                            TextStyle(
                                                fontSize: 20,
                                                fontWeight: FontWeight.w700)),
                                      ),
                                      child: Center(
                                        child: Text(
                                            state.voiceType == VoiceTypeEnum.ch
                                                ? "翻译"
                                                : "发送",
                                            style: style_1_28.copyWith(
                                                color: Colors.white)),
                                      )),
                                ),
                              ),
                              const SizedBox(
                                width: 20,
                              ),
                              GestureDetector(
                                onTap: () {
                                  context.read<VoiceCubit>().stop();
                                  context
                                      .read<VoiceCubit>()
                                      .showTextInput(context);
                                },
                                child: Image(
                                  image: AssetImage(argument.theme.themeStyle ==
                                          ThemeStyle.dark
                                      ? "images/text_edit2.png"
                                      : "images/text_edit.png"),
                                  width: 40,
                                  height: 40,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: Platform.isIOS ? 70.w : 60.w,
                        )
                      ],
                    ),
                  )
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

//   Sky({super.repaint, required this.progress});
//   @override
//   void paint(Canvas canvas, Size size) {
//     Rect rect = Rect.fromLTRB(0, 0, size.width, 44);
//     canvas.drawArc(rect, pi, pi, false, _paint);
//     rect = Rect.fromLTRB(0, 0, size.width, 44 - 2);

//     canvas.drawArc(rect, pi, pi * progress, false, _paint2);
//   }

//   @override
//   bool shouldRepaint(Sky oldDelegate) => true;
//   @override
//   bool shouldRebuildSemantics(Sky oldDelegate) => false;
// }

class TalkAPPBar extends StatelessWidget implements PreferredSizeWidget {
  const TalkAPPBar(
      {super.key,
      required this.argument,
      required this.pop,
      required this.conversationId,
      required this.canCreateReport,
      required this.finishTask,
      required this.totalQuestionCount,
      required this.currentQuestionIndex});
  final ExploreTalkArgument argument;
  final num conversationId;
  final bool canCreateReport;
  final bool finishTask;
  final int totalQuestionCount;
  final int currentQuestionIndex;

  final Function(BuildContext) pop;

  @override
  Size get preferredSize => Size.fromHeight(88.w);

  @override
  Widget build(BuildContext context) {
    return Container(
        color: Colors.transparent,
        child: SafeArea(
          child: Column(
            children: [
              Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  SizedBox(
                    width: 24.w,
                  ),
                  GestureDetector(
                    onTap: () {
                      pop(context);
                    },
                    child: argument.theme.themeStyle == ThemeStyle.dark
                        ? Image.asset(
                            "images/navigation_back.png",
                            width: 24,
                            height: 24,
                            color: Colors.white,
                          )
                        : Image.asset(
                            "images/navigation_back.png",
                            width: 24,
                            height: 24,
                          ),
                  ),
                  ClipRRect(
                    borderRadius: const BorderRadius.all(Radius.circular(20)),
                    child: Image.network(
                      argument.topicData.iconUrl ?? "_",
                      fit: BoxFit.contain,
                      width: 40,
                      height: 40,
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Text(
                    argument.topicData.title ?? "",
                    style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w800,
                        color: argument.theme.themeStyle == ThemeStyle.dark
                            ? Colors.white
                            : const Color(0xFF061B1F)),
                  ),
                  const Spacer(),
                  ShareImageWidget(
                      iconColor: argument.theme.themeStyle == ThemeStyle.dark
                          ? Colors.white
                          : Colors.black,
                      getImageFunc: () => context
                          .read<ExploreChatProvider>()
                          .getShareImageUrls()),
                  SizedBox(width: 16.w),
                  GestureDetector(
                      onTap: () {
                        FocusScope.of(context).unfocus(); // 点击其他地方收起键盘
                        _showSettingAlert(context);
                      },
                      child: Image.asset(
                        "images/chat_setting_bg.png",
                        width: 48.w,
                        height: 48.w,
                        color: argument.theme.themeStyle == ThemeStyle.dark
                            ? Colors.white
                            : Colors.black,
                      )),
                  const SizedBox(
                    width: 17,
                  ),
                ],
              ),
            ],
          ),
        ));
  }

  _showSettingAlert(BuildContext context) {
    showDialog(
        context: context,
        barrierColor: Colors.transparent,
        routeSettings: const RouteSettings(arguments: {"stopPlay": false}),
        builder: (context) {
          return GestureDetector(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.transparent,
                  child: Align(
                      alignment: Alignment.topRight,
                      child: Container(
                        margin: EdgeInsets.only(right: 40.w, top: 170.w),
                        width: 247.w,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(32.w),
                          child: BackdropFilter(
                            filter: ImageFilter.blur(
                                sigmaX:
                                    argument.theme.themeStyle == ThemeStyle.dark
                                        ? 5
                                        : 0,
                                sigmaY:
                                    argument.theme.themeStyle == ThemeStyle.dark
                                        ? 5
                                        : 0),
                            child: GestureDetector(
                              onTap: () {
                                Navigator.pop(context);
                                _showSpeechSetting(
                                    context, argument.theme.themeStyle);
                              },
                              child: Container(
                                color:
                                    argument.theme.themeStyle == ThemeStyle.dark
                                        ? Colors.black.withOpacity(0.5)
                                        : Colors.white,
                                height: 88.w,
                                width: double.infinity,
                                child: Row(
                                  children: [
                                    const Spacer(),
                                    Image.asset(
                                      "images/sppech_setting.png",
                                      width: 36.w,
                                      height: 36.w,
                                      color: argument.theme.themeStyle ==
                                              ThemeStyle.dark
                                          ? Colors.white
                                          : null,
                                    ),
                                    SizedBox(
                                      width: 12.w,
                                    ),
                                    Text(
                                      "语速设置",
                                      style: style_1_28_400.copyWith(
                                        color: argument.theme.themeStyle ==
                                                ThemeStyle.dark
                                            ? Colors.white
                                            : null,
                                      ),
                                    ),
                                    const Spacer(),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ))));
        });
  }

  _showSpeechSetting(BuildContext context, ThemeStyle themeStyle) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      constraints: BoxConstraints(maxWidth: double.infinity, maxHeight: 558.w),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20), topRight: Radius.circular(20))),
      builder: (context) {
        return SpeedSettingsDialog(
          themeStyle: themeStyle,
        );
      },
    );
  }
}
