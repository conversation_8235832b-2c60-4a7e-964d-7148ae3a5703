import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/modules/find/pages/explore_talk_page.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_englist_writing_page.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_translate_page.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_voice_translate_page.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_writing_page.dart';
import 'package:flutter_app_kouyu/modules/find/provider/find_page_provider.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class FindPage extends StatefulWidget {
  const FindPage({super.key});

  @override
  State<StatefulWidget> createState() => _FindPageState();
}

class _FindPageState extends State<FindPage> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<FindPageProvider>(
      create: (_) => FindPageProvider(),
      child: Consumer<FindPageProvider>(
        builder: (context, model, child) {
          return Stack(children: [
            Container(
              width: double.infinity,
              height: double.infinity,
              color: const Color(0xFFFFFFFF),
            ),
            Image.asset(
                "images/scene_study_bg.png",
                width: double.infinity,
                fit: BoxFit.fill,
                alignment: Alignment.topCenter,
              ),
            Scaffold(
              backgroundColor: Colors.transparent,
              appBar: AppBar(
                leadingWidth: 300,
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: CustomAppbar.leftWidgetWithNoIcon(context, text: "探索"),
              ),
              floatingActionButtonLocation:
                  FloatingActionButtonLocation.centerDocked,
              body: Padding(
                  padding: EdgeInsets.only(top: 16.w),
                  child: Column(children: [
                    Row(
                      children: [
                        _toolWidget(context, ToolType.writing),
                        _toolWidget(context, ToolType.english_writing),
                      ],
                    ),
                    SizedBox(height: 20.w),
                    Row(
                      children: [
                        _toolWidget(context, ToolType.translation),
                        _toolWidget(
                            context, ToolType.simultaneous_interpretation),
                      ],
                    ),
                    SizedBox(height: 40.w),
                    Padding(
                      padding: EdgeInsets.only(left: 32.w),
                      child: Row(
                        children: topicTaps(model),
                      ),
                    ),
                    SizedBox(height: 24.w),
                    Expanded(
                      child: ListView(
                        shrinkWrap: true,
                        // physics: NeverScrollableScrollPhysics(),
                        children: _listItemWidget(context, model),
                      ),
                    )
                  ])),
            ),
          ]);
        },
      ),
    );
  }
}

Widget _toolWidget(BuildContext context, ToolType toolType) {
  // 根据不同类型显示不同底色
  Color getColor() {
    switch (toolType) {
      case ToolType.writing:
        return const Color.fromRGBO(128, 65, 255, 0.12);
      case ToolType.english_writing:
        return const Color.fromRGBO(255, 65, 192, 0.12);
      case ToolType.translation:
        return const Color.fromRGBO(65, 128, 255, 0.12);
      case ToolType.simultaneous_interpretation:
        return const Color.fromRGBO(97, 65, 255, 0.12);
      default:
        return const Color.fromRGBO(65, 192, 255, 0.12);
    }
  }

  return Container(
    width: 325.w,
    height: 140.w,
    margin: EdgeInsets.fromLTRB(32.w, 0.w, 0.w, 0),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(24.w),
      color: getColor(),
    ),
    child: GestureDetector(
      onTap: () {
        switch (toolType) {
          case ToolType.writing:
            Navigator.of(context).push(ToolWritingPage.route());
            break;
          case ToolType.english_writing:
            Navigator.of(context).push(ToolEnglistWritingPage.route());
            break;
          case ToolType.translation:
            Navigator.of(context).push(ToolTranslatePage.route());
            break;
          case ToolType.simultaneous_interpretation:
            Navigator.of(context).push(ToolVoiceTranslatePage.route());
            break;
        }
      },
      child: Row(
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(32.w, 32.w, 0.w, 0.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  toolType.getToolName(),
                  style: style_1_28,
                ),
                Text(
                  toolType.getDesc(),
                  style: style_2_20,
                ),
              ],
            ),
          ),
          const Spacer(),
          toolType.getIcon()
        ],
      ),
    ),
  );
}

List<Widget> topicTaps(FindPageProvider model) {
  var list = <Widget>[];

  for (var element in model.typeList) {
    list.add(GestureDetector(
      onTap: () {
        model.selectTypeTab = element.key;
      },
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          if (model.selectTypeTab == element.key)
            Image.asset(
              "images/text_bg.png",
              width: 71.w,
              height: 20.w,
            ),
          Text(
            element.title,
            style: model.selectTypeTab == element.key ? style_1_36 : style_2_34,
          ),
        ],
      ),
    ));
    list.add(SizedBox(
      width: 40.w,
    ));
  }
  return list;
}

List<Widget> _listItemWidget(BuildContext context, FindPageProvider model) {
  var list = <Widget>[];
  for (var item in model.exploreTopicListModel?.data ?? []) {
    list.add(GestureDetector(
      onTap: () async {
        Navigator.push(
          context,
          ExploreTalkPage.route(
            argument: ExploreTalkArgument(
              topicData: item,
            ),
          ),
        );
      },
      child: Container(
          padding: EdgeInsets.fromLTRB(20.w, 32.w, 32.w, 32.w),
          margin: EdgeInsets.fromLTRB(32.w, 0.w, 32.w, 16.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24.w),
          ),
          child: Row(children: [
            CachedNetworkImage(
              imageUrl: item.iconUrl!,
              width: 90.w,
              height: 90.w,
            ),
            SizedBox(
              width: 16.w,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.title!,
                    style: style_1_28,
                  ),
                  Text(
                    item.description!,
                    style: style_2_20,
                  ),
                ],
              ),
            ),
            SizedBox(
              width: 16.w,
            ),
            Image.asset(
              "images/right_arrow_gray.png",
              width: 36.w,
              height: 36.w,
            ),
          ])),
    ));
    separatorBuilder:
    (context, index) {
      return SizedBox(
        height: 16.w,
      );
    };
  }
  return list;
}

enum ToolType {
  writing,
  english_writing,
  translation,
  simultaneous_interpretation
}

extension ToolTypeExtension on ToolType {
  String getToolName() {
    switch (this) {
      case ToolType.writing:
        return "写作批改";
      case ToolType.english_writing:
        return "英文写作";

      case ToolType.translation:
        return "随身翻译";

      case ToolType.simultaneous_interpretation:
        return "同声传译";
    }
  }

  String getDesc() {
    switch (this) {
      case ToolType.writing:
        return "文章优化指南";
      case ToolType.english_writing:
        return "英语创作之旅";

      case ToolType.translation:
        return "便捷翻译伙伴";
      case ToolType.simultaneous_interpretation:
        return "实时传译精技";
    }
  }

  Image getIcon() {
    switch (this) {
      case ToolType.writing:
        return Image.asset(
          "images/tool_writing.png",
          width: 132.w,
          height: 132.w,
        );
      case ToolType.english_writing:
        return Image.asset(
          "images/tool_english_writing.png",
          width: 132.w,
          height: 132.w,
        );
      case ToolType.translation:
        return Image.asset(
          "images/tool_translation.png",
          width: 132.w,
          height: 132.w,
        );
      case ToolType.simultaneous_interpretation:
        return Image.asset(
          "images/tool_simultaneous_interpretation.png",
          width: 132.w,
          height: 132.w,
        );
    }
  }
}
