import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/modules/find/provider/tool_english_writing_page_provider.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/selected_text.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class ToolEnglishWritingSubmitPage extends StatefulWidget {
  final String compositionType;
  final String directions;
  final String title;
  final String wordCount;

  ToolEnglishWritingSubmitPage({
    super.key,
    required this.compositionType,
    required this.title,
    required this.wordCount,
    required this.directions,
  });

  static Route<void> route(String compositionType, String title,
      String wordCount, String directions) {
    return MaterialPageRoute<void>(
        builder: (_) => ToolEnglishWritingSubmitPage(
              compositionType: compositionType,
              title: title,
              wordCount: wordCount,
              directions: directions,
            ));
  }

  @override
  _ToolEnglishWritingSubmitPageState createState() =>
      _ToolEnglishWritingSubmitPageState();
}

class _ToolEnglishWritingSubmitPageState
    extends State<ToolEnglishWritingSubmitPage> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<ToolEnglishWritingPageProvider>(
      create: (_) {
        var provider = ToolEnglishWritingPageProvider();
        provider.title = widget.title;
        provider.compositionType = widget.compositionType;
        provider.directions = widget.directions;
        provider.wordCount = widget.wordCount;
        return provider..submit();
      },
      child: Consumer<ToolEnglishWritingPageProvider>(
        builder: (context, model, child) {
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: Stack(
              children: [
                Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.white,
                ),
                // Image.asset(
                //   "images/my_bg.png",
                //   width: double.infinity,
                // ),
                Scaffold(
                  backgroundColor: Colors.transparent,
                  appBar: AppBar(
                    leadingWidth: 300,
                    elevation: 0,
                    leading: CustomAppbar.leftWidget(context, text: "英文写作"),
                  ),
                  floatingActionButtonLocation:
                      FloatingActionButtonLocation.centerDocked,
                  body: Padding(
                      padding: EdgeInsets.only(top: 32.w),
                      child: Column(
                        children: [
                          Container(
                            width: double.infinity,
                            height: 1200.w,
                            // margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
                            // child: Text(
                            //   model.streamText ?? "",
                            //   style: style_1_32,
                            // ),
                            child: Scrollbar(
                              thumbVisibility: true,
                              child: ListView(
                                children: [
                                  Container(
                                    margin: EdgeInsets.fromLTRB(
                                        24.w, 24.w, 24.w, 0),
                                    child: SelectableTextUtil.editableText(
                                        context,
                                        text: model.accumulatedText ?? '',
                                        style: style_1_32_400),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const Spacer(),
                          Align(
                            alignment: Alignment.bottomCenter,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                              ),
                              child: Row(
                                children: [
                                  _buttomLeftButtonWidget(context, model),
                                  _buttomRightButtonWidget(
                                      context, model.accumulatedText ?? ""),
                                ],
                              ),
                            ),
                          )
                        ],
                      )),
                )
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buttomLeftButtonWidget(
      BuildContext context, ToolEnglishWritingPageProvider provider) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();

          provider.submit();
        },
        child: Container(
          width: 670.w,
          height: 102.w,
          margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 32.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: const Color(0xFF2693FF),
          ),
          child: Center(
              child: Text(
            "重新生成",
            style: style_1_32.copyWith(color: Colors.white),
          )),
        ),
      ),
    );
  }

  Widget _buttomRightButtonWidget(BuildContext context, String text) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
          Clipboard.setData(ClipboardData(text: text));
          EasyLoading.showToast("已复制");
        },
        child: Container(
          width: 670.w,
          height: 102.w,
          margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 32.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: const Color(0xFF2693FF),
          ),
          child: Center(
              child: Text(
            "复制全文",
            style: style_1_32.copyWith(color: Colors.white),
          )),
        ),
      ),
    );
  }
}
