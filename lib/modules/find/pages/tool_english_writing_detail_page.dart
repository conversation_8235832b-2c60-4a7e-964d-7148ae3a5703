import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/modules/find/provider/tool_english_writing_page_provider.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/selected_text.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class ToolEnglishWritingDetailPage extends StatefulWidget {
  final int id;

  ToolEnglishWritingDetailPage({
    super.key,
    required this.id,
  });

  static Route<void> route(int id) {
    return MaterialPageRoute<void>(
        builder: (_) => ToolEnglishWritingDetailPage(id: id));
  }

  @override
  _ToolEnglishWritingDetailPageState createState() =>
      _ToolEnglishWritingDetailPageState();
}

class _ToolEnglishWritingDetailPageState
    extends State<ToolEnglishWritingDetailPage> {
  static Route<void> route(int id) {
    return MaterialPageRoute<void>(
        builder: (_) => ToolEnglishWritingDetailPage(id: id));
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<ToolEnglishWritingPageProvider>(
      create: (_) => ToolEnglishWritingPageProvider()..detail(widget.id),
      child: Consumer<ToolEnglishWritingPageProvider>(
        builder: (context, model, child) {
          return Stack(
            children: [
              Container(
                width: double.infinity,
                height: double.infinity,
                color: const Color(0xFFF3FBFD),
              ),
              Image.asset(
                "images/my_bg.png",
                width: double.infinity,
              ),
              Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  leadingWidth: 300,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  leading: CustomAppbar.leftWidget(context, text: "英文写作"),
                ),
                floatingActionButtonLocation:
                    FloatingActionButtonLocation.centerDocked,
                body: Padding(
                    padding: EdgeInsets.only(top: 32.w),
                    child: Column(
                      children: [
                        Container(
                          width: double.infinity,
                          height: 1200.w,
                          // margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
                          // child: Text(
                          //   model.streamText ?? "",
                          //   style: style_1_32,
                          // ),
                          child: Scrollbar(
                            thumbVisibility: true,
                            child: ListView(
                              children: [
                                Container(
                                  margin:
                                      EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
                                  child: SelectableTextUtil.editableText(
                                      context,
                                      text: model
                                              .toolUserEnglishWritingDetailModel
                                              ?.data
                                              ?.aiText ??
                                          '',
                                      style: style_1_32_400),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const Spacer(),
                        Container(
                          decoration: BoxDecoration(
                            color: Color(0xFFF0F8FA),
                          ),
                          child: Row(
                            children: [
                              _buttomButtonWidget(
                                  context,
                                  model.toolUserEnglishWritingDetailModel?.data!
                                          .aiText ??
                                      ''),
                            ],
                          ),
                        )
                      ],
                    )),
              )
            ],
          );
        },
      ),
    );
  }

  Widget _buttomButtonWidget(BuildContext context, String text) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          Clipboard.setData(ClipboardData(text: text));
          EasyLoading.showToast("已复制");
        },
        child: Container(
          width: 670.w,
          height: 102.w,
          margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 32.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: const Color(0xFF2693FF),
          ),
          child: Center(
              child: Text(
            "复制全文",
            style: style_1_32.copyWith(color: Colors.white),
          )),
        ),
      ),
    );
  }
}
