import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_english_writing_recods_model/recods.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_english_writing_detail_page.dart';
import 'package:flutter_app_kouyu/modules/find/provider/tool_english_writing_page_provider.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ToolEnglishWritingRecodsPage extends StatefulWidget {
  ToolEnglishWritingRecodsPage({
    super.key,
  });

  static Route<void> route() {
    return MaterialPageRoute<void>(
        builder: (_) => ToolEnglishWritingRecodsPage());
  }

  @override
  _ToolEnglishWritingRecodsPageState createState() =>
      _ToolEnglishWritingRecodsPageState();
}

class _ToolEnglishWritingRecodsPageState
    extends State<ToolEnglishWritingRecodsPage> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<ToolEnglishWritingPageProvider>(
      create: (_) => ToolEnglishWritingPageProvider()..recods(),
      child: Consumer<ToolEnglishWritingPageProvider>(
        builder: (context, model, child) {
          return Stack(
            children: [
              Container(
                width: double.infinity,
                height: double.infinity,
                // color: const Color(0xFFF3FBFD),
                color: const Color(0xFFE9F5F7),
              ),
              Image.asset(
                "images/my_bg.png",
                width: double.infinity,
              ),
              Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  leadingWidth: 300,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  leading: CustomAppbar.leftWidget(context, text: "英文写作"),
                ),
                floatingActionButtonLocation:
                    FloatingActionButtonLocation.centerDocked,
                body: Padding(
                    padding: EdgeInsets.only(top: 10.w),
                    child: SmartRefresher(
                      controller: model.refreshController,
                      enablePullUp: true,
                      enablePullDown: false,
                      onLoading: () {
                        model.recodsLoadMore();
                      },
                      child: CustomScrollView(slivers: [
                        SliverList.separated(
                          itemBuilder: (_, index) {
                            Recods? recods = model
                                .toolUserEnglishWritingRecodsModel
                                ?.data
                                ?.recods![index];
                            return _listItemWidget(context, recods!);
                          },
                          separatorBuilder: (context, index) {
                            return SizedBox(
                              height: 16.w,
                            );
                          },
                          itemCount: model.toolUserEnglishWritingRecodsModel
                                  ?.data?.recods?.length ??
                              0,
                        ),
                      ]),
                    )),
              )
            ],
          );
        },
      ),
    );
  }

  Widget _listItemWidget(BuildContext context, Recods recod) {
    // IeltsListProvider provider = context.read<IeltsListProvider>();

    return GestureDetector(
      onTap: () async {
        Navigator.of(context)
            .push(ToolEnglishWritingDetailPage.route(recod.id ?? 0));
      },
      child: _itemWidget(recod),
    );
  }

  Container _itemWidget(Recods recod) {
    return Container(
      padding: EdgeInsets.all(16.w),
      margin: EdgeInsets.fromLTRB(40.w, 0, 40.w, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        // color: const Color(0xFFF3FBFD),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                recod.createTime ?? '',
                style: style_3_24,
              ),
            ],
          ),
          SizedBox(
            height: 18.w,
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  recod.aiText ?? '',
                  style: style_1_24,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 18.w,
          ),
          Row(
            children: [
              Text(
                '查看详情',
                style: style_2_24,
              ),
              SizedBox(
                width: 10.w,
              ),
              Image.asset(
                "images/arrow_right_black.png",
                width: 10.w,
                height: 18.w,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
