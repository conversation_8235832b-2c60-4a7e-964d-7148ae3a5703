import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_try_times_model/tool_user_try_times_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_writing_page_model/recods.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/modules/find/provider/tool_user_writing_detail_page_provider.dart';
import 'package:flutter_app_kouyu/modules/find/provider/tool_user_writing_optimization_page_provider.dart';
import 'package:flutter_app_kouyu/modules/find/provider/tool_user_writing_page_provider.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielt_prescription_page.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/overlay_manager.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:path/path.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ToolWritingOptimizationPage extends StatefulWidget {
  final int id;

  ToolWritingOptimizationPage({
    super.key,
    required this.id,
  });

  static Route<void> route(int id) {
    return MaterialPageRoute<void>(
        builder: (_) => ToolWritingOptimizationPage(id: id));
  }

  @override
  _ToolWritingOptimizationPageState createState() =>
      _ToolWritingOptimizationPageState();
}

class _ToolWritingOptimizationPageState
    extends State<ToolWritingOptimizationPage> {
  static Route<void> route(int id) {
    return MaterialPageRoute<void>(
        builder: (_) => ToolWritingOptimizationPage(id: id));
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<ToolUserWritingOptimizationPageProvider>(
      create: (_) =>
          ToolUserWritingOptimizationPageProvider()..loadData(widget.id),
      child: Consumer<ToolUserWritingOptimizationPageProvider>(
        builder: (context, model, child) {
          return Stack(
            children: [
              Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.white,
              ),
              // Image.asset(
              //   "images/my_bg.png",
              //   width: double.infinity,
              // ),
              Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  leadingWidth: 300,
                  elevation: 0,
                  leading: CustomAppbar.leftWidget(context, text: "优化结果"),
                ),
                floatingActionButtonLocation:
                    FloatingActionButtonLocation.centerDocked,
                body: Padding(
                    padding: EdgeInsets.only(top: 32.w),
                    child: Column(
                      children: [
                        Container(
                          width: double.infinity,
                          height: 1200.w,
                          // margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
                          // child: Text(
                          //   model.streamText ?? "",
                          //   style: style_1_32,
                          // ),
                          child: Scrollbar(
                            thumbVisibility: true,
                            child: Container(
                              margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
                              child: ListView(
                                children: [
                                  Text(model.accumulatedText ?? '',
                                      style: style_1_32_400),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const Spacer(),
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                            ),
                            child: Row(
                              children: [
                                _buttomButtonWidget(
                                    context, model.accumulatedText ?? ""),
                              ],
                            ),
                          ),
                        )
                      ],
                    )),
              )
            ],
          );
        },
      ),
    );
  }

  Widget _buttomButtonWidget(BuildContext context, String text) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          Clipboard.setData(ClipboardData(text: text));
          EasyLoading.showToast("已复制");
        },
        child: Container(
          width: 670.w,
          height: 102.w,
          margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 32.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: const Color(0xFF2693FF),
          ),
          child: Center(
              child: Text(
            "复制全文",
            style: style_1_32.copyWith(color: Colors.white),
          )),
        ),
      ),
    );
  }
}
