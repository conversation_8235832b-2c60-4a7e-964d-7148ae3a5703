import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_try_times_model/tool_user_try_times_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_writing_detail_model/tool_user_writing_detail_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_writing_page_model/recods.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_writing_optimization_page.dart';
import 'package:flutter_app_kouyu/modules/find/provider/tool_user_writing_detail_page_provider.dart';
import 'package:flutter_app_kouyu/modules/find/provider/tool_user_writing_page_provider.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielt_prescription_page.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/overlay_manager.dart';
import 'package:flutter_app_kouyu/widgets/selected_text.dart';
import 'package:flutter_app_kouyu/widgets/vip_spical_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:path/path.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ToolWritingDetailPage extends StatefulWidget {
  late int id;
  double _dividerPosition = 0.5;
  double _totalHeight = 0;

  ToolWritingDetailPage({
    super.key,
    required this.id,
  });
  @override
  _ToolEnglistWritingPageState createState() => _ToolEnglistWritingPageState();

  static Route<void> route(int id) {
    return MaterialPageRoute<void>(
        builder: (_) => ToolWritingDetailPage(id: id));
  }
}

class _ToolEnglistWritingPageState extends State<ToolWritingDetailPage> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<ToolUserWritingPageProvider>(
      create: (_) => ToolUserWritingPageProvider()
        ..checkIsVip()
        ..tryTimes()
        ..getUserCompositionAssessmentDetail(widget.id),
      child: Consumer<ToolUserWritingPageProvider>(
        builder: (context, model, child) {
          return Stack(
            children: [
              Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.white,
              ),
              Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  leadingWidth: 300,
                  elevation: 0,
                  leading: CustomAppbar.leftWidget(context, text: "修改建议"),
                ),
                floatingActionButtonLocation:
                    FloatingActionButtonLocation.centerDocked,
                body: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    FocusScope.of(context).unfocus();
                  },
                  child: Padding(
                      padding: EdgeInsets.only(top: 32.w),
                      child: LayoutBuilder(builder: (context, constraints) {
                        widget._totalHeight = constraints.maxHeight;

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _headWidget(context, model),
                            Container(
                              margin:
                                  EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 24.w),
                              child: Text(
                                "原文",
                                style: style_1_24,
                              ),
                            ),
                            Expanded(
                              flex: (widget._dividerPosition * 10).toInt(),
                              child: Scrollbar(
                                thumbVisibility: true,
                                child: ListView(primary: false, children: [
                                  Container(
                                    margin: EdgeInsets.fromLTRB(
                                        24.w, 24.w, 24.w, 0),
                                    child: SelectableTextUtil.editableText(
                                      context,
                                      text: model.toolUserWritingDetailModel
                                              ?.data?.originalText ??
                                          "",
                                      style: style_1_32_400,
                                    ),
                                  )
                                ]),
                              ),
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: Color(0xFFF0F8FA),
                                borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(24.w),
                                    topRight: Radius.circular(24.w)),
                              ),
                              child: Row(
                                children: [
                                  GestureDetector(
                                    onVerticalDragUpdate: (details) {
                                      setState(() {
                                        widget._dividerPosition +=
                                            details.delta.dy /
                                                widget._totalHeight;
                                        widget._dividerPosition =
                                            widget._dividerPosition.clamp(0, 1);
                                      });
                                    },
                                    child: Container(
                                      margin: EdgeInsets.fromLTRB(
                                          24.w, 70.w, 24.w, 24.w),
                                      child: Text(
                                        "修改建议",
                                        style: style_1_40,
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    width: 100.w,
                                  ),
                                  GestureDetector(
                                    onVerticalDragUpdate: (details) {
                                      setState(() {
                                        widget._dividerPosition +=
                                            details.delta.dy /
                                                widget._totalHeight;
                                        widget._dividerPosition =
                                            widget._dividerPosition.clamp(0, 1);
                                      });
                                    },
                                    child: Container(
                                        alignment: Alignment.topRight,
                                        width: 64.w,
                                        height: 10.w,
                                        margin: EdgeInsets.fromLTRB(
                                            24.w, 0.w, 24.w, 24.w),
                                        decoration: BoxDecoration(
                                          color: Color(0xFFDDEDF0),
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(8.w)),
                                        )),
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              flex:
                                  ((1 - widget._dividerPosition) * 10).toInt(),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Color(0xFFF0F8FA),
                                  // borderRadius: BorderRadius.only(
                                  //     topLeft: Radius.circular(24.w),
                                  //     topRight: Radius.circular(24.w)),
                                ),
                                child: Scrollbar(
                                  thumbVisibility: true,
                                  child: ListView(children: [
                                    _suggestWidget(context, model),
                                  ]),
                                ),
                              ),
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: Color(0xFFF0F8FA),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [_buttomButtonWidget(context, model)],
                              ),
                            )
                          ],
                        );
                      })),
                ),
              )
            ],
          );
        },
      ),
    );
  }

  Widget _headWidget(BuildContext context, ToolUserWritingPageProvider model) {
    return Container(
      padding: EdgeInsets.all(24.w),
      margin: EdgeInsets.fromLTRB(24.w, 0, 24.w, 0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24.w),
        color: Color(0xFFF0F8FA),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _scoreWidget(
              "词汇分",
              model.toolUserWritingDetailModel?.data?.vocabulary.toString() ??
                  "0"),
          SizedBox(width: 80.w),
          _scoreWidget(
              "句子分",
              model.toolUserWritingDetailModel?.data?.sentence.toString() ??
                  "0"),
          SizedBox(width: 90.w),
          _scoreWidget(
              "结构分",
              model.toolUserWritingDetailModel?.data?.structure.toString() ??
                  "0"),
          SizedBox(width: 90.w),
          _scoreWidget(
              "内容分",
              model.toolUserWritingDetailModel?.data?.content.toString() ??
                  "0"),
        ],
      ),
    );
  }

  Widget _scoreWidget(String title, String score) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          score,
          style: style_1_40,
        ),
        Text(
          title,
          style: style_1_24,
        ),
      ],
    );
  }

  Widget _suggestWidget(
      BuildContext context, ToolUserWritingPageProvider model) {
    return Container(
      margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          for (var i = 0;
              i <
                  (model.toolUserWritingDetailModel?.data?.amendments?.length ??
                      0);
              i++)
            Text(
              (i + 1).toString() +
                  "." +
                  (model.toolUserWritingDetailModel?.data?.amendments?[i] ??
                      ""),
              style: style_1_32_400,
            ),
          SizedBox(height: 24.w),
          Text(
            "老师总结: " + (model.toolUserWritingDetailModel?.data?.summary ?? ""),
            style: style_1_32_400,
          ),
        ],
      ),
    );
  }

  Widget _buttomButtonWidget(
      BuildContext context, ToolUserWritingPageProvider provider) {
    int id = provider.toolUserWritingDetailModel?.data!.id ?? 0;
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();

        int num = provider
                .toolUserTryTimesModel?.data?.toolTryTimesCritiqueOfWriting ??
            0;
        bool isVip = provider.isVip;
        if (num <= 0 && !isVip) {
          //体验到期
          OverlayManager.getInstance().showOverlay(
            true,
            builder: (close) => VipSpicalWidget(
              close: close,
            ),
          );
          return;
        }
        Navigator.of(context).push(ToolWritingOptimizationPage.route(id));
      },
      child: Container(
        width: 670.w,
        height: 102.w,
        margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 32.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: const Color(0xFF2693FF),
        ),
        child: Center(
            child: Text(
          "一键优化",
          style: style_1_32.copyWith(color: Colors.white),
        )),
      ),
    );
  }
}
