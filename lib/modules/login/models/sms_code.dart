import 'package:formz/formz.dart';

enum SmsCodeValidationError {
  empty('验证码不能为空'),
  ;

  final String desc;

  const SmsCodeValidationError(this.desc);
}

class SmsCode extends FormzInput<String, SmsCodeValidationError> {
  const SmsCode.pure() : super.pure('');
  const SmsCode.dirty([super.value = '']) : super.dirty();

  @override
  SmsCodeValidationError? validator(String value) {
    if (value.isEmpty) {
      return SmsCodeValidationError.empty;
    }
    return null;
  }
}
