import 'package:formz/formz.dart';

enum UserPhoneValidationError {
  empty('手机号不能为空'),
  lengthError('手机号长度不对');

  final String desc;

  const UserPhoneValidationError(this.desc);
}

class UserPhone extends FormzInput<String, UserPhoneValidationError> {
  const UserPhone.pure() : super.pure('');
  const UserPhone.dirty([super.value = '']) : super.dirty();

  @override
  UserPhoneValidationError? validator(String value) {
    if (value.isEmpty) {
      return UserPhoneValidationError.empty;
    }
    if (value.length != 11) {
      return UserPhoneValidationError.lengthError;
    }

    return null;
  }
}
