import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/login/bloc/login_bloc.dart';
import 'package:flutter_app_kouyu/widgets/weview_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:formz/formz.dart';
import 'dart:io';

class LoginForm extends StatelessWidget {
  final TextEditingController _phoneTextField = TextEditingController();
  final TextEditingController _codeTextField = TextEditingController();

  LoginForm({super.key});
  @override
  Widget build(BuildContext context) {
    return BlocListener<LoginBloc, LoginState>(
      listener: (context, state) {
        if (state.status.isInitial) {
          EasyLoading.dismiss();
        }
        if (state.status.isFailure) {
          EasyLoading.showError("登录失败");
        }
        if (state.status.isInProgress) {
          EasyLoading.show(status: "登录中");
        }
        if (state.status.isSuccess) {
          EasyLoading.showSuccess("登录成功");
          if (state.loginInfoModel?.data?.enterTheProcess == true) {
            //去设置
            Navigator.of(context).pushNamedAndRemoveUntil(
                "/setup_setting_one", (route) => false);
          } else {
            LoginUtil.checkAppVersion = true;
            Navigator.of(context)
                .pushNamedAndRemoveUntil("/home", (route) => false);
          }
        }
      },
      child: Column(
        children: [
          Expanded(child: _body()),
          _protocolWidget(context),
          const SizedBox(
            height: 40,
          )
        ],
      ),
    );
  }

  RichText _protocolWidget(BuildContext context) {
    return RichText(
        text: TextSpan(text: "", children: [
      // WidgetSpan(
      //     alignment: PlaceholderAlignment.middle,
      //     child: Checkbox(
      //       value: true,
      //       shape: const CircleBorder(),
      //       onChanged: (value) {},
      //     )),
      const TextSpan(
        text: "登录即表示同意",
        style: TextStyle(
            color: Color(0xFFA3C5CC),
            fontSize: 12,
            fontWeight: FontWeight.w400),
      ),
      TextSpan(
          style: const TextStyle(
              color: Color(0xFF41C0FF),
              fontSize: 12,
              fontWeight: FontWeight.w400),
          text: '《用户服务协议》',
          recognizer: TapGestureRecognizer()
            ..onTap = () {
              Navigator.push(context, MaterialPageRoute(builder: (context) {
                return const WebviewWidget(
                  url:
                      "https://cos.xinquai.com/agreements_and_notes/ios_Membership_Agreement.html",
                );
              }));
            }),
      const TextSpan(
          style: TextStyle(
              color: Color(0xFFA3C5CC),
              fontSize: 12,
              fontWeight: FontWeight.w400),
          text: "及"),
      TextSpan(
          style: const TextStyle(
              color: Color(0xFF41C0FF),
              fontSize: 12,
              fontWeight: FontWeight.w400),
          text: "《隐私声明》",
          recognizer: TapGestureRecognizer()
            ..onTap = () {
              Navigator.push(context, MaterialPageRoute(builder: (context) {
                return const WebviewWidget(
                  url:
                      "https://cos.xinquai.com/agreements_and_notes/ios_privacy_agreement.html",
                );
              }));
            }),
    ]));
  }

  Widget _body() {
    return BlocBuilder<LoginBloc, LoginState>(builder: (context, state) {
      return Column(children: [
        // const Image(
        //   image: AssetImage("images/stage.png"),
        //   fit: BoxFit.fitHeight,
        // ),
        GestureDetector(
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: SizedBox(
            height: 280,
            child: Center(
              child: Stack(
                alignment: Alignment.topCenter,
                children: [
                  Image.asset(
                    "images/login_bg.png",
                    fit: BoxFit.fill,
                    width: double.infinity,
                    height: double.infinity,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: Image.asset(
                          fit: BoxFit.fill,
                          "images/icon_logo.png",
                          width: 100,
                          height: 100,
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      const Text(
                        "小西口语",
                        style: TextStyle(
                            fontWeight: FontWeight.w700,
                            fontSize: 20,
                            color: Color(0xFF333333)),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        ClipRRect(
            borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20)),
            child: Container(
              color: Colors.white,
              width: double.infinity,
              padding: const EdgeInsets.all(30),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "未注册手机号验证后自动创建账号",
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontSize: Platform.isIOS ? 15 : 12,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF646E70)),
                  ),
                  const SizedBox(height: 12),
                  Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: const Color(0xFFF3FBFD),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: TextField(
                                key: const Key('loginForm_phone_textField'),
                                controller: _phoneTextField,
                                onChanged: (phone) => context
                                    .read<LoginBloc>()
                                    .add(LoginPhoneChanged(phone)),
                                textAlign: TextAlign.left,
                                keyboardType: TextInputType.number,
                                style: TextStyle(
                                    fontWeight: FontWeight.w800,
                                    fontSize: Platform.isIOS ? 18 : 14,
                                    color: Color(0xFF061B1F)),
                                decoration: InputDecoration(
                                    icon: Text(
                                      "+86",
                                      style: TextStyle(
                                          fontWeight: FontWeight.w800,
                                          fontSize: Platform.isIOS ? 18 : 14,
                                          color: Color(0xFF061B1F)),
                                    ),
                                    hintText: "输入手机号",
                                    suffixIcon: Offstage(
                                      offstage: state.phone.value.isEmpty,
                                      child: IconButton(
                                        icon: Image.asset(
                                          'images/tf_icon_delete.png',
                                          width: 17,
                                          height: 17,
                                        ),
                                        onPressed: () {
                                          // 清除文本
                                          _phoneTextField.clear();
                                          context
                                              .read<LoginBloc>()
                                              .add(const LoginPhoneChanged(''));
                                        },
                                      ),
                                    ),
                                    border: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    hintStyle: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontSize: Platform.isIOS ? 18 : 14,
                                        color: Color(0xFF8BA2A6))),
                              ),
                            ),
                          ],
                        ),
                      )),
                  const SizedBox(height: 12),
                  Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: const Color(0xFFF3FBFD),
                      ),
                      child: Row(
                        children: [
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextField(
                              key: const Key('loginForm_code_textField'),
                              onChanged: (value) {
                                context
                                    .read<LoginBloc>()
                                    .add(SmsCodeChanged(value));
                                if (value.length >= 6) {
                                  FocusScope.of(context)
                                      .requestFocus(FocusNode());
                                }
                              },
                              textAlign: TextAlign.left,
                              keyboardType: TextInputType.number,
                              controller: _codeTextField,
                              style: TextStyle(
                                  fontWeight: FontWeight.w800,
                                  fontSize: Platform.isIOS ? 18 : 14,
                                  color: Color(0xFF061B1F)),
                              decoration: InputDecoration(
                                  hintText: "输入验证码",
                                  border: InputBorder.none,
                                  suffixIcon: Offstage(
                                    offstage: state.smsCode.value.isEmpty,
                                    child: IconButton(
                                      icon: Image.asset(
                                        'images/tf_icon_delete.png',
                                        width: 17,
                                        height: 17,
                                      ),
                                      onPressed: () {
                                        // 清除文本
                                        _codeTextField.text = '';
                                        context
                                            .read<LoginBloc>()
                                            .add(const SmsCodeChanged(''));
                                      },
                                    ),
                                  ),
                                  hintStyle: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontSize: Platform.isIOS ? 18 : 14,
                                      color: Color(0xFF8BA2A6))),
                            ),
                          ),
                          _SmsButton(
                            phone: state.phone.value,
                          ),
                          const SizedBox(width: 16),
                        ],
                      )),
                  const SizedBox(height: 20),
                  Container(
                    height: 52,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: state.isValid
                          ? const Color(0xFF2693FF)
                          : const Color(0xFF2693FF).withOpacity(0.4),
                    ),
                    child: TextButton(
                        style: const ButtonStyle(
                          textStyle: MaterialStatePropertyAll(TextStyle(
                              fontSize: 20, fontWeight: FontWeight.w700)),
                        ),
                        onPressed: () {
                          context.read<LoginBloc>().add(const LoginSubmitted());
                        },
                        child: Center(
                          child: Text(
                            "立即登录",
                            style: state.isValid
                                ? const TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w800)
                                : const TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w800),
                          ),
                        )),
                  ),
                ],
              ),
            ))
      ]);
    });
  }
}

class _SmsButton extends StatefulWidget {
  final String? phone;
  const _SmsButton({required this.phone});
  @override
  _SmsButtonState createState() => _SmsButtonState();
}

class _SmsButtonState extends State<_SmsButton> {
  String _buttonText = '发送验证码';
  int _count = 0;
  bool _buttonEnabled = true;
  Timer? _timer;
  void _startCountdown() {
    setState(() {
      _buttonEnabled = false;
      _buttonText = '60s'; // 初始文本为60s
    });

    _count = 60;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _count--;
        if (_count > 0) {
          _buttonText = '$_count s'; // 更新按钮文本为倒计时数字
        } else {
          _count = 0;
          _buttonText = '发送验证码';
          _buttonEnabled = true;
          timer.cancel(); // 取消定时器
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.phone != null && widget.phone!.length == 11 && _count == 0) {
      _buttonEnabled = true;
    } else {
      _buttonEnabled = false;
    }

    TextStyle textStyle = const TextStyle(
        color: Colors.grey, fontWeight: FontWeight.w400, fontSize: 14);
    if (_buttonEnabled) {
      textStyle = textStyle.copyWith(color: const Color(0xFF41C0FF));
    } else {
      if (_count > 0) {
        textStyle = textStyle.copyWith(color: const Color(0xFF061B1F));
      }
    }

    return GestureDetector(
      onTap: () {
        if (_buttonEnabled) {
          Api.smsSend({"phone": widget.phone}).then((value) {
            EasyLoading.showToast("验证码发送成功");
            _startCountdown();
          });
        }
      },
      child: Text(_buttonText, style: textStyle),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
