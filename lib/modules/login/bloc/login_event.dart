part of 'login_bloc.dart';

sealed class LoginEvent extends Equatable {
  const LoginEvent();

  @override
  List<Object?> get props => [];
}

final class LoginPhoneChanged extends LoginEvent {
  const LoginPhoneChanged(this.phone);
  final String phone;
  @override
  List<Object?> get props => [phone];
}

final class SmsCodeChanged extends LoginEvent {
  const SmsCodeChanged(this.code);
  final String code;
  @override
  List<Object?> get props => [code];
}

final class LoginSubmitted extends LoginEvent {
  const LoginSubmitted();
}

final class VisitorLoginSubmitted extends LoginEvent {
  const VisitorLoginSubmitted();
}

final class PhoneCodeSend extends LoginEvent {
  const PhoneCodeSend();
}
