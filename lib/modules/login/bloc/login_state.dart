part of 'login_bloc.dart';

final class LoginState extends Equatable {
  final UserPhone phone;
  final SmsCode smsCode;
  final FormzSubmissionStatus status;
  final bool isValid;
  final LoginInfoModel? loginInfoModel;

  const LoginState(
      {this.phone = const UserPhone.pure(),
      this.smsCode = const SmsCode.pure(),
      this.isValid = false,
      this.status = FormzSubmissionStatus.initial,
      this.loginInfoModel});

  LoginState copyWith(
      {FormzSubmissionStatus? status,
      UserPhone? phone,
      SmsCode? smsCode,
      bool? isValid,
      LoginInfoModel? loginInfoModel}) {
    return LoginState(
        status: status ?? FormzSubmissionStatus.initial,
        phone: phone ?? this.phone,
        smsCode: smsCode ?? this.smsCode,
        isValid: isValid ?? this.isValid,
        loginInfoModel: loginInfoModel ?? this.loginInfoModel);
  }

  @override
  List<Object?> get props => [status, phone, smsCode];
}
