import 'package:common_utils/common_utils.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/login_info_model/login_info_model.dart';
import 'package:flutter_app_kouyu/common/http/models/user_info_model/user_info_model.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/login/models/models.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:formz/formz.dart';

part 'login_event.dart';
part 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  LoginBloc() : super(const LoginState()) {
    on<LoginPhoneChanged>(_onPhoneChanged);
    on<SmsCodeChanged>(_onSmsCodehanged);
    on<LoginSubmitted>(_onSubmitted);
    on<VisitorLoginSubmitted>(_onVisitorSubmitted);
  }

  void _onPhoneChanged(LoginPhoneChanged event, Emitter<LoginState> emit) {
    final phone = UserPhone.dirty(event.phone);
    emit(state.copyWith(
        phone: phone, isValid: Formz.validate([phone, state.smsCode])));
  }

  void _onSmsCodehanged(SmsCodeChanged event, Emitter<LoginState> emit) {
    final code = SmsCode.dirty(event.code);
    emit(state.copyWith(
        smsCode: code, isValid: Formz.validate([code, state.phone])));
  }

  Future<void> _onSubmitted(
      LoginSubmitted event, Emitter<LoginState> emit) async {
    if (state.phone.error != null) {
      EasyLoading.showInfo(state.phone.error!.desc);
      return;
    }
    if (state.smsCode.error != null) {
      EasyLoading.showInfo(state.smsCode.error!.desc);
      return;
    }
    if (state.isValid) {
      emit(state.copyWith(status: FormzSubmissionStatus.inProgress));

      await Api.login({
        "phone": state.phone.value,
        "verification_code": state.smsCode.value
      }).then((value) {
        _handleLoginInfo(value);
      }).catchError((e) {
        LogUtil.v(e, tag: "解析异常");
        // emit(state.copyWith(status: FormzSubmissionStatus.failure));
      });
    }
  }

  Future<void> _onVisitorSubmitted(
      VisitorLoginSubmitted event, Emitter<LoginState> emit) async {
    await Api.visitorLogin().then((value) {
      _handleLoginInfo(value);
    }).catchError((e) {
      LogUtil.v(e, tag: "解析异常");
      // emit(state.copyWith(status: FormzSubmissionStatus.failure));
    });
  }

  void _handleLoginInfo(LoginInfoModel value) {
    LogUtil.v(value, tag: "登录数据返回成功");
    LogUtil.d(value);
    //保存登录信息到本地
    LoginUtil.saveOrUpdateLoginInfo(value);
    emit(state.copyWith(
        status: FormzSubmissionStatus.success, loginInfoModel: value));
  }
}
