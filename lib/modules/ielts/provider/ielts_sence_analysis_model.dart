import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/loading_status.dart';
import 'package:flutter_app_kouyu/common/http/models/english_tap_model/datum.dart';
import 'package:flutter_app_kouyu/common/http/models/english_tap_model/english_tap_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_analyze_vocabulary_model/ielts_speaking_analyze_vocabulary_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_grammar_check_model/ielts_speaking_grammar_check_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_question_answer_model/ielts_speaking_question_answer_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_soe_model/ielts_speaking_soe_model.dart';
import 'package:flutter_app_kouyu/modules/chat/view/follow_read_widget.dart';
import 'package:flutter_app_kouyu/modules/ielts/provider/ielts_chat_cubit.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielts_talk_page.dart';
import 'package:flutter_app_kouyu/modules/ielts/view/ielts_follow_read_widget.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_question_answer_model/data.dart'
    as hitData;

class IeltsSenceAnalysisModel extends ChangeNotifier {
  final IeltsSpeakingSoeModel? soeModel;
  final IeltsSpeakingGrammarCheckModel? analyzeModel;
  final IeltsTalkModel talkModel;
  final IeltsTalkArgument argument;
  IeltsSpeakingQuestionAnswerModel? hitModel;
  IeltsSpeakingAnalyzeVocabularyModel? analyzeVocabularyModel;
  hitData.Data? _currentHitData;

  final FreeTalkRepository freeTalkRepository = FreeTalkRepository();
  List<String> collectedList = [];
  Map<String, String?> translateMap = {};
  IeltsSenceAnalysisModel(
      {this.soeModel,
      this.analyzeModel,
      required this.talkModel,
      required this.argument}) {
    getChatHint();
    getAnalyzeVocabularyModel();
  }

  translate(String? text) {
    if (text == null) {
      return;
    }
    freeTalkRepository.translate(text).then((value) {
      translateMap[text] = value.data;
      notifyListeners();
    });
  }

  get currentHitData => _currentHitData;

  setCurrentHitData(hitData.Data data) async {
    _currentHitData = data;
    notifyListeners();
  }

  collection(String? text) async {
    await freeTalkRepository.collectVocabulary(text);
    if (text != null) {
      collectedList.add(text);
    }
    notifyListeners();
  }

  void gotoFollowRead(BuildContext context, String text) {
    showModalBottomSheet(
        isScrollControlled: true,
        useSafeArea: true,
        constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height -
                MediaQuery.of(context).padding.top),
        backgroundColor: Colors.transparent,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20))),
        context: context,
        builder: (context) {
          return IeltsFollowReadWidget(
            text: text,
            questionId: talkModel.questionId!,
            practiceId: talkModel.practiceId!,
          );
        });
  }

  void getChatHint() async {
    try {
      hitModel = await Api.ieltsSpeakingGetSnswerByQuestionId(
          talkModel.questionId ?? 0);
      _currentHitData = hitModel?.data?.first;
      notifyListeners();
    } catch (_) {
      notifyListeners();
    }
  }

  void getAnalyzeVocabularyModel() async {
    try {
      analyzeVocabularyModel = await Api.ieltsSpeakingAnalyzeVocabularye(
          talkModel.practiceId ?? 0,
          talkModel.questionId ?? 0,
          talkModel.answer ?? "");
      _currentHitData = hitModel?.data?.first;
      notifyListeners();
    } catch (_) {
      notifyListeners();
    }
  }
}
