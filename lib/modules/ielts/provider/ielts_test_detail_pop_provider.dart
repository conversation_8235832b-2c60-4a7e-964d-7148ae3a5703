// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_user_info_model/ielts_user_info_model.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_report_model.dart';
import 'package:flutter_app_kouyu/modules/chat/view/chat_report_page.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielts_speaking_report_page.dart';
import 'package:flutter_app_kouyu/modules/ielts/provider/ielts_speaking_report_provider.dart';
import 'package:flutter_app_kouyu/modules/ielts/view/ielts_test_transition_pop.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 雅思成绩详情弹窗provider
class IeltsTestDetailPopProvider extends ChangeNotifier {
  /// 口语测试过渡弹窗是否展示
  bool testTransitionPopup = false;
  IeltsUserInfoModel? ieltsUserInfoModel;
  IeltsTestDetailPopProvider({
    required this.ieltsUserInfoModel,
  }) {
    notifyListeners();
  }

  // 查看口语测试报告
  enterReport(BuildContext context) {
    //查看报告
    Navigator.push(
        context,
        MaterialPageRoute<void>(
            builder: (_) => IeltsSpeakingReportPage(
                  practiceId: ieltsUserInfoModel?.data?.practiceId ?? 0,
                  type: IeltsSpeakingReportDetailType.view,
                )));
  }

  showTestPop(BuildContext context) {
    showModalBottomSheet(
        backgroundColor: Colors.transparent,
        constraints:
            BoxConstraints(maxWidth: double.infinity, maxHeight: 1138.w),
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20))),
        context: context,
        builder: (context) {
          return Padding(
            padding: EdgeInsets.only(left: 24.w, right: 24.w, bottom: 50.w),
            child: Container(
                clipBehavior: Clip.antiAlias,
                decoration:
                    BoxDecoration(borderRadius: BorderRadius.circular(40.w)),
                child: IeltsTestTransitionPop(
                  popType: "test",
                )),
          );
        });
  }
}
