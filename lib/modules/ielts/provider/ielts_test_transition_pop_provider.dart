// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_topic_detail_model/ielts_topic_detail_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_user_info_model/ielts_user_info_model.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_report_model.dart';
import 'package:flutter_app_kouyu/modules/chat/view/chat_report_page.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielts_speaking_report_page.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielts_talk_page.dart';
import 'package:flutter_app_kouyu/modules/ielts/provider/ielts_speaking_report_provider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 雅思成绩详情弹窗provider
class IeltsTestTransitionPopProvider extends ChangeNotifier {
  IeltsUserInfoModel? ieltsUserInfoModel;
  IeltsTopicDetailModel? part1Data;
  IeltsTopicDetailModel? part23Data;
  int tabIndex = 0;
  IeltsTopicDetailModel? selectedPartData;

  IeltsTestTransitionPopProvider({
    this.ieltsUserInfoModel,
  });

  // 进入聊天
  enterChat(BuildContext context) {
    Navigator.push(
      context,
      IeltsTalkPage.route(
        argument: IeltsTalkArgument(
            partCategoryTitle: part1Data?.data?.partCategoryName ?? "",
            ieltsType: IeltsTypeEnum.test,
            topicNameChinese: part1Data?.data?.chineseTopicName ?? "",
            topicId: part1Data!.data!.topicId!,
            topicCode: part1Data!.data!.topicCode!,
            topicIds: [part1Data!.data!.topicId!, part23Data!.data!.topicId!]),
      ),
    );
  }

  refreshData(List<int>? topicIds) {
    Api.getUserIeltsInfo().then((value) async {
      ieltsUserInfoModel = value;

      await getTopicDetail(topicIds);

      notifyListeners();
    });
  }

  /// 获取两个 topic 的详情
  getTopicDetail(List<int>? topicIds) async {
    if (topicIds == null) {
      var result = await Api.getIeltsTopicDetail(
          ieltsUserInfoModel?.data?.trialPart1TopicId ?? 0);
      part1Data = result;
      selectedPartData = part1Data;

      part23Data = await Api.getIeltsTopicDetail(
          ieltsUserInfoModel?.data?.trialPart23TopicId ?? 0);
    } else {
      if (topicIds.length > 0) {
        var result = await Api.getIeltsTopicDetail(topicIds[0] ?? 0);
        part1Data = result;
        selectedPartData = part1Data;
      }

      if (topicIds.length > 1) {
        part23Data = await Api.getIeltsTopicDetail(topicIds[1] ?? 0);
      }
    }
  }

  /// 切换tab, 根据不同的tab展示不同的数据
  switchTab(int index) {
    tabIndex = index;
    selectedPartData = tabIndex == 0 ? part1Data : part23Data;

    notifyListeners();
  }
}
