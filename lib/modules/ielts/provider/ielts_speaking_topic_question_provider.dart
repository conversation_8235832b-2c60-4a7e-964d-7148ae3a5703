// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_topic_questions_model/ielts_speaking_topic_questions_model.dart';
import 'package:flutter_app_kouyu/common/http/models/report_list_model/report_list_model.dart';
import 'package:flutter_app_kouyu/modules/my/pages/report_record_page.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class IeltsSpeakingTopicQuestionProvider extends ChangeNotifier {
  IeltsSpeakingTopicQuestionModel? questionModel;
  RefreshController refreshController = RefreshController();
  late int topicId;

  IeltsSpeakingTopicQuestionProvider(this.topicId) {
    loadData();
    // notifyListeners();
  }

  loadData() async {
    try {
      questionModel = await Api.ieltsSpeakingGetTopicquestions(topicId);
    } catch (e) {
      refreshController.refreshFailed();
    } finally {
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }
}
