// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_topics_model/ielts_speaking_topics_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_user_info_model/ielts_user_info_model.dart';
import 'package:flutter_app_kouyu/common/http/models/iets_tab_model/topic_type_list.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/ielts/view/ielts_test_detail_pop.dart';
import 'package:flutter_app_kouyu/widgets/overlay_manager.dart';
import 'package:flutter_app_kouyu/widgets/trial_expires_widget.dart';
import 'package:flutter_app_kouyu/widgets/vip_spical_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/iets_tab_model/iets_tab_model.dart';
import 'package:flutter_app_kouyu/modules/ielts/view/ielts_test_transition_pop.dart';

import '../../../common/http/models/iets_tab_model/datum.dart';

class IeltsListProvider extends ChangeNotifier {
  IetsTabModel? ietsTabModel;
  Datum? _selectIetsTab;
  IeltsSpeakingTopicsModel? topicsModel;
  //我的雅思信息
  IeltsUserInfoModel? ieltsUserInfoModel;
  bool isVip = true;
  IeltsListProvider() {
    getIeltsTabs();
  }

  TopicTypeList? _selectedTopicTypeList;
  set selectedTopicTypeList(TopicTypeList? selectedTopicTypeList) {
    _selectedTopicTypeList = selectedTopicTypeList;
    getTopicsModels();
    notifyListeners();
  }

  TopicTypeList? get selectedTopicTypeList => _selectedTopicTypeList;

  set selectIetsTab(Datum? value) {
    _selectIetsTab = value;
    selectedTopicTypeList = _selectIetsTab?.topicTypeList?.first;
    notifyListeners();
  }

  Datum? get selectIetsTab => _selectIetsTab;

  getMyIeltsUserInfo() async {
    ieltsUserInfoModel = await Api.getUserIeltsInfo();
    notifyListeners();
  }

  checkIsVip() async {
    final userInfo = await LoginUtil.userInfoModel();
    isVip = userInfo?.data?.memberType == 1;
  }

  getIeltsTabs() async {
    ietsTabModel = await Api.getIeltsTabs();
    selectIetsTab = ietsTabModel?.data?.first;
    notifyListeners();
  }

  getTopicsModels() async {
    topicsModel = await Api.getIeltsSpeakingTopics(
        _selectIetsTab?.partCategoryCode ?? "",
        _selectedTopicTypeList?.topicType ?? 0);
    notifyListeners();
  }

  /// 显示雅思口语测试弹窗
  showIeltsTestTransitionPop(BuildContext context) async {
    final userInfo = await LoginUtil.userInfoModel();
    bool isVip = userInfo?.data?.memberType == 1;
    if (!isVip) {
      //体验到期
      OverlayManager.getInstance().showOverlay(
        true,
        builder: (close) => VipSpicalWidget(
          close: close,
        ),
      );
      return;
    }
    showModalBottomSheet(
        backgroundColor: Colors.transparent,
        constraints:
            BoxConstraints(maxWidth: double.infinity, maxHeight: 1138.w),
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20))),
        context: context,
        builder: (context) {
          return Padding(
            padding: EdgeInsets.only(left: 24.w, right: 24.w, bottom: 50.w),
            child: Container(
                clipBehavior: Clip.antiAlias,
                decoration:
                    BoxDecoration(borderRadius: BorderRadius.circular(40.w)),
                // child: IeltsTestTransitionPop()),
                child: const IeltsTestTransitionPop(popType: 'test')),
          );
        });
  }

  /// 显示雅思口语测试弹窗
  showIeltsTestDetailPop(BuildContext context) async {
    final userInfo = await LoginUtil.userInfoModel();
    bool isVip = userInfo?.data?.memberType == 1;
    if (!isVip) {
      //体验到期
      OverlayManager.getInstance().showOverlay(
        true,
        builder: (close) => TrialExpiresWidget(
          close: close,
        ),
      );
      return;
    }
    showModalBottomSheet(
        backgroundColor: Colors.transparent,
        constraints:
            BoxConstraints(maxWidth: double.infinity, maxHeight: 1138.w),
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20))),
        context: context,
        builder: (context) {
          return Padding(
            padding: EdgeInsets.only(left: 24.w, right: 24.w, bottom: 50.w),
            child: Container(
                clipBehavior: Clip.antiAlias,
                decoration:
                    BoxDecoration(borderRadius: BorderRadius.circular(40.w)),
                // child: IeltsTestTransitionPop()),
                child: IeltsTestDetailPop(
                    ieltsUserInfoModel: this.ieltsUserInfoModel!)),
          );
        });
  }
}
