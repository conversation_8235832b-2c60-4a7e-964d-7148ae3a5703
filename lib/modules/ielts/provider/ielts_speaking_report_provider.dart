import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/custome_scene_model/get_share_image_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_report_model/ielts_speaking_report_model.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_report_model/record.dart'
    as ielts_record;
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';

import '../../../widgets/share-widget/utils.dart';

enum IeltsSpeakingReportDetailType {
  //1 创建  2 查看
  create,
  view,
}

class IeltsSpeakingReportNotifier extends ChangeNotifier {
  // 页面截图控制器
  final ScreenshotController screenshotController = ScreenshotController();

  final num practiceId;
  //1 创建  2 查看
  final IeltsSpeakingReportDetailType type;
  IeltsSpeakingReportModel? model;
  Map<String, String?> translateMap = {};
  //当前选中的记录
  ielts_record.Record? currentRecord;
  //当前选中的part类型
  String? _selectPartTypeTab;

  //当前选中的问题索引
  int? _selectedIndex = 0;

  set selectPartTypeTab(String? value) {
    _selectPartTypeTab = value;
    if (value == "Part1") {
      currentRecord = model?.data?.records![0].first;
    } else {
      currentRecord = model?.data?.records![1].first;
    }
    selectedIndex = 0;
    notifyListeners();
  }

  int? get selectedIndex => _selectedIndex;

  set selectedIndex(int? value) {
    _selectedIndex = value;
    if (selectPartTypeTab == "Part1" || selectPartTypeTab == null) {
      currentRecord = model?.data?.records![0][value!];
    } else {
      currentRecord = model?.data?.records![1][value!];
    }
    notifyListeners();
  }

  String? get selectPartTypeTab => _selectPartTypeTab;

  final FreeTalkRepository freeTalkRepository = FreeTalkRepository();
  List<String> collectedList = [];

  IeltsSpeakingReportNotifier({required this.practiceId, required this.type}) {
    requestData();
  }

  void requestData() async {
    if (type == IeltsSpeakingReportDetailType.create) {
      EasyLoading.show(status: "生成报告中...");
      try {
        await Api.generateIeltsSpeakingReport({"practice_id": practiceId})
            .then((value) {
          model = value;
          currentRecord = model?.data?.records![0].first;
          if (model?.data?.records!.length == 2) {
            selectPartTypeTab = "Part1";
          }
          notifyListeners();
        });
      } finally {
        EasyLoading.dismiss();
      }
    } else {
      Api.getIeltsSpeakingReport({"practice_id": practiceId}).then((value) {
        model = value;
        currentRecord = model?.data?.records![0].first;
        if (model?.data?.records!.length == 2) {
          selectPartTypeTab = "Part1";
        }
        notifyListeners();
      });
    }
  }

  display(String? text, String? chinese) {
    if (text == null) {
      return;
    }
    translateMap[text] = chinese;
    notifyListeners();
  }

  translate(String? text) {
    if (text == null) {
      return;
    }
    freeTalkRepository.translate(text).then((value) {
      translateMap[text] = value.data;
      notifyListeners();
    });
  }

  collection(String? text) async {
    await freeTalkRepository.collectionSentence(
        text, "ielts_speaking_report", "$practiceId");
    if (text != null) {
      collectedList.add(text);
    }
    notifyListeners();
  }

  cancelCollection(String? text) async {
    await freeTalkRepository.cancelCollectionSentence(text);
    if (text != null) {
      collectedList.remove(text);
    }
    notifyListeners();
  }

  /// 获取分享图
  Future<List<String>> getShareImageUrls() async {
    final serverUrls = await getShareImageFromServer();
    final screenshotUrl = await getShareImageFromScreenshot();
    return [screenshotUrl, ...serverUrls];
  }

  /// 从截图获取分享图
  Future<String> getShareImageFromScreenshot() async {
    final screenshotUrl = await captureAndSaveScreenshot(screenshotController, "ielts_speaking_report");
    return screenshotUrl;

    // todo 合并二维码
  }

  /// 从网络获取分享图
  Future<List<String>> getShareImageFromServer() async {
    GetShareImageModel result = await Api.getShareImageList(practiceId, "chat_report");

    if (result.data != null && result.data!.isNotEmpty) {
      return result.data!.map((e) => e.poster_url ?? "").toList();
    }
    return []; 
  }
}
