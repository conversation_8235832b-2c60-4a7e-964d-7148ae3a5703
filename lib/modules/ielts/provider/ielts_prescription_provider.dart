// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_prescription_model/ielts_prescription_model.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_prescription_model/part.dart';

class IeltsPrescriptionProvider extends ChangeNotifier {
  IeltsPrescriptionModel? ieltsPrescriptionModel;
  List<String> typeList = [
    'Part1',
    'Part2',
    'Part3',
  ];
  String _selectPrescriptionTab = 'Part1';
  IeltsPrescriptionProvider() {
    getIeltsSpeakingGuide();
  }

  set selectPrescriptionTab(String selectPrescriptionTab) {
    _selectPrescriptionTab = selectPrescriptionTab;
    getIeltsSpeakingGuide();
    notifyListeners();
  }

  String get selectPrescriptionTab => _selectPrescriptionTab;

  getIeltsSpeakingGuide() async {
    ieltsPrescriptionModel = await Api.getIeltsSpeakingGuide();
    notifyListeners();
  }

  Part? getPartData() {
    if (selectPrescriptionTab == 'Part1') {
      return ieltsPrescriptionModel?.data?.part1;
    } else if (selectPrescriptionTab == 'Part2') {
      return ieltsPrescriptionModel?.data?.part2;
    } else if (selectPrescriptionTab == 'Part3') {
      return ieltsPrescriptionModel?.data?.part3;
    }
    return null;
  }
}
