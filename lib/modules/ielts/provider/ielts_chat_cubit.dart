import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/loading_status.dart';
import 'package:flutter_app_kouyu/common/http/models/analyze_model/analyze_model.dart';
import 'package:flutter_app_kouyu/common/http/models/collect_sentence.dart';
import 'package:flutter_app_kouyu/common/http/models/en_to_ch_model.dart';
import 'package:flutter_app_kouyu/common/http/models/audio_upload_model/audio_upload_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_chat_model/ielts_speaking_chat_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_grammar_check_model/ielts_speaking_grammar_check_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_practice_model/ielts_speaking_practice_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_question_answer_model/ielts_speaking_question_answer_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_soe_model/ielts_speaking_soe_model.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';
import 'package:flutter_app_kouyu/common/http/request_error.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/common/utils/log.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielts_talk_page.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_question_answer_model/data.dart';

class IeltsChatProvider extends ChangeNotifier implements VoiceEvent {
  final FreeTalkRepository freeTalkRepository = FreeTalkRepository();
  final IeltsTalkArgument argument;
  final BuildContext context;
  final List<IeltsTalkModel> list = [];
  LoadingStatus? chatHintStatus;
  List<Data> chatHintList = [];
  int totalQuestionCount = 0;
  int currentQuestionIndex = 0;
  bool finished = false;
  IeltsChatProvider(
    this.context, {
    required this.argument,
  });

  Future<void> intiGreetingStream() async {
    //取最新的model
    int index = list.length;
    IeltsTalkModel initModel = IeltsTalkModel(
        type: 0, replyStatus: LoadingState(status: LoadingStatus.inProgress));
    list.add(initModel);
    notifyListeners();
    try {
      IeltsSpeakingPracticeModel greetingModel = await Api.startPractice(
          argument.topicId,
          argument.ieltsType.type,
          argument.topicIds ?? [argument.topicId]);
      IeltsTalkModel model = IeltsTalkModel(
          type: 0,
          practiceId: greetingModel.data?.practiceId,
          questionId: greetingModel.data?.questionId,
          answer: greetingModel.data?.question?.english,
          audioUrl: greetingModel.data?.questionAudioUrl,
          translateText: greetingModel.data?.question?.chinese,
          showTranslateText: argument.ieltsType == IeltsTypeEnum.practiceType,
          translateStatus: const LoadingState(status: LoadingStatus.success),
          replyStatus: const LoadingState(status: LoadingStatus.success));
      list.clear();
      list.add(model);

      IeltsTalkModel model2 = IeltsTalkModel(
          type: 0,
          practiceId: greetingModel.data?.practiceId,
          questionId: greetingModel.data?.questionId,
          answer: greetingModel.data?.nextQuestion?.english,
          audioUrl: greetingModel.data?.nextQuestionAudioUrl,
          translateText: greetingModel.data?.nextQuestion?.chinese,
          showTranslateText: argument.ieltsType == IeltsTypeEnum.practiceType,
          translateStatus: const LoadingState(status: LoadingStatus.success),
          replyStatus: const LoadingState(status: LoadingStatus.success));

      list.add(model2);
      totalQuestionCount = greetingModel.data?.totalQuestionCount ?? 0;
      currentQuestionIndex = greetingModel.data?.lastFinishedQuestionIndex ?? 0;
      notifyListeners();
      CommonUtils.playVideo(model.audioUrl ?? "", nextUrl: model2.audioUrl);
    } catch (e) {
      initModel = IeltsTalkModel(
          type: 0, replyStatus: LoadingState(status: LoadingStatus.failure));
      list[index] = initModel;
      notifyListeners();
    }
  }

  //不显示提示
  cleanHit() {
    chatHintList.clear();
    chatHintStatus = null;
    notifyListeners();
  }

  chat(String? question, String filePath) async {
    Log.d("准备 发送语音");
    if (question == null || question.isEmpty) {
      return;
    }
    //添加问话
    IeltsTalkModel questionModel = IeltsTalkModel(
        type: 1,
        practiceId: list.last.practiceId,
        questionId: list.last.questionId,
        answer: question,
        soeStatus: const LoadingState(status: LoadingStatus.inProgress),
        analyzeStatus: const LoadingState(status: LoadingStatus.inProgress));
    if (questionModel.practiceId == null) {
      return;
    }
    list.add(questionModel);
    int index = list.length - 1;
    //获取对话
    sendChat(questionModel);
    //语法分析
    analyze(questionModel, index);
    Future.delayed(Duration(seconds: 1), () async {
      AudioUploadModel value = await uploadVoice(filePath);
      questionModel.audioUrl = value.data?.fileUrl;
      soe(questionModel, index);
    });
  }

  Future<void> sendChat(
    IeltsTalkModel question,
  ) async {
    //取最新的model
    int index = list.length;
    IeltsTalkModel initModel = IeltsTalkModel(
        type: 0,
        replyStatus: const LoadingState(status: LoadingStatus.inProgress));
    list.add(initModel);
    notifyListeners();
    try {
      //请求答案
      IeltsSpeakingChatModel answer = await Api.ieltsSpeakingChat(
          question.practiceId ?? 0,
          question.questionId ?? 0,
          question.answer ?? "",
          question.audioUrl);

      //如果list.length大于0，则等待1秒
      if (list.isNotEmpty) {
        await Future.delayed(Duration(seconds: 1));
      }

      IeltsTalkModel model = IeltsTalkModel(
          type: 0,
          practiceId: answer.data?.practiceId,
          questionId: answer.data?.questionId,
          question: question.answer,
          answer: answer.data?.question?.english,
          audioUrl: answer.data?.questionAudioUrl,
          translateText: answer.data?.question?.chinese,
          showTranslateText: argument.ieltsType == IeltsTypeEnum.practiceType,
          translateStatus: const LoadingState(status: LoadingStatus.success),
          replyStatus: const LoadingState(status: LoadingStatus.success));

      list[index] = model;
      totalQuestionCount = answer.data?.totalQuestionCount ?? 0;
      currentQuestionIndex = answer.data?.lastFinishedQuestionIndex ?? 0;
      finished = answer.data?.isEnd ?? false;

      if (answer.data?.partCategory != null) {
        argument.partCategoryTitle = answer.data!.partCategory!;
      }
      if (answer.data?.topicChineseName != null) {
        argument.topicNameChinese = answer.data!.topicChineseName!;
      }
      if (answer.data?.nextQuestion != null) {
        IeltsTalkModel model2 = IeltsTalkModel(
            type: 0,
            practiceId: answer.data?.practiceId,
            questionId: answer.data?.questionId,
            answer: answer.data?.nextQuestion?.english,
            audioUrl: answer.data?.nextQuestionAudioUrl,
            translateText: answer.data?.nextQuestion?.chinese,
            showTranslateText: argument.ieltsType == IeltsTypeEnum.practiceType,
            translateStatus: const LoadingState(status: LoadingStatus.success),
            replyStatus: const LoadingState(status: LoadingStatus.success));

        list.add(model2);
        notifyListeners();
        CommonUtils.playVideo(model.audioUrl ?? "", nextUrl: model2.audioUrl);
      } else {
        notifyListeners();
        playVideo(model.audioUrl);
      }
    } catch (e) {
      initModel = IeltsTalkModel(
          type: 0,
          replyStatus: const LoadingState(status: LoadingStatus.failure));
      list[index] = initModel;
      notifyListeners();
    }
  }

  Future<AudioUploadModel> uploadVoice(String filePath) async {
    return freeTalkRepository.upload(filePath,
        topicCode: "${argument.topicCode}", sence: "ielts_mock_test");
  }

  void translate(IeltsTalkModel model, int index) async {
    model.showTranslateText = true;
    notifyListeners();
  }

  void collection(IeltsTalkModel model, int index) async {
    if (model.collected == true) {
      await freeTalkRepository.cancelCollectionSentence(model.answer);
      model.collected = false;
      notifyListeners();
      return;
    } else {
      await freeTalkRepository.collectionSentence(
          model.answer, "conversation_history", "0");
      model.collected = true;
      notifyListeners();
      return;
    }
  }

  void collectionHitModel(Data data) async {
    if (data.collected == true) {
      data.collected = false;
      notifyListeners();
      return;
    }
    await freeTalkRepository.collectionSentence(
        data.english, "conversation_history", "0");
    data.collected = true;
    notifyListeners();
  }

  void update<T>(int index, Future<T> Function() request) async {
    //取最新的model
    if (T == EnToCnModel) {
      list[index].translateStatus =
          const LoadingState(status: LoadingStatus.inProgress);
    } else if (T == IeltsSpeakingGrammarCheckModel) {
      list[index].analyzeStatus =
          const LoadingState(status: LoadingStatus.inProgress);
    } else if (T == IeltsSpeakingSoeModel) {
      list[index].soeStatus =
          const LoadingState(status: LoadingStatus.inProgress);
    } else if (T == CollectionSentence) {
      list[index].collected = true;
    } else {
      return;
    }
    notifyListeners();
    try {
      T result = await request();
      //取最新的model
      if (T == EnToCnModel) {
        list[index].translateStatus =
            const LoadingState(status: LoadingStatus.success);
        list[index].translateText = (result as EnToCnModel).data;
      } else if (T == IeltsSpeakingGrammarCheckModel) {
        list[index].analyzeStatus =
            const LoadingState(status: LoadingStatus.success);
        list[index].analyzeModel = result as IeltsSpeakingGrammarCheckModel;
      } else if (T == IeltsSpeakingSoeModel) {
        list[index].soeStatus =
            const LoadingState(status: LoadingStatus.success);
        list[index].soeModel = result as IeltsSpeakingSoeModel;
      } else {
        return;
      }
      notifyListeners();
    } catch (e) {
      //取最新的model
      String? errorMessage;

      if (e is RequestError) {
        errorMessage = e.message;
        if ((e).code == "0019" || (e).code == "0020") {
          //中文不能进行语音评测 不支持中文语法检测
          errorMessage = null;
        }
      }

      if (T == EnToCnModel) {
        list[index].translateStatus =
            LoadingState(status: LoadingStatus.failure, message: errorMessage);
      } else if (T == IeltsSpeakingGrammarCheckModel) {
        list[index].analyzeStatus =
            LoadingState(status: LoadingStatus.failure, message: errorMessage);
      } else if (T == IeltsSpeakingSoeModel) {
        list[index].soeStatus =
            LoadingState(status: LoadingStatus.failure, message: errorMessage);
      } else {
        return;
      }
      notifyListeners();
    }
  }

  void getChatHint() async {
    String? text = list.last.answer;
    if (text == null || text.isEmpty) {
      return;
    }
    if (chatHintList.isNotEmpty) {
      return;
    }
    chatHintStatus = LoadingStatus.inProgress;
    notifyListeners();
    try {
      IeltsSpeakingQuestionAnswerModel result =
          await Api.ieltsSpeakingGetSnswerByQuestionId(
              list.last.questionId ?? 0);
      chatHintList = result.data ?? [];
      chatHintStatus = LoadingStatus.success;
      notifyListeners();
    } catch (_) {
      chatHintStatus = LoadingStatus.failure;
      notifyListeners();
    }
  }

  Future<void> analyze(IeltsTalkModel model, int index) async {
    // if (model.analyzeStatus == LoadingStatus.inProgress) {
    //   return;
    // }
    update(
        index,
        () => Api.ieltsSpeakingGrammarCheck(
            model.practiceId ?? 0, model.questionId ?? 0, model.answer ?? ""));
  }

  Future<void> soe(IeltsTalkModel model, int index) async {
    update(
        index,
        () => Api.ieltsSpeakingSoe(model.practiceId ?? 0, model.questionId ?? 0,
            model.answer ?? "", model.audioUrl ?? ""));
  }

  void playVideo(String? url) async {
    CommonUtils.playVideo(url ?? "");
  }

  showFinishTaskDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      routeSettings: const RouteSettings(arguments: {"stopPlay": false}),
      builder: (context) {
        Future.delayed(const Duration(milliseconds: 1000), () {
          Navigator.of(context).pop();
        });
        return GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: Container(
            width: double.infinity,
            height: double.infinity,
            padding: EdgeInsets.only(top: 200.w),
            child: Align(
              alignment: Alignment.topCenter,
              child: Image.asset(
                "images/finish_task_alert.png",
                width: 397.w,
                fit: BoxFit.fitWidth,
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  recordFinish(String? question, String filePath) {
    chat(question, filePath);
  }
}

class IeltsTalkModel {
  final String? question;
  final String? answer;

  String? audioUrl;
  String? translateText;
  bool showTranslateText;
  //0 question  1  reply
  final int type;
  IeltsSpeakingGrammarCheckModel? analyzeModel;
  LoadingState? analyzeStatus;
  IeltsSpeakingSoeModel? soeModel;
  LoadingState? soeStatus;
  final LoadingState? replyStatus;
  LoadingState? translateStatus;
  bool? collected;
  final int? practiceId;
  final int? questionId;

  IeltsTalkModel(
      {required this.type,
      this.question,
      this.answer,
      this.audioUrl,
      this.translateText,
      this.analyzeModel,
      this.soeModel,
      this.soeStatus,
      this.analyzeStatus,
      this.translateStatus,
      this.collected,
      this.replyStatus,
      this.practiceId,
      this.questionId,
      this.showTranslateText = false});
}
