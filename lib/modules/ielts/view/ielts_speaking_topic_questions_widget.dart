import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/modules/ielts/provider/ielts_speaking_topic_question_provider.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielts_talk_page.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class IeltsSpeakingTopicQuestionsDialogWidget extends StatelessWidget {
  final int topicId;
  final int exerciseResidueTimes;
  final int mockResidueTimes;
  final bool vip;
  final bool isChatPage;

  const IeltsSpeakingTopicQuestionsDialogWidget({
    super.key,
    required this.topicId,
    this.exerciseResidueTimes = 2,
    this.mockResidueTimes = 2,
    this.vip = false,
    this.isChatPage = false,
  });

  @override
  Widget build(BuildContext context) {
    // _showTask(context);
    return ChangeNotifierProvider<IeltsSpeakingTopicQuestionProvider>(
        create: (_) => IeltsSpeakingTopicQuestionProvider(topicId)..loadData(),
        child: Consumer<IeltsSpeakingTopicQuestionProvider>(
            builder: (context, model, child) {
          // insetPadding: EdgeInsets.zero,
          return Container(
            color: Colors.white,
            width: MediaQuery.of(context).size.width,
            height: 1303.w,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 40.w),
                  child: Row(
                    children: [
                      SizedBox(width: 40.w), // Add left margin of 40px
                      Expanded(
                        child: Text(
                          (model.questionModel?.data?.partCategoryName ??
                                  'No Category') +
                              "-" +
                              (model.questionModel?.data?.topicName ??
                                  'No Topic'),
                          style: style_1_36,
                          overflow: TextOverflow.visible,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 15.w),
                Container(
                  child: Divider(
                    color: Color(0xFFDDEDF0),
                    thickness: 1,
                  ),
                  margin: EdgeInsets.only(left: 40.w, right: 40.w),
                  // height: 0.5.h,
                ),
                SizedBox(height: 15.w),

                Expanded(
                    child: SingleChildScrollView(
                  child: Container(
                    margin: EdgeInsets.only(left: 40.w),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        // mainAxisAlignment: MainAxisAlignment.left,
                        children: <Widget>[
                          if (model.questionModel?.data?.partCategoryName ==
                              "Part2&3")
                            Text(
                              (model.questionModel?.data?.questions![0]
                                      .partName ??
                                  'PART2'),
                              style: style_1_36,
                            ),
                          SizedBox(height: 20.w),
                          for (var i = 0;
                              i <
                                  (model.questionModel?.data?.questions?[0].list
                                          ?.length ??
                                      0);
                              i++)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                //  SizedBox(width: 40.w), // Add left margin of 40px
                                Text(
                                    '${i + 1}. ${model.questionModel?.data?.questions?[0].list?[i].english ?? 'No Question'}',
                                    style: style_1_32_400),
                                SizedBox(height: 10.w),
                                Text(
                                    model.questionModel?.data?.questions?[0]
                                            .list?[i].chinese ??
                                        'No Translation',
                                    style: style_2_24),
                                SizedBox(height: 20.w),
                              ],
                            ),
                          if (model.questionModel?.data?.partCategoryName ==
                              "Part2&3")
                            Container(
                              child: Divider(
                                color: const Color.fromARGB(255, 233, 231, 231),
                                thickness: 1,
                              ),
                              margin: EdgeInsets.only(right: 40.w),
                              // height: 0.5.h,
                            ),
                          if (model.questionModel?.data?.partCategoryName ==
                              "Part2&3")
                            Text(
                              (model.questionModel?.data?.questions![1]
                                      .partName ??
                                  'PART3'),
                              style: style_1_36,
                            ),
                          SizedBox(height: 20.w),
                          if (model.questionModel?.data?.partCategoryName ==
                              "Part2&3")
                            for (var j = 0;
                                j <
                                    (model.questionModel?.data?.questions?[1]
                                            .list?.length ??
                                        0);
                                j++)
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                      '${j + 1}. ${model.questionModel?.data?.questions?[1].list?[j].english ?? 'No Question'}',
                                      style: style_1_32_400),
                                  SizedBox(height: 10.w),
                                  Text(
                                      model.questionModel?.data?.questions?[1]
                                              .list?[j].chinese ??
                                          'No Translation',
                                      style: style_2_24),
                                  SizedBox(height: 20.w),
                                ],
                              ),
                        ]
                        // children: [

                        //   Text(
                        //       "1.What kind of famous people do you often see in the news?",
                        //       style: style_1_32_400),
                        //   Text("你经常在新闻上看到哪些名人?", style: style_2_24),
                        //   SizedBox(height: 10),

                        // ],
                        ),
                  ),
                )),
                // ),
                // Spacer(),
                Container(
                  margin: EdgeInsets.only(
                      left: 40.w, right: 40.w, bottom: 40.w, top: 20.w),
                  child: Row(
                    children: [
                      if (!isChatPage)
                        Expanded(
                            child: Stack(
                          children: [
                            Container(
                              height: 88.w,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: Colors.black),
                              ),
                              child: TextButton(
                                  onPressed: () {
                                    if (!vip && exerciseResidueTimes <= 0) {
                                      EasyLoading.showToast("练习模式试用次数已经用完啦~");
                                      return;
                                    }
                                    _jumpPage(context, model,
                                        IeltsTypeEnum.practiceType);
                                  },
                                  style: const ButtonStyle(
                                    textStyle: MaterialStatePropertyAll(
                                        TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.w700)),
                                  ),
                                  child: Center(
                                    child: Text("练习模式", style: style_1_28),
                                  )),
                            ),
                            if (!vip)
                              Positioned(
                                  right: 0,
                                  child: Container(
                                    padding: EdgeInsets.only(
                                        left: 8.w,
                                        right: 8.w,
                                        top: 4.w,
                                        bottom: 4.w),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(12.w),
                                          topRight: Radius.circular(12.w),
                                          bottomLeft: Radius.circular(2.w),
                                          bottomRight: Radius.circular(12.w)),
                                      color: const Color(0xFFFF8F1F),
                                    ),
                                    child: Text(
                                      "剩余$exerciseResidueTimes次",
                                      style: TextStyle(
                                          color: Colors.white, fontSize: 20.w),
                                    ),
                                  )),
                          ],
                        )),
                      if (!isChatPage) SizedBox(width: 20.w),
                      if (!isChatPage)
                        Expanded(
                          child: Stack(children: [
                            Container(
                              height: 88.w,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                gradient: const LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      Color(0xFF2693FF),
                                      Color(0xFF2693FF),
                                    ]),
                              ),
                              child: TextButton(
                                  onPressed: () {
                                    if (!vip && mockResidueTimes <= 0) {
                                      EasyLoading.showToast("模拟考试试用次数已经用完啦~");
                                      return;
                                    }

                                    _jumpPage(
                                        context, model, IeltsTypeEnum.mock);
                                  },
                                  style: const ButtonStyle(
                                    textStyle: MaterialStatePropertyAll(
                                        TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.w700)),
                                  ),
                                  child: Center(
                                    child: Text("模拟考试", style: style_1_28.copyWith(color: Colors.white)),
                                  )),
                            ),
                            if (!vip)
                              Positioned(
                                  right: 0,
                                  child: Container(
                                    padding: EdgeInsets.only(
                                        left: 8.w,
                                        right: 8.w,
                                        top: 4.w,
                                        bottom: 4.w),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(12.w),
                                          topRight: Radius.circular(12.w),
                                          bottomLeft: Radius.circular(2.w),
                                          bottomRight: Radius.circular(12.w)),
                                      color: const Color(0xFFFF8F1F),
                                    ),
                                    child: Text(
                                      "剩余$mockResidueTimes次",
                                      style: TextStyle(
                                          color: Colors.white, fontSize: 20.w),
                                    ),
                                  ))
                          ]),
                        ),
                      if (isChatPage)
                        Expanded(
                          child: Stack(children: [
                            Container(
                              height: 88.w,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                color: const Color(0xFF2693FF),
                              ),
                              child: TextButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                  style: const ButtonStyle(
                                    textStyle: MaterialStatePropertyAll(
                                        TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.w700)),
                                  ),
                                  child: Center(
                                    child: Text("我知道了", style: style_1_28.copyWith(color: Colors.white)),
                                  )),
                            )
                          ]),
                        ),
                    ],
                  ),
                )
                // Text(chatDialogType.fmtContent(totoalNum - num), style: style_2_28),
                // chatDialogType.fmtContent2(totoalNum - num)
              ],
            ),
          );
        }));
  }

  _jumpPage(BuildContext context, IeltsSpeakingTopicQuestionProvider provider,
      IeltsTypeEnum typeEnum) {
    Navigator.of(context).pop();

    Navigator.push(
      context,
      IeltsTalkPage.route(
        argument: IeltsTalkArgument(
          partCategoryTitle:
              provider.questionModel!.data!.partCategoryName ?? "",
          ieltsType: typeEnum,
          topicNameChinese: provider.questionModel!.data!.chineseTopicName ??
              ""
                  "",
          topicId: topicId,
          topicCode: provider.questionModel!.data!.topicCode ?? "",
        ),
      ),
    );
  }
}
