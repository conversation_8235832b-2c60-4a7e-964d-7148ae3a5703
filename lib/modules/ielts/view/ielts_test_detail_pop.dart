import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_user_info_model/ielts_user_info_model.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/modules/ielts/provider/ielts_test_detail_pop_provider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

/// 雅思成绩详情弹窗
class IeltsTestDetailPop extends StatefulWidget {
  final IeltsUserInfoModel ieltsUserInfoModel;
  const IeltsTestDetailPop({super.key, required this.ieltsUserInfoModel});

  @override
  State<IeltsTestDetailPop> createState() => _IeltsTestDetailPopState();
}

class _IeltsTestDetailPopState extends State<IeltsTestDetailPop> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<IeltsTestDetailPopProvider>(
      create: (_) {
        return IeltsTestDetailPopProvider(
          ieltsUserInfoModel: widget.ieltsUserInfoModel,
        );
      },
      child: Consumer<IeltsTestDetailPopProvider>(
        builder: (context, model, child) {
          var ieltsInfo = model.ieltsUserInfoModel?.data;

          return SingleChildScrollView(
            child: Container(
              color: Colors.white,
              padding: EdgeInsets.all(32.sp),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  /// 标题, 我的雅思口语水平
                  Text(
                    "我的雅思口语水平",
                    style: TextStyle(
                        fontSize: 32.sp,
                        fontWeight: FontWeight.w500,
                        color: ColorUtil.black1),
                  ),
                  SizedBox(height: 15.w),

                  /// flex row between 布局, 左边雅思得分, 右边显示超过多少人
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        ieltsInfo?.score ?? "",
                        style: TextStyle(
                            fontSize: 36.sp,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF00B578)),
                      ),
                      Text(
                        ieltsInfo?.encouragement ?? "",
                        style: style_2_24,
                      ),
                    ],
                  ),
                  SizedBox(height: 30.w),

                  /// 一个得分雷达图片, 上面分布着四个相对于雷达图的得分文案, 文案分上下, 上面是描述, 下面是得分
                  Stack(
                    // 宽度铺满
                    // fit: StackFit.loose,
                    // 水平垂直居中
                    alignment: Alignment.center,

                    children: [
                      /// 生成四个文案
                      _radarWidget(model.ieltsUserInfoModel)
                    ],
                  ),
                  SizedBox(height: 45.w),

                  /// 评分说明, 标题 + 文案列表
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "评分说明:",
                        style: TextStyle(
                            fontSize: 28.sp,
                            fontWeight: FontWeight.w500,
                            color: ColorUtil.black1),
                      ),
                      for (int i = 0;
                          i < (ieltsInfo?.desc?.rateDesc ?? []).length;
                          i++)
                        Padding(
                          padding: EdgeInsets.only(top: 15.w),
                          child: Text(
                            (ieltsInfo?.desc?.rateDesc ?? [])[i],
                            style: TextStyle(
                                fontSize: 28.sp,
                                fontWeight: FontWeight.w400,
                                color: ColorUtil.black2),
                          ),
                        )
                    ],
                  ),
                  SizedBox(height: 45.w),

                  /// 等级说明, 标题 + 文案列表
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "等级说明:",
                        style: TextStyle(
                            fontSize: 28.sp,
                            fontWeight: FontWeight.w500,
                            color: ColorUtil.black1),
                      ),
                      for (int i = 0;
                          i < (ieltsInfo?.desc?.rankDesc ?? []).length;
                          i++)
                        Padding(
                          padding: EdgeInsets.only(top: 15.w),
                          child: Text(
                            (ieltsInfo?.desc?.rankDesc ?? [])[i],
                            style: TextStyle(
                                fontSize: 28.sp,
                                fontWeight: FontWeight.w400,
                                color: ColorUtil.black2),
                          ),
                        )
                    ],
                  ),
                  SizedBox(height: 25.w),

                  /// 固定在底部定位的两个按钮, 一个是【重新测试】, 一个是【测试报告】, 水平居中
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      GestureDetector(
                        child: Container(
                          /// 内边距
                          padding: EdgeInsets.symmetric(horizontal: 90.w),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              border: Border.all(color: ColorUtil.black1),
                              borderRadius: BorderRadius.circular(12)),
                          // width: 155.0,
                          height: 88.w,
                          child: Center(
                            child: Text(
                              '重新测试',
                              style: style_1_28,
                            ),
                          ),
                        ),
                        onTap: () {
                          context
                              .read<IeltsTestDetailPopProvider>()
                              .showTestPop(context);
                        },
                      ),
                      SizedBox(
                        width: 20.w,
                      ),
                      GestureDetector(
                        child: Container(
                          /// 内边距
                          padding: EdgeInsets.symmetric(horizontal: 90.w),
                          decoration: BoxDecoration(
                              color: const Color(0xFF2693FF),
                              borderRadius: BorderRadius.circular(12)),
                          // width: 155.0,
                          height: 88.w,
                          child: Center(
                            child: Text(
                              '测试报告',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.w800
                              ),
                            ),
                          ),
                        ),
                        onTap: () {
                          context
                              .read<IeltsTestDetailPopProvider>()
                              .enterReport(context);
                        },
                      ),
                    ],
                  ),
                  SizedBox(height: 15.w),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _radarWidget(IeltsUserInfoModel? model) {
    String? fluencyScore = model?.data?.fluencyScore ?? "_";
    String? grammarScore = model?.data?.grammarScore ?? "_";
    String? pronunciationScore = model?.data?.pronunciationScore ?? "_";
    String? vocabularyScore = model?.data?.vocabularyScore ?? "_";
    return SizedBox(
      height: 500.w,
      width: double.infinity,
      child: Column(
        children: [
          const Spacer(),
          _radarScoreWidget("流利性和连贯性", fluencyScore),
          Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              const Spacer(),
              Column(
                children: [
                  _radarScoreWidget("     发音     ", pronunciationScore),
                  SizedBox(
                    height: 0.w,
                  )
                ],
              ),
              Image.network(
                model?.data?.radarChartUrl ?? "",
                width: 350.w,
                fit: BoxFit.fitWidth,
              ),
              Column(
                children: [
                  _radarScoreWidget("词汇多样性", vocabularyScore),
                  SizedBox(
                    width: 0.w,
                  )
                ],
              ),
              const Spacer(),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _radarScoreWidget("语法多样性及准确性", grammarScore),
            ],
          ),
          const Spacer(),
        ],
      ),
    );
  }

  Column _radarScoreWidget(String name, String score) {
    final numScore = NumUtil.getNumByValueStr(score) ?? 0;
    var index = numScore <= 6.5
        ? 0
        : numScore < 8
            ? 1
            : 2;
    return Column(
      /// 居中对齐
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          name,
          style: style_2_24,
        ),
        Text(
          score,
          style: TextStyle(
              fontSize: 32.sp,
              fontWeight: FontWeight.w700,
              color: [
                const Color(0xFF00B578),
                const Color(0xFF061B1F),
                const Color(0xFFFF8F1F),
              ][index]),
        ),
      ],
    );
  }
}
