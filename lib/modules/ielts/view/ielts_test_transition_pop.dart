import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/modules/ielts/provider/ielts_test_transition_pop_provider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

/// 雅思口语测试过度框
class IeltsTestTransitionPop extends StatefulWidget {
  final String? popType;
  final List<int>? topicIds;

  const IeltsTestTransitionPop({super.key, this.popType, this.topicIds});

  @override
  State<IeltsTestTransitionPop> createState() => _IeltsTestTransitionPopState();
}

class _IeltsTestTransitionPopState extends State<IeltsTestTransitionPop> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<IeltsTestTransitionPopProvider>(
      // create: (_) => IeltsTestTransitionPopProvider()..getTopicDetail2(widget.topicIds),
      create: (_) =>
          IeltsTestTransitionPopProvider()..refreshData(widget.topicIds),
      child: Consumer<IeltsTestTransitionPopProvider>(
        builder: (context, model, child) {
          var topicData = model.selectedPartData?.data;
          return Container(
            color: Colors.white,
            width: MediaQuery.of(context).size.width,
            height: 1303.w,
            padding: EdgeInsets.all(40.w),
            child: Column(
              children: [
                /// 两个 tab 可以切换, 一个是 part1, 一个是 part2&3)
                Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        // 切换到 part1
                        model.switchTab(0);
                      },
                      child: Stack(
                        alignment: Alignment.bottomCenter,
                        children: [
                          Text(
                            "Part1",
                            style:
                                model.tabIndex == 0 ? style_1_36 : style_2_32,
                          ),
                          if (model.tabIndex == 0)
                            Image.asset(
                              "images/text_bg.png",
                              width: 63.w,
                              height: 16.w,
                            )
                        ],
                      ),
                    ),
                    SizedBox(
                      width: 40.w,
                    ),
                    GestureDetector(
                      onTap: () {
                        // 切换到 part2&3
                        model.switchTab(1);
                      },
                      child: Stack(
                        alignment: Alignment.bottomCenter,
                        children: [
                          Text(
                            "Part2&3",
                            style:
                                model.tabIndex == 1 ? style_1_36 : style_2_32,
                          ),
                          if (model.tabIndex == 1)
                            Image.asset(
                              "images/text_bg.png",
                              width: 63.w,
                              height: 16.w,
                            )
                        ],
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: const Icon(
                        Icons.close,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
                Expanded(
                  child: ListView(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: 18.w),
                        child: Text(
                          topicData?.topicName ?? "",
                          style: TextStyle(
                              fontSize: 36.sp,
                              fontWeight: FontWeight.w500,
                              color: ColorUtil.black1),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 32.w),
                        color: const Color(0xFFDDEDF0),
                        height: 0.5.h,
                      ),
                      Padding(
                          padding: EdgeInsets.only(top: 18.w),
                          child: _testTransitionContentWidget(model)),
                    ],
                  ),
                ),

                /// 固定在底部定位的一个按钮, 文案是开始测试
                GestureDetector(
                  onTap: () {
                    if (widget.popType == null) {
                      Navigator.of(context).pop();
                    } else {
                      Navigator.of(context).pop();
                      model.enterChat(context);
                    }
                  },
                  child: Container(
                    width: 750.w,
                    height: 96.w,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        color: const Color(0xFF2693FF), // 修改为深蓝色背景
                        borderRadius: BorderRadius.circular(24.w)),
                    child: Text(
                      widget.popType == null ? "我知道了" : "开始测试",
                      style: TextStyle(
                          fontSize: 32.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.white), // 修改文字颜色为白色
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 根据不同的 tab 展示不同的内容, 内容结构是相同的
  /// 字段结构为 { topicName: strinh, questions: {list: {chinese: string, english: string}[], partName: string }[]}
  /// 上下展示, 标题, 问题列表

  Widget _testTransitionContentWidget(IeltsTestTransitionPopProvider model) {
    final topicData = model.selectedPartData?.data;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        /// 问题列表
        for (int i = 0; i < (topicData?.questions ?? []).length; i++)
          Padding(
            padding: EdgeInsets.only(top: 20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  topicData?.questions?[i].partName ?? "",
                  style: TextStyle(
                      fontSize: 36.w,
                      fontWeight: FontWeight.w500,
                      color: ColorUtil.black1),
                ),
                for (int j = 0; j < topicData!.questions![i].list!.length; j++)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 24.w,
                      ),
                      Text(
                          '${j + 1}. ${topicData.questions?[i].list?[j].english ?? ""}',
                          style: style_1_32_400),
                      SizedBox(height: 8.w),
                      Text(
                        topicData.questions?[i].list?[j].chinese ?? "",
                        style: style_2_24,
                      ),
                    ],
                  )
              ],
            ),
          )
      ],
    );
  }
}
