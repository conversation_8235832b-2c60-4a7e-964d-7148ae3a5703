import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ChatProgressDialogWidget extends StatelessWidget {
  final ChatDialogType chatDialogType;
  final int num;
  final int totoalNum;
  final Function? leftFunc;
  final Function? rightFunc;

  ChatProgressDialogWidget(
      {required this.chatDialogType,
      required this.num,
      required this.totoalNum,
      this.leftFunc,
      this.rightFunc});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      elevation: 0,
      title: Center(
        child: Text(chatDialogType.title, style: style_1_36),
      ),
      content: Container(
        constraints: BoxConstraints(
          maxWidth: 600.w,
          maxHeight: 510.w,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            chatDialogType.fmtSchedule(num, totoalNum),
            SizedBox(height: 10.w),
            // Text(chatDialogType.fmtContent(totoalNum - num), style: style_2_28),
            chatDialogType.fmtContent2(totoalNum - num)
          ],
        ),
      ),
      actions: [
        Row(
          children: [
            Expanded(
              child: Container(
                height: 88.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.black),
                ),
                child: TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      leftFunc!();
                    },
                    style: const ButtonStyle(
                      textStyle: MaterialStatePropertyAll(
                          TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
                    ),
                    child: Center(
                      child: Text("仍要退出", style: style_1_28),
                    )),
              ),
            ),
            SizedBox(width: 20.w),
            Expanded(
              child: Container(
                height: 88.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: const Color(0xFF2693FF),
                ),
                child: TextButton(
                    onPressed: () {
                      if (rightFunc != null) {
                        rightFunc!();
                      } else {
                        Navigator.of(context).pop();
                      }
                    },
                    style: const ButtonStyle(
                      textStyle: MaterialStatePropertyAll(
                          TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
                    ),
                    child: Center(
                      child: Text(
                        num > 0 && num == totoalNum
                            ? "查看报告"
                            : chatDialogType.fmtAckButtonText(),
                        style: style_1_28.copyWith(color: Colors.white),
                      ),
                    )),
              ),
            ),
          ],
        )
      ],
    );
  }
}

void showCustomDialog(BuildContext context, ChatDialogType chatDialogType,
    int num, int totoalNum) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return ChatProgressDialogWidget(
        chatDialogType: chatDialogType,
        num: num,
        totoalNum: totoalNum,
      );
    },
  );
}

enum ChatDialogType { ielts_practice, ielts_test, ielts_mock }

extension ChatDialogTypeExtension on ChatDialogType {
  String get title {
    switch (this) {
      case ChatDialogType.ielts_practice:
        //雅思练习进度
        return "练习进度";
      case ChatDialogType.ielts_test:
        //雅思口语测试进度
        return "测试进度";
      case ChatDialogType.ielts_mock:
        //雅思模考进度
        return "模考进度";
    }
  }

  // String fmtContent(int num) {
  //   switch (this) {
  //     case ChatDialogType.ielts_test:
  //       return "还有$num个问题就完成测试了，中途退出无法生成报告，且需要重新开始，再坚持下～";
  //     case ChatDialogType.ielts_mock:
  //       return "还有$num个问题就完成模考了，中途退出无法生成报告，且需要重新开始，再坚持下～";
  //     case ChatDialogType.ielts_practice:
  //       return "还有$num个问题就完成练习了，中途退出无法生成报告，且需要重新开始，再坚持下～";
  //   }
  // }

  Column fmtContent2(int num) {
    switch (this) {
      case ChatDialogType.ielts_test:
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text("还有$num个问题就完成测试了，", style: style_2_28),
            Text("中途退出无法生成报告，", style: style_2_28),
            Text("且需要重新开始，再坚持一下~", style: style_2_28),
          ],
        );
      case ChatDialogType.ielts_mock:
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text("还有$num个问题就完成模考了，", style: style_2_28),
            Text("中途退出无法生成报告，", style: style_2_28),
            Text("且需要重新开始，再坚持一下~", style: style_2_28),
          ],
        );
      case ChatDialogType.ielts_practice:
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text("还有$num个问题就完成练习了，", style: style_2_28),
            Text("中途退出无法生成报告，", style: style_2_28),
            Text("且需要重新开始，再坚持一下~", style: style_2_28),
          ],
        );
    }
  }

  Container fmtSchedule(int num, int totalNum) {
    switch (this) {
      case ChatDialogType.ielts_test:
        return Container(
          color: Color(0xFFF0F8FA),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text("完成进度", style: style_2_24),
              SizedBox(width: 10.w),
              Text("$num/$totalNum",
                  style: style_2_28.copyWith(color: Color(0xFFA7D3DB))),
            ],
          ),
        );
      case ChatDialogType.ielts_mock:
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Color(0xFFF0F8FA),
          ),
          width: 520.w,
          height: 80.w,
          child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(num, (index) {
                    return Image.asset("images/check_circle.png",
                        width: 18, height: 18);
                  }) +
                  List.generate(totalNum - num, (index) {
                    return Image.asset('images/un_check_circle.png',
                        width: 18, height: 18);
                  })

              // children: [
              //   Text("完成进度", style: style_2_24),
              //   SizedBox(width: 10),
              //   Text("$num/$totalNum",
              //       style: style_2_28.copyWith(color: Color(0xFFA7D3DB))),
              // ],
              ),
        );
      case ChatDialogType.ielts_practice:
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Color(0xFFF0F8FA),
          ),
          width: 520.w,
          height: 80.w,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text("完成进度", style: style_2_24),
              SizedBox(width: 10.w),
              Text("$num/$totalNum",
                  style: style_2_28.copyWith(color: Color(0xFFA7D3DB))),
            ],
          ),
        );
    }
  }

  String fmtAckButtonText() {
    switch (this) {
      case ChatDialogType.ielts_test:
        return "继续测试";
      case ChatDialogType.ielts_mock:
        return "继续模考";
      case ChatDialogType.ielts_practice:
        return "继续练习";
    }
  }
}
