import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_report_model/ielts_speaking_report_model.dart';
import 'package:provider/provider.dart';

class ButtonGroup extends StatefulWidget {
  final int size; // 按钮数量
  ButtonGroup({required this.size});

  @override
  _ButtonGroupState createState() => _ButtonGroupState();
}

class _ButtonGroupState extends State<ButtonGroup> {
  int? selectedIndex = 0; // 当前选中按钮的索引

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerLeft, // 按钮组左对齐
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: List<Widget>.generate(widget.size, (index) {
          bool isSelected = selectedIndex == index; // 判断按钮是否选中

          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  selectedIndex = index; // 设置选中按钮
                });
              },
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  gradient: isSelected
                      ? const LinearGradient(
                          colors: [Colors.blue, Colors.green],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        )
                      : null, // 选中时应用渐变背景
                  color: isSelected ? null : Colors.white, // 未选中时背景为白色
                ),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Text(
                  '问题${index + 1}', // 按钮文本
                  style: TextStyle(
                    color: isSelected
                        ? Colors.black
                        : const Color(0xFF646E70), // 选中时文字为黑色，否则为灰色
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
