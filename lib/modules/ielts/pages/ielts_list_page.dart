import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/iets_tab_model/topic_type_list.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/ielts/provider/ielts_list_provider.dart';
import 'package:flutter_app_kouyu/modules/ielts/view/ielts_speaking_topic_questions_widget.dart';
import 'package:flutter_app_kouyu/modules/my/pages/report_record_page.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/overlay_manager.dart';
import 'package:flutter_app_kouyu/widgets/vip_spical_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_topics_model/datum.dart'
    as IeltsSpeakingTopicsItem;

class IeltsListPage extends StatelessWidget {
  const IeltsListPage({super.key});

  static Route<void> route(String sentence, String collectId) {
    return MaterialPageRoute<void>(builder: (_) => const IeltsListPage());
  }

  @override
  Widget build(BuildContext context) {
    final double floatingButtonHeight = 144.w;

    return ChangeNotifierProvider<IeltsListProvider>(
      create: (_) => IeltsListProvider()
        ..getMyIeltsUserInfo()
        ..checkIsVip(),
      child: Consumer<IeltsListProvider>(
        builder: (context, model, child) {
          return Stack(
            children: [
              Container(
                width: double.infinity,
                height: double.infinity,
                color: const Color(0xFFFFFFFF),
              ),
              Image.asset(
                "images/scene_study_bg.png",
                width: double.infinity,
                fit: BoxFit.fill,
                alignment: Alignment.topCenter,
              ),
              Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  leadingWidth: 300,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  leading:
                      CustomAppbar.leftWidgetWithNoIcon(context, text: "雅思模考"),
                ),
                floatingActionButton:
                    _bottomFloatWidget(context, model, floatingButtonHeight),
                floatingActionButtonLocation:
                    FloatingActionButtonLocation.centerDocked,
                body: Padding(
                  padding: EdgeInsets.only(top: 16.w),
                  child: CustomScrollView(
                    slivers: [
                      _headerWidget(context, model),
                      SliverPersistentHeader(
                          pinned: true,
                          delegate: SliverHeaderDelegate(
                            height: 88.w,
                            child: _ieltsTypeWidget(model),
                          )),
                      SliverPersistentHeader(
                          pinned: true,
                          delegate: SliverHeaderDelegate(
                            height: 78.w,
                            child: _senceTypeWidget(model),
                          )),
                      SliverToBoxAdapter(
                        child: SizedBox(
                          height: 24.w,
                        ),
                      ),
                      SliverList.separated(
                        itemBuilder: (_, index) {
                          IeltsSpeakingTopicsItem.Datum item =
                              model.topicsModel!.data![index];
                          return _listItemWidget(context, item);
                        },
                        separatorBuilder: (context, index) {
                          return SizedBox(
                            height: 16.w,
                          );
                        },
                        itemCount: model.topicsModel?.data?.length ?? 0,
                      ),
                      SliverToBoxAdapter(
                          child: SizedBox(
                        height: 96.w,
                        child: Center(
                          child: Text(
                            "没有更多了~",
                            style: style_3_24.copyWith(
                                color: const Color(0xFF8BA2A6)),
                          ),
                        ),
                      )),
                      if (!model.isVip)
                        SliverToBoxAdapter(
                          child: SizedBox(
                            height: floatingButtonHeight,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _bottomFloatWidget(BuildContext context, IeltsListProvider model,
      double floatingButtonHeight) {
    int count = 0;
    if (model.selectIetsTab?.partCategoryCode == "part1") {
    count = max(
        count, model.ieltsUserInfoModel?.data?.part1ExerciseResidueTimes ?? 0);
    count =
        max(count, model.ieltsUserInfoModel?.data?.part1MockResidueTimes ?? 0);
    } else {
    count = max(
        count, model.ieltsUserInfoModel?.data?.part23ExerciseResidueTimes ?? 0);
    count =
        max(count, model.ieltsUserInfoModel?.data?.part23MockResidueTimes ?? 0);
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        GestureDetector(
          onTap: () {
            Navigator.of(context).pushNamed('/examination_prescription');
          },
          child: Image.asset(
            "images/yasi_prescription.png",
            fit: BoxFit.fill,
            width: 103.w,
            height: 103.w,
          ),
        ),
        GestureDetector(
          onTap: () {
            Navigator.of(context)
                .push(ReportRecordPage.route(null, type: ReportType.ielts));
          },
          child: Image.asset(
            "images/yasi_report.png",
            fit: BoxFit.fill,
            width: 103.w,
            height: 103.w,
          ),
        ),
        if (model.isVip)
          SizedBox(
            width: double.infinity,
            height: floatingButtonHeight,
          ),
        if (!model.isVip)
          Container(
            color: ColorUtil.separated,
            // height: 1.w,
            width: double.infinity,
          ),
        if (!model.isVip)
          Container(
            height: floatingButtonHeight,
            padding: EdgeInsets.only(left: 40.w, right: 40.w),
            width: double.infinity,
            color: Colors.white,
            child: Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () async {
                      _freeTestAction(context, model);
                    },
                    child: Stack(
                      children: [
                        Container(
                          height: 96.w,
                          margin: EdgeInsets.only(top: 10.w),
                          clipBehavior: Clip.none,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(24.w),
                              color: Colors.white,
                              border:
                                  Border.all(color: Colors.black, width: 1.w)),
                          child: Center(
                              child: Text(
                            "免费试用",
                            style: style_1_32,
                          )),
                        ),
                        Positioned(
                            right: 0,
                            child: Container(
                              padding: EdgeInsets.only(
                                  left: 8.w, right: 8.w, top: 4.w, bottom: 4.w),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(12.w),
                                    topRight: Radius.circular(12.w),
                                    bottomLeft: Radius.circular(2.w),
                                    bottomRight: Radius.circular(12.w)),
                                color: const Color(0xFFFF8F1F),
                              ),
                              child: Text(
                                "剩余$count次",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 20.w),
                              ),
                            )),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  width: 20.w,
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).pushNamed('/open_vip');
                    },
                    child: Container(
                      height: 96.w,
                      margin: EdgeInsets.only(top: 10.w),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24.w),
                        color: const Color(0xFFFFE2B8),
                      ),
                      child: Center(
                          child: Text(
                        "立即订阅",
                        style: style_1_32,
                      )),
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _listItemWidget(
      BuildContext context, IeltsSpeakingTopicsItem.Datum item) {
    IeltsListProvider provider = context.read<IeltsListProvider>();

    return GestureDetector(
      onTap: () async {
        final userInfo = await LoginUtil.userInfoModel();
        bool isVip = userInfo?.data?.memberType == 1;
        if (!isVip) {
          //体验到期
          OverlayManager.getInstance().showOverlay(
            true,
            builder: (close) => VipSpicalWidget(
              close: close,
            ),
          );
          return;
        }

        _listIitemAction(context, item, provider, isVip);
      },
      child: Container(
          padding: EdgeInsets.fromLTRB(14.w, 40.w, 14.w, 32.w),
          margin: EdgeInsets.fromLTRB(32.w, 0.w, 32.w, 0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24.w),
          ),
          child: Row(children: [
            CachedNetworkImage(
              imageUrl: item.imageUrl!,
              width: 80.w,
              height: 80.w,
            ),
            SizedBox(
              width: 16.w,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.chinese!,
                    style: style_1_28,
                  ),
                  Text(
                    item.english!,
                    style: style_2_20,
                  ),
                ],
              ),
            ),
            SizedBox(
              width: 16.w,
            ),
            if (!provider.isVip)
              Image.asset(
                "images/yasi_item_lock.png",
                width: 36.w,
                height: 36.w,
              )
          ])),
    );
  }

  Future<dynamic> _freeTestAction(
      BuildContext context, IeltsListProvider provider) async {
    final userInfo = await LoginUtil.userInfoModel();
    bool isVip = userInfo?.data?.memberType == 1;
    if (!isVip) {
      int count = 0;
      if (provider.selectIetsTab?.partCategoryCode == "part1") {
        count = max(count,
            provider.ieltsUserInfoModel?.data?.part1ExerciseResidueTimes ?? 0);
        count = max(
            count, provider.ieltsUserInfoModel?.data?.part1MockResidueTimes ?? 0);
      } else {
        count = max(count,
            provider.ieltsUserInfoModel?.data?.part23ExerciseResidueTimes ?? 0);
        count = max(count,
            provider.ieltsUserInfoModel?.data?.part23MockResidueTimes ?? 0);
      }
      if (count == 0) {
        //体验到期
        OverlayManager.getInstance().showOverlay(
          true,
          builder: (close) => VipSpicalWidget(
            close: close,
          ),
        );
        return;
      }
    }

    return showModalBottomSheet(
      // backgroundColor: Colors.black.withOpacity(0.8),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      // barrierColor: Colors.black.withOpacity(0.8),
      barrierColor: Colors.transparent, // 遮罩色设置为透明
      elevation: 0,
      context: context,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(bottom: 0.w),
          child: Container(
            clipBehavior: Clip.antiAlias,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(40.w),
                topRight: Radius.circular(40.w),
              ),
            ),
            child: Stack(
              children: [
                IeltsSpeakingTopicQuestionsDialogWidget(
                  topicId: provider.selectIetsTab?.partCategoryCode == "part1"
                      ? provider.ieltsUserInfoModel!.data!.trialPart1TopicId!
                      : provider.ieltsUserInfoModel!.data!.trialPart23TopicId!,
                  vip: isVip,
                  exerciseResidueTimes: provider.selectIetsTab?.partCategoryCode == "part1"
                      ? (provider.ieltsUserInfoModel?.data?.part1ExerciseResidueTimes ?? 0)
                      : (provider.ieltsUserInfoModel?.data?.part23ExerciseResidueTimes ?? 0),
                  mockResidueTimes: provider.selectIetsTab?.partCategoryCode == "part1"
                      ? (provider.ieltsUserInfoModel?.data?.part1MockResidueTimes ?? 0)
                      : (provider.ieltsUserInfoModel?.data?.part23MockResidueTimes ?? 0),
                ),
                Positioned(
                  top: 20.w,
                  right: 30.w,
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: const Icon(
                      Icons.close,
                      size: 18,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<dynamic> _listIitemAction(BuildContext context,
      IeltsSpeakingTopicsItem.Datum item, IeltsListProvider provider, isVip) {
    return showModalBottomSheet(
      // backgroundColor: Colors.black.withOpacity(0.8),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      // barrierColor: Colors.black.withOpacity(0.8),
      // barrierColor: Colors.transparent, // 遮罩色设置为透明
      elevation: 0,
      context: context,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(bottom: 0.w),
          child: Container(
            clipBehavior: Clip.antiAlias,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(40.w),
                topRight: Radius.circular(40.w),
              ),
            ),
            child: Stack(
              children: [
                IeltsSpeakingTopicQuestionsDialogWidget(
                  topicId: item.id!,
                  vip: isVip,
                  exerciseResidueTimes: provider.selectIetsTab?.partCategoryCode == "part1"
                      ? (provider.ieltsUserInfoModel?.data?.part1ExerciseResidueTimes ?? 0)
                      : (provider.ieltsUserInfoModel?.data?.part23ExerciseResidueTimes ?? 0),
                  mockResidueTimes: provider.selectIetsTab?.partCategoryCode == "part1"
                      ? (provider.ieltsUserInfoModel?.data?.part1MockResidueTimes ?? 0)
                      : (provider.ieltsUserInfoModel?.data?.part23MockResidueTimes ?? 0),
                ),
                Positioned(
                  top: 20.w,
                  right: 30.w,
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: const Icon(
                      Icons.close,
                      size: 18,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _ieltsTypeWidget(IeltsListProvider model) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          Row(
            children: ieltsTaps(model),
          ),
        ],
      ),
    );
  }

  List<Widget> ieltsTaps(IeltsListProvider model) {
    var list = <Widget>[];

    for (var element in model.ietsTabModel?.data ?? []) {
      list.add(GestureDetector(
        onTap: () {
          model.selectIetsTab = element;
        },
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Text(
              element.title,
              style: model.selectIetsTab == element ? style_1_36 : style_2_36,
            ),
            if (model.selectIetsTab == element)
              Image.asset(
                "images/text_bg.png",
                width: 63.w,
                height: 16.w,
              )
          ],
        ),
      ));
      list.add(SizedBox(
        width: 40.w,
      ));
    }
    return list;
  }

  Widget _headerWidget(BuildContext context, IeltsListProvider model) {
    if (model.ieltsUserInfoModel?.data?.score != null &&
        model.ieltsUserInfoModel!.data!.score!.isNotEmpty) {
      return SliverToBoxAdapter(
        child: Container(
          padding: EdgeInsets.all(32.w),
          margin: EdgeInsets.fromLTRB(32.w, 0.w, 32.w, 0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24.w),
            gradient: ColorUtil.blueToGreen2,
          ),
          child: GestureDetector(
            onTap: () {
              context.read<IeltsListProvider>().showIeltsTestDetailPop(context);
            },
            child: Row(
              children: [
                Text(
                  "雅思口语水平测试",
                  style: style_1_32.copyWith(color: Colors.white),
                ),
                const Spacer(),
                Center(
                  child: Text(
                    model.ieltsUserInfoModel?.data?.score ?? "",
                    style: style_1_32.copyWith(color: Colors.white),
                  ),
                ),
                SizedBox(
                  width: 10.w,
                ),
                Image.asset(
                  "images/arrow_right_black.png",
                  width: 10.w,
                  height: 20.w,
                  color: Colors.white,
                )
              ],
            ),
          ),
        ),
      );
    }

    // 如果请求还未结束, 那么不显示
    if (model.ieltsUserInfoModel == null) {
      return SliverToBoxAdapter(
        child: Container(
          height: 100.w,
        ),
      );
    }

    return SliverToBoxAdapter(
      child: Container(
        padding: EdgeInsets.all(32.w),
        margin: EdgeInsets.fromLTRB(32.w, 0.w, 32.w, 0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.w),
          gradient: ColorUtil.blueToGreen2,
        ),
        child: Row(
          children: [
            Image.asset(
              "images/yasi_header.png",
              width: 100.w,
              height: 100.w,
            ),
            SizedBox(
              width: 16.w,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "雅思口语水平测试",
                  style: style_1_32.copyWith(color: Colors.white),
                ),
                Text(
                  "测测你的口语水平",
                  style: style_1_24.copyWith(color: Color.fromRGBO(255, 255, 255, 0.8)),
                ),
              ],
            ),
            const Spacer(),
            GestureDetector(
              onTap: () {
                context
                    .read<IeltsListProvider>()
                    .showIeltsTestTransitionPop(context);
              },
              child: Container(
                padding: EdgeInsets.fromLTRB(24.w, 12.w, 24.w, 12.w),
                decoration: BoxDecoration(
                  color: const Color(0xFF2693FF),
                  borderRadius: BorderRadius.all(Radius.circular(16.w)),
                ),
                child: Center(
                  child: Text(
                    '开始测试',
                    style: style_1_28.copyWith(color: Colors.white),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _senceTypeWidget(IeltsListProvider model) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(top: 4.w, left: 32.w, right: 32.w, bottom: 12.w),
      child: SizedBox(
        height: 62.w,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemBuilder: (context, index) {
            TopicTypeList topicTypeList =
                model.selectIetsTab!.topicTypeList![index];
            bool selected = topicTypeList == model.selectedTopicTypeList;
            return GestureDetector(
              onTap: () {
                model.selectedTopicTypeList = topicTypeList;
              },
              child: Container(
                height: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                margin: EdgeInsets.only(right: 16.w),
                decoration: selected
                    ? BoxDecoration(
                        borderRadius: BorderRadius.circular(24.w),
                        color: const Color.fromRGBO(65, 192, 255, 0.12),
                        border: Border.all(
                          color: const Color.fromRGBO(65, 192, 255, 0.12),
                        ),
                      )
                    : BoxDecoration(
                        borderRadius: BorderRadius.circular(24.w),
                        color: Colors.transparent,
                        border: Border.all(
                          color: const Color.fromRGBO(0, 0, 0, 0.12),
                        ),
                      ),
                child: Text(
                  "${topicTypeList.topicTypeName}(${topicTypeList.topicCount})",
                  style: TextStyle(
                      fontSize: 28.sp,
                      fontWeight: selected ? FontWeight.w600 : FontWeight.normal,
                      color: selected
                          ? const Color(0xFF2693FF)
                          : const Color(0xFF272733)),
                ),
              ),
            );
          },
          itemCount: (model.selectIetsTab?.topicTypeList ?? []).length,
        ),
      ),
    );
  }
}

class SliverHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double height;
  final Widget child;
  SliverHeaderDelegate({required this.height, required this.child});
  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
