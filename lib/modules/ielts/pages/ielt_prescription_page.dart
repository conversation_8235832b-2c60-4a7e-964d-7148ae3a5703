import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/modules/ielts/provider/ielts_prescription_provider.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class IeltsPrescriptionPage extends StatelessWidget {
  const IeltsPrescriptionPage({super.key});

  static Route<void> route() {
    return MaterialPageRoute<void>(
        builder: (_) => const IeltsPrescriptionPage());
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<IeltsPrescriptionProvider>(
      create: (_) => IeltsPrescriptionProvider(),
      child: Consumer<IeltsPrescriptionProvider>(
        builder: (context, model, child) {
          return Scaffold(
            backgroundColor: const Color(0xFFF3FBFD),
            extendBody: true,
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              leadingWidth: 300,
              elevation: 0,
              leading: CustomAppbar.leftWidget(context, text: "考试指南"),
            ),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerDocked,
            body: CustomScrollView(
              slivers: [
                SliverPersistentHeader(
                    pinned: true,
                    delegate: SliverHeaderDelegate(
                      height: 370.w,
                      child: _headerWidget(context),
                    )),
                SliverPersistentHeader(
                  pinned: true,
                  delegate: SliverHeaderDelegate(
                    height: 94.w,
                    child: Container(
                      color: Colors.white, // 设置明确的背景色
                      child: _ieltsTypeWidget(model),
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: SizedBox(
                    height: 24.w,
                  ),
                ),
                SliverToBoxAdapter(
                  child: _ieltsPartWidget(model),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _ieltsTypeWidget(IeltsPrescriptionProvider model) {
    return Container(
      color: const Color(0xFFFFFFFF),
      padding: EdgeInsets.fromLTRB(32.w, 20.w, 32.w, 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          Row(
            children: ieltsTaps(model),
          ),
        ],
      ),
    );
  }

  Widget _ieltsPartWidget(IeltsPrescriptionProvider model) {
    final part = model.getPartData();
    return Container(
      margin: EdgeInsets.fromLTRB(40.w, 0.w, 40.w, 0.w),
      padding: EdgeInsets.all(32.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.w),
        color: const Color(0xFFFFFFFF),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                part?.title ?? "",
                style: style_1_32,
              ),
              const Spacer(),
              Image.network(
                part?.headImageUrl ?? "",
                width: 234.w,
              )
            ],
          ),
          Container(
            margin: EdgeInsets.only(top: 32.w),
            color: const Color(0xFFDDEDF0),
            height: 0.5.h,
          ),
          Padding(
            padding: EdgeInsets.only(top: 24.w),
            child: Row(
              children: [
                Text(
                  "对话时长:",
                  style: style_1_28,
                ),
                Text(
                  part?.talkTime ?? "",
                  style: style_1_28_400,
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 24.w),
            //child: Expanded(
            child: RichText(
              text: TextSpan(text: '', children: [
                TextSpan(text: "第一部分:", style: style_1_28),
                TextSpan(text: part?.partContent, style: style_1_28_400)
              ]),
            ),
            //),
          ),
          Padding(
            padding: EdgeInsets.only(top: 24.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "建议答题步骤:",
                  style: style_1_28,
                ),
                for (var i in part?.suggestStep ?? [])
                  Text(
                    i ?? "",
                    style: style_1_28_400,
                  ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 32.w),
            color: const Color(0xFFDDEDF0),
            height: 0.5.h,
          ),
          Padding(
            padding: EdgeInsets.only(top: 24.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "示例:",
                  style: style_1_28,
                ),
                Padding(
                    padding: EdgeInsets.only(top: 4.w),
                    child: Text(
                      part?.example ?? "",
                      style: style_1_28_400,
                    )),
                Padding(
                    padding: EdgeInsets.only(top: 6.w),
                    child: Text(
                      "1.表达观点:",
                      style: style_1_28,
                    )),
                Padding(
                    padding: EdgeInsets.only(top: 4.w),
                    child: Text(
                      part?.step1 ?? "",
                      style: style_1_28_400,
                    )),
                Padding(
                    padding: EdgeInsets.only(top: 6.w),
                    child: Text(
                      "2.解释原因:",
                      style: style_1_28,
                    )),
                Padding(
                    padding: EdgeInsets.only(top: 4.w),
                    child: Text(
                      part?.step2 ?? "",
                      style: style_1_28_400,
                    )),
                Padding(
                    padding: EdgeInsets.only(top: 6.w),
                    child: Text(
                      "3.举例说明:",
                      style: style_1_28,
                    )),
                Padding(
                    padding: EdgeInsets.only(top: 4.w),
                    child: Text(
                      part?.step3 ?? "",
                      style: style_1_28_400,
                    )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> ieltsTaps(IeltsPrescriptionProvider model) {
    var list = <Widget>[];

    for (var element in model.typeList) {
      list.add(GestureDetector(
        onTap: () {
          model.selectPrescriptionTab = element;
        },
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Text(
              element,
              style: model.selectPrescriptionTab == element
                  ? style_1_36
                  : style_2_36,
            ),
            if (model.selectPrescriptionTab == element)
              Image.asset(
                "images/text_bg.png",
                width: 63.w,
                height: 16.w,
              )
          ],
        ),
      ));
      list.add(SizedBox(
        width: 40.w,
      ));
    }
    return list;
  }

  Widget _headerWidget(BuildContext context) {
    return Container(
      child: Stack(
        children: [
          Image.asset(
            "images/bg_prescription.png",
            width: double.infinity,
            height: 370.w,
            fit: BoxFit.fill,
          ),
          Container(
            margin: EdgeInsets.fromLTRB(40.w, 0.w, 10.w, 0),
            child: Stack(
              children: [
                Column(
                  children: [
                    SizedBox(
                      height: kToolbarHeight +
                          MediaQuery.of(context).padding.top -
                          28.w,
                    ),
                    Image.asset(
                      "images/prescription.png",
                      height: 74.w,
                      fit: BoxFit.fitHeight,
                    ),
                  ],
                ),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: MediaQuery.of(context).padding.top +
                                kToolbarHeight +
                                24.w,
                          ),
                          Text(
                            "雅思口语考试指南",
                            style: style_1_32,
                          ),
                          Text(
                            "雅思考试是考生与考官之间进行一对一对话共计10~12分钟),分为三个部分",
                            style: style_1_24,
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(
                          top: MediaQuery.of(context).padding.top +
                              kToolbarHeight -
                              60.w),
                      child: Image.asset(
                        "images/prescription_head.png",
                        width: 241.w,
                        height: 241.w,
                      ),
                    )
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class SliverHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double height;
  final Widget child;
  SliverHeaderDelegate({required this.height, required this.child});
  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
