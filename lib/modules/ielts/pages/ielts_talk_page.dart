import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io' show Platform;
import 'package:flutter_app_kouyu/common/http/loading_status.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';
import 'package:flutter_app_kouyu/modules/chat/view/speed_settings_dialog.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielts_speaking_report_page.dart';
import 'package:flutter_app_kouyu/modules/ielts/provider/ielts_chat_cubit.dart';
import 'package:flutter_app_kouyu/modules/ielts/provider/ielts_speaking_report_provider.dart';
import 'package:flutter_app_kouyu/modules/ielts/view/chat_progress_widget.dart';
import 'package:flutter_app_kouyu/modules/ielts/view/ielts_soe_widget.dart';
import 'package:flutter_app_kouyu/modules/ielts/view/ielts_speaking_topic_questions_widget.dart';
import 'package:flutter_app_kouyu/modules/ielts/view/ielts_test_transition_pop.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_app_kouyu/widgets/selected_text.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gif_view/gif_view.dart';
import 'package:provider/provider.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_question_answer_model/data.dart';

enum IeltsTypeEnum {
  //练习模式
  practiceType(1, "练习"),
  //模拟考试
  mock(2, "模考"),
  //模拟考试
  test(3, "测试");

  final int type;
  final String name;
  const IeltsTypeEnum(this.type, this.name);

  static IeltsTypeEnum from(int? type) {
    return values.firstWhere((element) => element.type == type);
  }
}

class IeltsSpeakingTopicsDetail {
  String? chinese;
  int? id;
  String? imageUrl;
  String? partCategoryCode;
  String? topicCode;
  int? topicType;
}

class IeltsTalkArgument {
  final IeltsTypeEnum ieltsType;
  //part1 part2&3
  String partCategoryTitle;
  String topicNameChinese;
  final int topicId;
  final String topicCode;
  // 0: 自由聊天 1: 教学模式
  final int model = 0;
  final List<int>? topicIds;

  IeltsTalkArgument(
      {required this.partCategoryTitle,
      required this.topicNameChinese,
      required this.ieltsType,
      required this.topicId,
      required this.topicCode,
      this.topicIds});
}

class IeltsTalkPage extends StatelessWidget {
  final IeltsTalkArgument argument;
  const IeltsTalkPage({super.key, required this.argument});
  static Route<void> route({required IeltsTalkArgument argument}) {
    return MaterialPageRoute<void>(
        builder: (_) => IeltsTalkPage(
              argument: argument,
            ));
  }

  void pop(BuildContext context) async {
    IeltsChatProvider cubit = context.read<IeltsChatProvider>();
    showDialog(
      context: context,
      builder: (BuildContext context) {
        ChatDialogType chatDialogType = ChatDialogType.ielts_mock;
        if (argument.ieltsType == IeltsTypeEnum.practiceType) {
          chatDialogType = ChatDialogType.ielts_practice;
        } else if (argument.ieltsType == IeltsTypeEnum.test) {
          chatDialogType = ChatDialogType.ielts_test;
        } else if (argument.ieltsType == IeltsTypeEnum.mock) {
          chatDialogType = ChatDialogType.ielts_mock;
        }
        return ChatProgressDialogWidget(
          chatDialogType: chatDialogType,
          num: cubit.currentQuestionIndex,
          totoalNum: cubit.totalQuestionCount,
          leftFunc: () {
            Navigator.of(context).pop();
          },
          rightFunc: () {
            if (!cubit.finished) {
              Navigator.of(context).pop();
            } else {
              //查看报告
              Navigator.of(context).pop();
              _createReport(context, cubit.list.first.practiceId ?? 0);
            }
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (didPop) {
          return;
        }
        pop(context);
      },
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => VoiceCubit(),
          ),
          ChangeNotifierProvider(
            create: (context) => IeltsChatProvider(
              context,
              argument: argument,
            )..intiGreetingStream(),
          ),
        ],
        child: Consumer<IeltsChatProvider>(builder: (context, model, _) {
          return Scaffold(
            extendBodyBehindAppBar: true,
            backgroundColor: const Color(0xFFF3FBFD),
            appBar: TalkAPPBar(
              argument: argument,
              finishTask: false,
              conversationId: 0,
              canCreateReport: model.list.length >= 6 && argument.model == 0,
              currentQuestionIndex: model.currentQuestionIndex,
              totalQuestionCount: model.totalQuestionCount,
              pop: (p0) => pop(context),
            ),
            body: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                FocusScope.of(context).unfocus(); // 点击其他地方收起键盘
              },
              child: Stack(
                children: [
                  Column(
                    children: [
                      Expanded(
                        child: Align(
                          alignment: Alignment.topCenter,
                          child: _chatListView(),
                        ),
                      ),
                      model.finished
                          ? _getReportWidget(
                              context, model.list.first.practiceId ?? 0)
                          : BlocBuilder<VoiceCubit, VoiceState>(
                              builder: (context, state) {
                                return state.voiceType == VoiceTypeEnum.init
                                    ? _chatWidget()
                                    : VoiceWidget(
                                        model: argument.model,
                                      );
                              },
                            )
                    ],
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _chatListView() {
    return Consumer<IeltsChatProvider>(
      builder: (context, state, _) {
        return ListView.builder(
          reverse: true,
          shrinkWrap: true,
          itemBuilder: (BuildContext context, int index) {
            int total = state.list.length +
                ((state.chatHintStatus == LoadingStatus.inProgress ||
                        state.chatHintList.isNotEmpty)
                    ? 1
                    : 0);
            if (index == 0) {
              if (state.chatHintList.isNotEmpty) {
                //最后一个且有跟读
                return _followListWidget(state.chatHintList);
              } else if (state.chatHintStatus == LoadingStatus.inProgress) {
                return _followListLoadingWidget();
              }
            }

            IeltsTalkModel model;
            int chatIndex = total - index - 1;

            model = state.list[chatIndex];
            if (model.type == 0) {
              //question
              return Padding(
                padding: EdgeInsets.only(left: 40.w, right: 64.w, bottom: 10.w),
                child: ChatListItemLeft(
                  ieltsTalkModel: model,
                  index: chatIndex,
                ),
              );
            } else {
              return Padding(
                padding: EdgeInsets.only(left: 64.w, right: 40.w, bottom: 10.w),
                child: ChatListItemRight(
                  ieltsTalkModel: model,
                  index: chatIndex,
                  argument: argument,
                ),
              );
            }
          },
          itemCount: state.list.length +
              ((state.chatHintStatus == LoadingStatus.inProgress ||
                      state.chatHintList.isNotEmpty)
                  ? 1
                  : 0),
        );
      },
    );
  }

  Widget _followListLoadingWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 6,
        ),
        Padding(
          padding: const EdgeInsets.only(left: 20),
          child: Text(
            '你可以尝试这样回答${LoginUtil.currentUserInfoModel()?.data?.userSettings?.characterName ?? "小西老师"}',
            style: style_2_28,
          ),
        ),
        SizedBox(
          height: 24.w,
        ),
        Container(
          height: 48,
          width: double.infinity,
          margin: const EdgeInsets.only(left: 20, right: 20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.all(Radius.circular(12)),
            border: Border.all(
              color: const Color(0xFF84E9FF),
              width: 3,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(
                width: 12,
              ),
              GifView.asset(
                "images/hint_loading.gif",
                height: 27,
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 12,
        ),
      ],
    );
  }

  Widget _followListWidget(List<Data> chatHintList) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 6,
        ),
        Padding(
          padding: const EdgeInsets.only(left: 20),
          child: Text(
            '你可以尝试这样回答${LoginUtil.currentUserInfoModel()?.data?.userSettings?.characterName ?? "小西老师"}',
            style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                color: Color(0xFF646E70)),
          ),
        ),
        const SizedBox(
          height: 12,
        ),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: IntrinsicHeight(
            child: Row(
              children: [
                const SizedBox(
                  width: 16,
                ),
                ...List.generate(chatHintList.length, (index) {
                  var data = chatHintList[index];
                  return ChatHitItemWidget(
                    hitModel: data,
                  );
                }),
                const SizedBox(
                  width: 16,
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _getReportWidget(BuildContext context, num practiceId) {
    return Column(
      children: [
        Center(
          child: Image.asset("images/lianxi_success.png",
              width: 227.w, height: 150.w),
        ),
        SizedBox(
          height: 32.w,
        ),
        Container(
          height: 88.w,
          margin: const EdgeInsets.fromLTRB(30, 0, 40, 30),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: const Color(0xFF2693FF), // 修改1：渐变色改为深蓝色背景
          ),
          child: TextButton(
              onPressed: () {
                //查看报告
                _createReport(context, practiceId);
              },
              style: const ButtonStyle(
                textStyle: MaterialStatePropertyAll(
                    TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
              ),
              child: Center(
                child: Text("查看报告", 
                    style: style_1_28.copyWith(color: Colors.white)), // 修改2：文字颜色改为白色
              )),
        ),
      ],
    );
  }

  _createReport(BuildContext context, num practiceId) {
    //创建报告前判断有没有语法分析和soe未结束的
    IeltsChatProvider cubit = context.read<IeltsChatProvider>();
    for (var chat in cubit.list) {
      if (chat.analyzeStatus?.status == LoadingStatus.inProgress) {
        EasyLoading.showToast("语法检测未结束，请稍后点击查看报告！");
        return;
      }
      if (chat.soeStatus?.status == LoadingStatus.inProgress) {
        EasyLoading.showToast("语音评测未结束，请稍后点击查看报告！");
        return;
      }
    }

    //查看报告
    Navigator.push(
        context,
        MaterialPageRoute<void>(
            builder: (_) => IeltsSpeakingReportPage(
                  practiceId: practiceId,
                  type: IeltsSpeakingReportDetailType.create,
                )));
  }

  Widget _chatWidget() {
    return BlocBuilder<VoiceCubit, VoiceState>(
      builder: (context, state) {
        return Container(
          padding: EdgeInsets.all(8.w),
          margin: EdgeInsets.only(bottom: 48.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.w),
          ),
          child: argument.ieltsType == IeltsTypeEnum.mock ||
                  argument.ieltsType == IeltsTypeEnum.test
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(width: 40.w),
                    _speakWidget(context),
                    SizedBox(width: 40.w),
                  ],
                )
              : Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(bottom: 16.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(width: 40.w),
                          GestureDetector(
                            onTap: () {
                              if (LoadingStatus.inProgress ==
                                  context
                                      .read<IeltsChatProvider>()
                                      .list
                                      .last
                                      .replyStatus
                                      ?.status) {
                                EasyLoading.showToast('正在回复...');
                                return;
                              }
                              context
                                  .read<VoiceCubit>()
                                  .chineseHelp(context.read<IeltsChatProvider>());
                              context.read<IeltsChatProvider>().cleanHit();
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(24.w),
                                  border: Border.all(color: separated, width: 2.w)),
                              child: Image.asset(
                                "images/chinese_help_btn.png",
                                width: 199.w,
                                height: 72.w,
                              ),
                            ),
                          ),
                          SizedBox(width: 16.w),
                          GestureDetector(
                            onTap: () {
                              FocusScope.of(context).unfocus(); // 点击其他地方收起键盘
                              context.read<IeltsChatProvider>().getChatHint();
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(24.w),
                                  border: Border.all(color: separated, width: 2.w)),
                              child: Image.asset(
                                "images/hit_btn.png",
                                width: 199.w,
                                height: 72.w,
                              ),
                            ),
                          ),
                          SizedBox(width: 40.w),
                        ],
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(width: 40.w),
                        _speakWidget(context),
                        SizedBox(width: 40.w),
                      ],
                    ),
                  ],
                ),
        );
      },
    );
  }

  Expanded _speakWidget(BuildContext context) {
    return Expanded(
      child: Container(
        height: Platform.isIOS ? 96.w : 96.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: const Color(0xFF2693FF),
        ),
        child: TextButton(
            onPressed: () {
              if (LoadingStatus.inProgress ==
                  context
                      .read<IeltsChatProvider>()
                      .list
                      .last
                      .replyStatus
                      ?.status) {
                EasyLoading.showToast('正在回复...');

                return;
              }
              //教学模式支持中英文
              context.read<VoiceCubit>().speakEn(
                  isZh: argument.model == 1,
                  chatCubit: context.read<IeltsChatProvider>());
              context.read<IeltsChatProvider>().cleanHit();
            },
            style: const ButtonStyle(
              textStyle: MaterialStatePropertyAll(
                  TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
            ),
            child: Row(
              children: [
                const Spacer(),
                Text("点击说话", style: style_1_28.copyWith(color: Colors.white)),
                const Spacer(),
              ],
            )),
      ),
    );
  }
}

class ChatHitItemWidget extends StatelessWidget {
  final Data? hitModel;
  const ChatHitItemWidget({super.key, required this.hitModel});

  @override
  Widget build(BuildContext context) {
    return Container(
        width: 300,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.all(Radius.circular(12)),
          border: Border.all(
            color: const Color(0xFF84E9FF),
            width: 3.w,
          ),
        ),
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // 使Column自适应内容大小
          children: [
            // 文本内容区域 - 自适应高度
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.fromLTRB(6, 4, 6, 4),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: const Color(0xFFDDEDF0),
                        width: 0.5,
                      ),
                    ),
                    child: Text(hitModel?.label ?? "_",
                        style: const TextStyle(
                            color: Color(0xFFA7D3DB),
                            fontSize: 10,
                            fontWeight: FontWeight.w400)),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    hitModel?.english ?? "",
                    style: TextStyle(
                      fontWeight: FontWeight.w400, 
                      fontSize: 28.w,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    hitModel?.chinese ?? "",
                    style: Platform.isIOS 
                      ? style_2_24 
                      : style_2_24.copyWith(fontSize: 20.w),
                  ),
                ],
              ),
            ),
            // 底部区域 - 与底边框间距更小
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 8), // 减小与上方内容的间距
                _lineWidget(),
                SizedBox(height: 16.w), // 减小与底部按钮的间距
                Row(
                  children: [
                    PlayButtonWidget(
                        audioUrl: hitModel?.audioUrl ?? "",
                        style: PlayButtonStyle.blue),
                    SizedBox(width: 20.w),
                    GestureDetector(
                        onTap: () {
                          FocusScope.of(context).unfocus(); // 点击其他地方收起键盘
                          context
                              .read<IeltsChatProvider>()
                              .collectionHitModel(hitModel!);
                        },
                        child: Image(
                          image: AssetImage(hitModel!.collected == true
                              ? "images/chat_collected.png"
                              : "images/chat_collect_blue.png"),
                          width: 48.w,
                          height: 48.w,
                        )),
                    SizedBox(width: 20.w),
                    GestureDetector(
                      onTap: () {
                        FocusScope.of(context).unfocus(); // 点击其他地方收起键盘
                        Clipboard.setData(
                            ClipboardData(text: hitModel?.english ?? ""));
                        EasyLoading.showToast("已复制");
                      },
                      child: Image(
                        image: const AssetImage("images/chat_copy_blue.png"),
                        width: 48.w,
                        height: 48.w,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        FocusScope.of(context).unfocus(); // 点击其他地方收起键盘
                        context.read<VoiceCubit>().followRead(
                            hitModel?.english, context.read<IeltsChatProvider>());
                        context.read<IeltsChatProvider>().cleanHit();
                      },
                      child: Image.asset(
                        "images/fllow_btn.png",
                        height: 28,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ));
  }
}

class ChatListItemLeft extends StatelessWidget {
  final IeltsTalkModel? ieltsTalkModel;
  final int index;
  const ChatListItemLeft(
      {super.key, required this.ieltsTalkModel, required this.index});

  @override
  Widget build(BuildContext context) {
    if (ieltsTalkModel?.replyStatus?.status == LoadingStatus.inProgress) {
      return Container(
          margin: const EdgeInsets.only(top: 6, bottom: 6),
          padding:
              const EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: const Color(0xFFDCF4FA),
          ),
          child: Row(
            children: [
              GifView.asset(
                "images/reply_loading.gif",
                fit: BoxFit.fill,
                width: 36,
                height: 27,
                repeat: ImageRepeat.repeat,
              ),
            ],
          ));
    }
    return Container(
        margin: const EdgeInsets.only(top: 6, bottom: 0),
        padding:
            const EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: const Color(0xFFDCF4FA),
        ),
        child: ieltsTalkModel?.replyStatus?.status == LoadingStatus.inProgress
            ? GifView.asset(
                "images/reply_loading.gif",
                fit: BoxFit.fitWidth,
                width: 16,
                height: 16,
                repeat: ImageRepeat.repeat,
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SelectableTextUtil.editableText(
                    context,
                    text: ieltsTalkModel?.answer ?? "",
                    style: TextStyle(
                        fontWeight: FontWeight.w400, 
                        fontSize: Platform.isIOS ? 16 : 14),
                  ),
                  ieltsTalkModel?.translateStatus?.status ==
                          LoadingStatus.inProgress
                      ? GifView.asset(
                          "images/samll_loading_blue.gif",
                          fit: BoxFit.fitWidth,
                          width: 16,
                          height: 16,
                          repeat: ImageRepeat.repeat,
                        )
                      : Visibility(
                          visible: ieltsTalkModel?.showTranslateText == true &&
                              ieltsTalkModel?.translateText != null &&
                              ieltsTalkModel!.translateText!.isNotEmpty,
                          child: Text(
                            ieltsTalkModel?.translateText ?? "",
                            style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontSize: Platform.isIOS ? 12 : 10,
                                color: Color(0xFF646E70)),
                          ),
                        ),
                  SizedBox(
                    height: 24.w,
                  ),
                  _lineWidget(),
                  SizedBox(
                    height: 24.w,
                  ),
                  Row(
                    children: [
                      PlayButtonWidget(
                          audioUrl: ieltsTalkModel?.audioUrl ?? "",
                          style: PlayButtonStyle.blue),
                      SizedBox(
                        width: 20.w,
                      ),
                      GestureDetector(
                          onTap: () {
                            FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                            context
                                .read<IeltsChatProvider>()
                                .translate(ieltsTalkModel!, index);
                          },
                          child: Image(
                            image: const AssetImage(
                                "images/chat_translate_blue.png"),
                            width: 48.w,
                            height: 48.w,
                          )),
                      SizedBox(
                        width: 20.w,
                      ),
                      Visibility(
                        //教学模式不显示收藏
                        visible:
                            context.read<IeltsChatProvider>().argument.model !=
                                1,
                        child: GestureDetector(
                            onTap: () {
                              FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                              context
                                  .read<IeltsChatProvider>()
                                  .collection(ieltsTalkModel!, index);
                            },
                            child: Image(
                              image: AssetImage(
                                  ieltsTalkModel!.collected == true
                                      ? "images/chat_collected.png"
                                      : "images/chat_collect_blue.png"),
                              width: 48.w,
                              height: 48.w,
                            )),
                      ),
                      Visibility(
                        visible:
                            context.read<IeltsChatProvider>().argument.model !=
                                1,
                        child: SizedBox(
                          width: 20.w,
                        ),
                      ),
                      GestureDetector(
                          onTap: () {
                            FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                            Clipboard.setData(ClipboardData(
                                text: ieltsTalkModel?.answer ?? ""));
                            EasyLoading.showToast("已复制");
                          },
                          child: Image(
                            image:
                                const AssetImage("images/chat_copy_blue.png"),
                            width: 48.w,
                            height: 48.w,
                          )),
                      SizedBox(
                        width: 20.w,
                      ),
                    ],
                  ),
                ],
              ));
  }
}

Container _lineWidget({double? padding}) {
  return Container(
    height: 1.w,
    margin: EdgeInsets.only(left: padding ?? 0, right: padding ?? 0),
    width: double.infinity,
    color: ColorUtil.separated,
  );
}

class ChatListItemRight extends StatelessWidget {
  final IeltsTalkModel ieltsTalkModel;
  final int index;
  final IeltsTalkArgument argument;

  const ChatListItemRight(
      {super.key,
      required this.ieltsTalkModel,
      required this.index,
      required this.argument});

  @override
  Widget build(
    BuildContext context,
  ) {
    return Container(
        margin: const EdgeInsets.only(top: 6, bottom: 6),
        padding:
            const EdgeInsets.only(left: 12, right: 12, top: 12, bottom: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SelectableTextUtil.editableText(
              context,
              text: ieltsTalkModel.answer ?? "",
              style: TextStyle(
                fontWeight: FontWeight.w400, 
                fontSize: Platform.isIOS ? 16 : 14),
            ),
            SizedBox(
              height: 24.w,
            ),
            _lineWidget(),
            SizedBox(
              height: 24.w,
            ),
            Row(
              children: [
                PlayButtonWidget(
                    audioUrl: ieltsTalkModel.audioUrl ?? "",
                    style: PlayButtonStyle.blue),
                SizedBox(
                  width: 20.w,
                ),
                PlayButtonWidget(
                  text: ieltsTalkModel.answer,
                  style: PlayButtonStyle.blue,
                  image: Image(
                    image: const AssetImage("images/chat_ai_blue.png"),
                    width: 48.w,
                    height: 48.w,
                  ),
                ),
                SizedBox(
                  width: 20.w,
                ),
                GestureDetector(
                    onTap: () {
                      FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                      context
                          .read<IeltsChatProvider>()
                          .collection(ieltsTalkModel, index);
                    },
                    child: Image(
                      image: AssetImage(ieltsTalkModel.collected == true
                          ? "images/chat_collected.png"
                          : "images/chat_collect_blue.png"),
                      width: 48.w,
                      height: 48.w,
                    )),
                SizedBox(
                  width: 20.w,
                ),
                GestureDetector(
                  onTap: () {
                    FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                    Clipboard.setData(
                        ClipboardData(text: ieltsTalkModel.answer ?? ""));
                    EasyLoading.showToast("已复制");
                  },
                  child: Image(
                    image: const AssetImage("images/chat_copy_blue.png"),
                    width: 48.w,
                    height: 48.w,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                    if (ieltsTalkModel.soeModel == null) {
                      return;
                    }
                    showModalBottomSheet(
                        isScrollControlled: true,
                        useSafeArea: true,
                        constraints: BoxConstraints(
                            maxHeight: MediaQuery.of(context).size.height -
                                MediaQuery.of(context).padding.top -
                                140),
                        shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(20),
                                topRight: Radius.circular(20))),
                        context: context,
                        builder: (context) {
                          return IeltsSoeWidget(
                            soeModel: ieltsTalkModel.soeModel,
                            analyzeModel: ieltsTalkModel.analyzeModel,
                            talkModel: ieltsTalkModel,
                            argument: argument,
                          );
                        });
                  },
                  child: Row(
                    children: [
                      Visibility(
                        visible: (ieltsTalkModel.analyzeStatus?.status !=
                                    LoadingStatus.failure ||
                                ieltsTalkModel.analyzeStatus?.message !=
                                    null) &&
                            argument.model == 0,
                        child: Row(
                          children: [
                            if (ieltsTalkModel.analyzeStatus?.status !=
                                LoadingStatus.inProgress)
                              (ieltsTalkModel.analyzeModel?.data?.correct ==
                                          false ||
                                      ieltsTalkModel.analyzeStatus?.status ==
                                          LoadingStatus.failure)
                                  ? Image.asset(
                                      "images/chat_no_good.png",
                                      width: 16,
                                      height: 16,
                                    )
                                  : Image.asset(
                                      "images/chat_good_logo.png",
                                      width: 16,
                                      height: 16,
                                    ),
                            const SizedBox(
                              width: 2,
                            ),
                            ieltsTalkModel.analyzeStatus?.status ==
                                    LoadingStatus.inProgress
                                ? GifView.asset(
                                    "images/samll_loading.gif",
                                    fit: BoxFit.fitWidth,
                                    width: 16,
                                    height: 16,
                                    repeat: ImageRepeat.repeat,
                                  )
                                : Text(
                                    ieltsTalkModel.analyzeStatus?.message ??
                                        (ieltsTalkModel.analyzeModel?.data
                                                    ?.correct ==
                                                true
                                            ? "语法正确"
                                            : "语法错误"),
                                    style: const TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w400,
                                        color: Color(0xff646E70)),
                                  ),
                          ],
                        ),
                      ),
                      SizedBox(
                        width: 16.w,
                      ),
                      Visibility(
                        visible: ieltsTalkModel.soeStatus?.status !=
                                LoadingStatus.failure ||
                            ieltsTalkModel.soeStatus?.message != null,
                        child: Row(
                          children: [
                            if (ieltsTalkModel.soeStatus?.status !=
                                LoadingStatus.inProgress)
                              ((ieltsTalkModel.soeModel?.data?.suggestedScore ??
                                              100) <
                                          80 ||
                                      ieltsTalkModel.soeStatus?.status ==
                                          LoadingStatus.failure)
                                  ? Image.asset(
                                      "images/chat_no_good.png",
                                      width: 16,
                                      height: 16,
                                    )
                                  : Image.asset(
                                      "images/chat_good_logo.png",
                                      width: 16,
                                      height: 16,
                                    ),
                            const SizedBox(
                              width: 2,
                            ),
                            ieltsTalkModel.soeStatus?.status ==
                                    LoadingStatus.inProgress
                                ? GifView.asset(
                                    "images/samll_loading.gif",
                                    fit: BoxFit.fitWidth,
                                    width: 16,
                                    height: 16,
                                    repeat: ImageRepeat.repeat,
                                  )
                                : Text(
                                    ieltsTalkModel.soeModel?.data
                                                ?.suggestedScore !=
                                            null
                                        ? "发音${ieltsTalkModel.soeModel?.data?.suggestedScore}分"
                                        : ieltsTalkModel.soeStatus?.message ??
                                            "",
                                    style: const TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w400,
                                        color: Color(0xff646E70)),
                                  ),
                            Image.asset(
                              "images/right_arrow_gray.png",
                              width: 36.w,
                              height: 36.w,
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          ],
        ));
  }
}

class VoiceWidget extends StatelessWidget {
  // model 0: 自由聊天 1: 教学模式
  final int model;
  const VoiceWidget({
    super.key,
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VoiceCubit, VoiceState>(
      builder: (context, state) {
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          ScrollController scrollController =
              context.read<VoiceCubit>().scrollController;
          scrollController.jumpTo(scrollController.position.maxScrollExtent);
        });
        final maxSeconds = context.read<VoiceCubit>().maxSeconds;
        return Offstage(
          offstage: state.voiceType == VoiceTypeEnum.init,
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Column(
                children: [
                  Visibility(
                    visible: state.voiceType == VoiceTypeEnum.follow,
                    child: Container(
                      padding: Platform.isIOS 
                        ? const EdgeInsets.fromLTRB(12, 40, 12, 40)
                        : const EdgeInsets.fromLTRB(12, 30, 12, 30),
                      margin: Platform.isIOS
                        ? const EdgeInsets.fromLTRB(30, 0, 40, 23)
                        : const EdgeInsets.fromLTRB(30, 0, 40, 18),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: const LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Color(0xFF98ECEC),
                              Color(0xFF84E9FF),
                              Color(0xFF9BE1FF)
                            ]),
                      ),
                      child: Center(
                        child: Text(state.followTest ?? "_",
                            style: TextStyle(
                                color: const Color(0xFF061B1F),
                                fontSize: Platform.isIOS ? 16 : 14,
                                fontWeight: FontWeight.w400)),
                      ),
                    ),
                  ),
                  Container(
                    color: Colors.transparent,
                    height: 2.w,
                    width: double.infinity,
                    child: Center(
                      child: LinearProgressIndicator(
                        value: state.seconds / maxSeconds,
                        minHeight: 2.w,
                        backgroundColor: Colors.white,
                        color: Colors.cyan,
                      ),
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.only(left: 20, right: 20, top: 12),
                    color: Colors.white,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        TextField(
                          maxLines: Platform.isIOS ? 5 : 4,
                          readOnly: true,
                          controller:
                              context.read<VoiceCubit>().textEditingController,
                          scrollController:
                              context.read<VoiceCubit>().scrollController,
                          style: TextStyle(
                              fontSize: Platform.isIOS ? 16 : 14,
                              fontWeight: FontWeight.w400,
                              color: const Color(0xFF061B1F)),
                          textAlign: TextAlign.center,
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            focusedBorder: InputBorder.none,
                          ),
                        ),
                        Visibility(
                            visible:
                                state.voiceType == VoiceTypeEnum.cnTanslated,
                            child: const SizedBox(height: 10)),
                        Visibility(
                            visible:
                                state.voiceType == VoiceTypeEnum.cnTanslated,
                            child: Align(
                              alignment: Alignment.center,
                              child: GestureDetector(
                                onTap: () => CommonUtils.playVideo(state
                                    .chToEnAudioModel?.data?.replyAudioUrl),
                                child: RichText(
                                    maxLines: 3,
                                    text: TextSpan(
                                        text: state.chToEnAudioModel?.data
                                                ?.reply ??
                                            "",
                                        style: TextStyle(
                                            color: const Color(0xFF061B1F),
                                            fontWeight: FontWeight.w400,
                                            fontSize: Platform.isIOS ? 16 : 14),
                                        children: [
                                          WidgetSpan(
                                              alignment:
                                                  PlaceholderAlignment.middle,
                                              child: PlayButtonWidget(
                                                style: PlayButtonStyle.white,
                                                audioUrl: state.chToEnAudioModel
                                                    ?.data?.replyAudioUrl,
                                              )),
                                        ])),
                              ),
                            )),
                        const SizedBox(height: 20),
                        Visibility(
                          visible: state.voiceType == VoiceTypeEnum.cnTanslated,
                          child: Container(
                            height: Platform.isIOS ? 52 : 45,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              gradient: const LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Color(0xFF98ECEC),
                                    Color(0xFF84E9FF),
                                    Color(0xFF9BE1FF)
                                  ]),
                            ),
                            child: TextButton(
                                onPressed: () {
                                  context.read<VoiceCubit>().followRead(
                                      state.chToEnAudioModel?.data?.reply,
                                      context.read<IeltsChatProvider>());
                                  context.read<IeltsChatProvider>().cleanHit();
                                },
                                style: const ButtonStyle(
                                  textStyle: MaterialStatePropertyAll(TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.w700)),
                                ),
                                child: Center(
                                  child: Text("点击跟读",
                                      style: TextStyle(
                                          color: const Color(0xFF061B1F),
                                          fontSize: Platform.isIOS ? 18 : 16,
                                          fontWeight: FontWeight.w800)),
                                )),
                          ),
                        ),
                        Visibility(
                          visible: state.voiceType != VoiceTypeEnum.cnTanslated,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              const SizedBox(
                                width: 20,
                              ),
                              GestureDetector(
                                onTap: () {
                                  FocusScope.of(context)
                                      .unfocus(); // 点击其他地方收起键盘

                                  context.read<VoiceCubit>().stop();
                                },
                                child: const Image(
                                  image: AssetImage("images/chat_close.png"),
                                  width: 40,
                                  height: 40,
                                ),
                              ),
                              const SizedBox(
                                width: 20,
                              ),
                              Expanded(
                                child: GifView.asset(
                                  "images/chat_loading.gif",
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: Platform.isIOS ? 36 : 30,
                                  repeat: ImageRepeat.repeat,
                                ),
                              ),
                              const SizedBox(
                                width: 20,
                              ),
                              GestureDetector(
                                onTap: () {
                                  FocusScope.of(context)
                                      .unfocus(); // 点击其他地方收起键盘

                                  context.read<VoiceCubit>().action(
                                      context.read<IeltsChatProvider>());
                                },
                                child: Image(
                                  image: state.voiceType == VoiceTypeEnum.ch
                                      ? const AssetImage(
                                          "images/chat_translate_big.png")
                                      : const AssetImage(
                                          "images/chat_send.png"),
                                  width: 40,
                                  height: 40,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: Platform.isIOS ? 70.w : 60.w,
                        )
                      ],
                    ),
                  )
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

// class Sky extends CustomPainter {
//   final double progress;
//   static double pi = 3.1415926;
//   final Paint _paint = Paint()
//     ..color = Colors.white
//     ..isAntiAlias = true
//     ..style = PaintingStyle.fill //绘画风格，默认为填充
//     ..strokeWidth = 2.0;

//   final Paint _paint2 = Paint()
//     ..color = Colors.cyan //画笔颜色
//     ..isAntiAlias = true
//     ..style = PaintingStyle.stroke //绘画风格，默认为填充
//     ..strokeWidth = 2.0;

//   Sky({super.repaint, required this.progress});
//   @override
//   void paint(Canvas canvas, Size size) {
//     Rect rect = Rect.fromLTRB(0, 0, size.width, 44);
//     canvas.drawArc(rect, pi, pi, false, _paint);
//     rect = Rect.fromLTRB(0, 0, size.width, 44 - 2);

//     canvas.drawArc(rect, pi, pi * progress, false, _paint2);
//   }

//   @override
//   bool shouldRepaint(Sky oldDelegate) => true;
//   @override
//   bool shouldRebuildSemantics(Sky oldDelegate) => false;
// }

class TalkAPPBar extends StatelessWidget implements PreferredSizeWidget {
  const TalkAPPBar(
      {super.key,
      required this.argument,
      required this.pop,
      required this.conversationId,
      required this.canCreateReport,
      required this.finishTask,
      required this.totalQuestionCount,
      required this.currentQuestionIndex});
  final IeltsTalkArgument argument;
  final num conversationId;
  final bool canCreateReport;
  final bool finishTask;
  final int totalQuestionCount;
  final int currentQuestionIndex;

  final Function(BuildContext) pop;

  @override
  Size get preferredSize => Size.fromHeight(260.w);

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: const BoxDecoration(
          color: Colors.transparent,
          image: DecorationImage(
              alignment: Alignment.topCenter,
              image: AssetImage("images/free_talk_top_bg.png"),
              fit: BoxFit.fill),
        ),
        child: SafeArea(
          child: Column(
            children: [
              Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  const SizedBox(
                    width: 17,
                  ),
                  GestureDetector(
                    onTap: () {
                      pop(context);
                    },
                    child: Image.asset(
                      "images/navigation_back.png",
                      width: 24,
                      height: 24,
                    ),
                  ),
                  const Spacer(),
                  Column(
                    children: [
                      Text(
                        argument.ieltsType.name,
                        style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w800,
                            color: Color(0xFF061B1F)),
                      ),
                      SizedBox(
                        width: 16.w,
                      ),
                      Center(
                          child: Text(
                        "${argument.partCategoryTitle} - ${argument.topicNameChinese}",
                        style: style_1_20,
                      ))
                    ],
                  ),
                  const Spacer(),
                  const SizedBox(
                    width: 41,
                  ),
                ],
              ),
              SizedBox(
                height: 16.w,
              ),
              Row(
                children: [
                  const SizedBox(
                    width: 17,
                  ),
                  for (var i = 0; i < totalQuestionCount; i++) ...[
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color: i >= currentQuestionIndex
                              ? const Color(0x1f41C0FF)
                              : const Color(0xFF41C0FF),
                        ),
                        height: 12.w,
                      ),
                    ),
                    const SizedBox(
                      width: 4,
                    )
                  ],
                  const SizedBox(
                    width: 13,
                  ),
                ],
              ),
              SizedBox(
                height: 32.w,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(
                    width: 17,
                  ),
                  Image.network(
                    LoginUtil.currentUserInfoModel()
                            ?.data
                            ?.userSettings
                            ?.iconUrl ??
                        "_",
                    fit: BoxFit.contain,
                    width: 40,
                    height: 40,
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Text(
                    LoginUtil.currentUserInfoModel()
                            ?.data
                            ?.userSettings
                            ?.characterName ??
                        "小西老师",
                    style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w800,
                        color: Color(0xFF061B1F)),
                  ),
                  const Spacer(),
                  Column(
                    children: [
                      GestureDetector(
                          onTap: () {
                            FocusScope.of(context).unfocus(); // 点击其他地方收起键盘

                            if (argument.ieltsType == IeltsTypeEnum.test) {
                              _showTopicDetail(context, argument.topicId);
                            } else {
                              _showMockTopicDetail(context, argument.topicId);
                            }
                          },
                          child: Visibility(
                              visible: true,
                              child: Image.asset(
                                finishTask
                                    ? "images/chat_task_success.png"
                                    : "images/chat_task.png",
                                width: 64.w,
                                height: 64.w,
                              ))),
                    ],
                  ),
                  SizedBox(
                    width: 16.w,
                  ),
                  const SizedBox(
                    width: 17,
                  ),
                ],
              ),
            ],
          ),
        ));
  }

  _showSpeechSetting(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      constraints: BoxConstraints(maxWidth: double.infinity, maxHeight: 558.w),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20), topRight: Radius.circular(20))),
      builder: (context) {
        return const SpeedSettingsDialog();
      },
    );
  }

  Future<dynamic> _showTopicDetail(BuildContext context, int topicId) {
    return showModalBottomSheet(
        backgroundColor: Colors.transparent,
        constraints:
            BoxConstraints(maxWidth: double.infinity, maxHeight: 1138.w),
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20))),
        context: context,
        builder: (context) {
          return Padding(
            padding: EdgeInsets.only(left: 24.w, right: 24.w, bottom: 50.w),
            child: Container(
                clipBehavior: Clip.antiAlias,
                decoration:
                    BoxDecoration(borderRadius: BorderRadius.circular(40.w)),
                // child: IeltsTestTransitionPop()),
                child:
                    IeltsTestTransitionPop(topicIds: argument.topicIds ?? [])),
          );
        });
  }

  Future<dynamic> _showMockTopicDetail(BuildContext context, int topicId) {
    return showModalBottomSheet(
      // backgroundColor: Colors.black.withOpacity(0.8),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      // barrierColor: Colors.black.withOpacity(0.8),
      barrierColor: Colors.transparent, // 遮罩色设置为透明
      elevation: 0,
      context: context,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(bottom: 0.w),
          child: Container(
            clipBehavior: Clip.antiAlias,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(40.w),
                topRight: Radius.circular(40.w),
              ),
            ),
            child: Stack(
              children: [
                IeltsSpeakingTopicQuestionsDialogWidget(
                  topicId: topicId,
                  isChatPage: true,
                ),
                Positioned(
                  top: 40.w,
                  right: 30.w,
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: const Icon(
                      Icons.close,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
