import 'dart:math';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_report_model/ielts_speaking_report_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_report_model/record.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielts_talk_page.dart';
import 'package:flutter_app_kouyu/modules/ielts/provider/ielts_speaking_report_provider.dart';
import 'package:flutter_app_kouyu/modules/chat/view/follow_read_widget.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/high_frequency_vocabulary_page.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_app_kouyu/widgets/selected_text.dart';
import 'package:flutter_app_kouyu/widgets/share-widget/share_image_widget.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:screenshot/screenshot.dart';

class IeltsSpeakingReportPage extends StatelessWidget {
  final num practiceId;
  final IeltsSpeakingReportDetailType type;
  const IeltsSpeakingReportPage(
      {super.key, required this.practiceId, required this.type});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<IeltsSpeakingReportNotifier>(
      create: (_) =>
          IeltsSpeakingReportNotifier(practiceId: practiceId, type: type),
      child: Consumer<IeltsSpeakingReportNotifier>(
          builder: (context, model, child) {
        return Scaffold(
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            leadingWidth: 300.w,
            elevation: 0,
            backgroundColor: Colors.transparent,
            leading: CustomAppbar.leftWidget(context, text: "雅思报告"),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ShareImageWidget(
                  getImageFunc: model.getShareImageUrls,
                ),
              ],
            ),
          ),
          backgroundColor: const Color(0xFFF3FBFD),
          body: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: ListView(
              padding: EdgeInsets.only(top: 0.w),
              children: [
                Screenshot(
                  controller: model.screenshotController,
                  child: Container(
                    padding: EdgeInsets.only(
                        left: 40.w, right: 40.w, bottom: 60.w, top: 20.w),
                    decoration: const BoxDecoration(
                      color: Color(0xFFF3FBFD),
                      image: DecorationImage(
                        alignment: Alignment.topCenter,
                        image: AssetImage("images/report_top_score.png"),
                        fit: BoxFit.fitWidth,
                      ),
                    ),
                    width: double.infinity,
                    child: Stack(
                      children: [
                        ListView(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          children: [
                            ..._topWidget(model.model),
                            SizedBox(height: 32.w),
                            ..._scroreWidget(context, model.model),
                            SizedBox(height: 32.w),
                            _partTypeWidget_new(model),
                            SizedBox(height: 16.w),
                            _question_list_widget(model),
                            Container(
                              padding:
                                  EdgeInsets.fromLTRB(0.w, 24.w, 0.w, 24.w),
                              child:
                                  _reviewWidget(context, model.currentRecord),
                            ),
                            SizedBox(height: 32.w),
                          ],
                        ),
                        Positioned(
                          top: 145.w,
                          left: 420.w,
                          child: SizedBox(
                            height: 114.w,
                            width: 220.w,
                            child: Center(
                              child: Text(
                                model.model?.data?.totalScore ?? "_",
                                style: TextStyle(
                                  color: const Color(0xFFFCEEA7),
                                  fontSize: 90.w,
                                  fontWeight: FontWeight.w800,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
          bottomNavigationBar: Container(
            color: Colors.white,
            padding: EdgeInsets.only(left: 40.w, right: 40.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: 144.w,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Container(
                          height: 96.w,
                          width: 325.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.black),
                          ),
                          child: TextButton(
                              onPressed: () {
                                Navigator.push(
                                  context,
                                  IeltsTalkPage.route(
                                    argument: IeltsTalkArgument(
                                        partCategoryTitle: model
                                            .model!.data!.topicTitle!.first!
                                            .split(":")[0]
                                            .trim(),
                                        ieltsType: IeltsTypeEnum.from(
                                            model.model!.data!.practiceType),
                                        topicNameChinese: model.model?.data
                                                ?.topicChinese?[0] ??
                                            "",
                                        topicId: model.model?.data
                                                ?.topicIdArray?[0] ??
                                            0,
                                        topicCode: model.model?.data
                                                ?.topicCodeArray?[0] ??
                                            "",
                                        topicIds:
                                            model.model?.data?.topicIdArray),
                                  ),
                                );
                              },
                              style: const ButtonStyle(
                                textStyle: MaterialStatePropertyAll(TextStyle(
                                    fontSize: 20, fontWeight: FontWeight.w700)),
                              ),
                              child: Center(
                                child: Text("重新练习", style: style_1_28),
                              )),
                        ),
                      ),
                      SizedBox(width: 20.w),
                      Expanded(
                        child: Container(
                          height: 96.w,
                          width: 325.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: const Color(0xFF2693FF),
                          ),
                          child: TextButton(
                              onPressed: () {
                                //返回首页
                                Navigator.of(context).pushNamedAndRemoveUntil(
                                    '/', (route) => false);
                              },
                              style: const ButtonStyle(
                                textStyle: MaterialStatePropertyAll(TextStyle(
                                    fontSize: 20, fontWeight: FontWeight.w700)),
                              ),
                              child: Center(
                                child: Text(
                                  "返回首页",
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w800),
                                ),
                              )),
                        ),
                      )
                    ],
                  ),
                ),
                SizedBox(
                  height: 68.w,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // 可以在此处添加其他内容或保持空白以符合UI需求
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _question_list_widget(IeltsSpeakingReportNotifier model) {
    int size = 0;
    if (model.model != null) {
      if (model.selectPartTypeTab == "Part1" ||
          model.selectPartTypeTab == null) {
        size = model.model!.data!.records!.first!.length;
      } else {
        size = model.model!.data!.records![1].length;
      }
    }

    return Align(
      alignment: Alignment.centerLeft, // 按钮组左对齐
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal, // 横向滑动
        child: Row(
          children: List<Widget>.generate(size, (index) {
            bool isSelected = model.selectedIndex == index; // 判断按钮是否选中
            return Padding(
              padding: EdgeInsets.only(right: 16.w), // 每个按钮右边距为16.w
              child: GestureDetector(
                onTap: () {
                  model.selectedIndex = index;
                },
                child: Container(
                  padding: EdgeInsets.fromLTRB(20.w, 12.w, 20.w, 12.w),
                  decoration: BoxDecoration(
                    borderRadius: isSelected
                        ? BorderRadius.circular(16.w)
                        : BorderRadius.circular(10.w),
                    gradient: isSelected ? ColorUtil.blueToGreen : null,
                    color: isSelected ? null : const Color(0xFFFFFFFF),
                  ),
                  child: Text(
                    '问题${index + 1}' ?? "_",
                    style: style_1_24,
                  ),
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget _partTypeWidget(IeltsSpeakingReportNotifier model) {
    return Container(
      color: const Color(0xFFF3FBFD),
      padding: EdgeInsets.fromLTRB(32.w, 40.w, 32.w, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          Row(
            children: partType(model),
          ),
        ],
      ),
    );
  }

  Widget _partTypeWidget_new(IeltsSpeakingReportNotifier model) {
    return Container(
      color: const Color(0xFFF3FBFD),
      padding: EdgeInsets.only(left: 0.w, right: 40.w, top: 40.w),
      child: SizedBox(
        height: 56.w,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Align(
              alignment: Alignment.centerLeft, // 确保文本左对齐
              child: Text(
                '复盘提升',
                style: style_1_36,
              ),
            ),
            const Spacer(),
            Visibility(
              visible: model.model?.data?.topicTitle != null &&
                  model.model?.data?.topicTitle?.length == 2,
              child: Expanded(
                child: Container(
                  width: 127.2 * 2,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.w),
                    color: Colors.white,
                  ),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () {},
                        child: GestureDetector(
                          onTap: () {
                            model.selectPartTypeTab = "Part1";
                          },
                          child: Container(
                            width: 92.w,
                            height: 56.w,
                            child: Center(
                              child: Text(
                                'Part1',
                                style: TextStyle(
                                    fontSize: 24.w,
                                    fontWeight: FontWeight.w400,
                                    color: model.selectPartTypeTab == "Part1"
                                        ? const Color(0xff061B1F)
                                        : const Color(0xffA7D3DB)),
                              ),
                            ),
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          model.selectPartTypeTab = "Part2&3";
                        },
                        child: Container(
                          width: 122.w,
                          height: 56.w,
                          child: Center(
                            child: Text(
                              'Part2&3',
                              style: TextStyle(
                                  fontSize: 24.w,
                                  fontWeight: FontWeight.w400,
                                  color: model.selectPartTypeTab == "Part2&3"
                                      ? const Color(0xff061B1F)
                                      : const Color(0xffA7D3DB)),
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  List<Widget> partType(IeltsSpeakingReportNotifier model) {
    var list = <Widget>[];
    // list 添加一个Text 元素：复盘提升
    list.add(Text("复盘提升", style: style_1_36));
    list.add(const Spacer());
    //如果有两个主题就添加两个按钮
    if (model.model?.data?.topicTitle != null &&
        model.model?.data?.topicTitle?.length == 2) {
      list.add(GestureDetector(
        onTap: () {
          model.selectPartTypeTab = "Part1";
        },
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Text(
              "Part1",
              style:
                  model.selectPartTypeTab == "Part1" ? style_1_36 : style_2_36,
            )
          ],
        ),
      ));
      list.add(SizedBox(
        width: 40.w,
      ));
      list.add(GestureDetector(
        onTap: () {
          model.selectPartTypeTab = "Part2&3";
        },
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Text(
              "Part2&3",
              style: model.selectPartTypeTab == "Part2&3"
                  ? style_1_36
                  : style_2_36,
            )
          ],
        ),
      ));
      list.add(SizedBox(
        width: 40.w,
      ));
    }
    return list;
  }

  /**
   * 构建雅思报告中的问题索引按钮
   */
  Widget _question_index_button_list(BuildContext context, int? size) {
    //构建一个按钮组，个数为size，在一个Row内，每个按钮有一个背景颜色，按钮中有一个Text，文案分别是：问题1、问题2 。。。
    return Align(
      alignment: Alignment.centerLeft, // 按钮组左对齐
      child: Wrap(
        spacing: 1.0, // 按钮之间的水平间距
        runSpacing: 1.0,
        children: List.generate(size ?? 0, (index) {
          return ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue, // 每个按钮的背景颜色
              padding: const EdgeInsets.all(1),
            ),
            onPressed: () {
              print('Button ${index + 1} pressed');
            },
            child: Text(
              '问题${index + 1}', // 按钮上的文本
              style: TextStyle(color: Colors.white),
            ),
          );
        }),
      ),
    );
  }

  Widget _voiceWidget(String type, String url) {
    return GestureDetector(
      onTap: () {
        CommonUtils.playVideo(url);
      },
      child: Container(
        height: 48.w,
        decoration: BoxDecoration(
            border: Border.all(color: ColorUtil.separated),
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.w)),
        child: Row(
          children: [
            SizedBox(
              width: 24.w,
            ),
            Text(
              type,
              style: style_1_24,
            ),
            PlayButtonWidget2(
              audioUrl: url,
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _scroreWidget(
      BuildContext context, IeltsSpeakingReportModel? model) {
    return [
      Row(
        children: [
          Text(
            "综合评分",
            style: style_1_36,
          ),
          SizedBox(
            width: 16.w,
          ),
          Text(
            model?.data?.encouragement ?? "_",
            style: style_2_24,
          ),
          const Spacer(),
          TextButton.icon(
              onPressed: () {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  builder: (BuildContext context) {
                    return FractionallySizedBox(
                      widthFactor: 1.0,
                      child: Container(
                        height: MediaQuery.of(context).size.height * 0.9,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius:
                              BorderRadius.vertical(top: Radius.circular(16.0)),
                        ),
                        padding: EdgeInsets.all(16.0),
                        child: Stack(
                          children: [
                            SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // 标题行
                                  Text(
                                    "评分明细",
                                    style: TextStyle(
                                      fontSize: 36.w,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                  ),
                                  SizedBox(height: 16.w),

                                  // 标题1和内容
                                  Text(
                                    "流利性和连贯性" +
                                        " | " +
                                        model!.data!.fluencyScore! +
                                        "分",
                                    style: TextStyle(
                                      fontSize: 32.w,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                  ),
                                  SizedBox(height: 8.w),
                                  Text(
                                    model!.data!.explanationOfScore!
                                        .fluencyScore!,
                                    style: TextStyle(
                                      fontSize: 28.w,
                                      color: Color(0xFF646E70),
                                    ),
                                  ),
                                  SizedBox(height: 16.h),

                                  // 标题2和内容
                                  Text(
                                    "词汇多样性" +
                                        " | " +
                                        model!.data!.vocabularyScore! +
                                        "分",
                                    style: TextStyle(
                                      fontSize: 32.w,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                  ),
                                  SizedBox(height: 8.h),
                                  Text(
                                    model!.data!.explanationOfScore!
                                        .vocabularyScore!,
                                    style: TextStyle(
                                      fontSize: 28.w,
                                      color: Color(0xFF646E70),
                                    ),
                                  ),
                                  SizedBox(height: 16),

                                  // 标题3和内容
                                  Text(
                                    "发音" +
                                        " | " +
                                        model!.data!.pronunciationScore! +
                                        "分",
                                    style: TextStyle(
                                      fontSize: 32.w,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                  ),
                                  SizedBox(height: 8.h),
                                  Text(
                                    model!.data!.explanationOfScore!
                                        .pronunciationScore!,
                                    style: TextStyle(
                                      fontSize: 28.w,
                                      color: Color(0xFF646E70),
                                    ),
                                  ),
                                  SizedBox(height: 16.h),

                                  // 标题4和内容
                                  Text(
                                    "语法多样性及准确性" +
                                        " | " +
                                        model!.data!.grammarScore! +
                                        "分",
                                    style: TextStyle(
                                      fontSize: 32.w,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                  ),
                                  SizedBox(height: 8.h),
                                  Text(
                                    model!.data!.explanationOfScore!
                                        .grammarScore!,
                                    style: TextStyle(
                                      fontSize: 28.w,
                                      color: Color(0xFF646E70),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // 右上角关闭按钮
                            Positioned(
                              top: 8.w,
                              right: 8.w,
                              child: GestureDetector(
                                onTap: () {
                                  Navigator.of(context).pop();
                                },
                                child: Icon(
                                  Icons.close,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
              icon: Text(
                "查看详情",
                style: style_2_24,
              ),
              label: Image.asset(
                "images/arrow_right_black.png",
                width: 12.w,
                height: 20.w,
              ))
        ],
      ),
      Container(
        margin: EdgeInsets.only(top: 24.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24.w), color: Colors.white),
        child: Column(
          children: [
            _radarWidget(model),
            Padding(
                padding: EdgeInsets.only(left: 25.w, right: 25.w),
                child: _lineWidget()),
            Padding(
                padding: EdgeInsets.fromLTRB(32.w, 0.w, 0.w, 0.w),
                child: Row(
                  children: [
                    Text(
                      "核心词汇 ",
                      style: style_1_28_400,
                    ),
                    Text(
                      "${model?.data?.coreVocabulariesCount ?? "_"}个",
                      style: style_1_32,
                    ),
                    const Spacer(),
                    TextButton.icon(
                        onPressed: () {
                          Navigator.push(context,
                              HighFrequencyVocabularyPage.route("$practiceId"));
                        },
                        icon: Text(
                          "查看详情",
                          style: style_2_24,
                        ),
                        label: Image.asset(
                          "images/arrow_right_black.png",
                          width: 12.w,
                          height: 20.w,
                        ))
                  ],
                )),
            Padding(
                padding: EdgeInsets.only(left: 25.w, right: 25.w),
                child: _lineWidget())
            // Padding(
            //     padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
            //     child: Row(
            //       children: [
            //         Text(
            //           "对话轮数 ",
            //           style: style_1_28_400,
            //         ),
            //         Text(
            //           "${model?.data?.conversationRounds ?? "_"}轮",
            //           style: style_1_32,
            //         ),
            //         const Spacer(),
            //         Text(
            //           "${model?.data?.excellentRounds ?? "_"}",
            //           style: style_green_40,
            //         ),
            //         Text(
            //           " 表现优秀",
            //           style: style_2_24,
            //         ),
            //         Container(
            //           margin: EdgeInsets.only(left: 16.w, right: 16.w),
            //           width: 1.w,
            //           height: 20.w,
            //           color: ColorUtil.separated,
            //         ),
            //         Text(
            //           "${model?.data?.improvementRounds ?? "_"}",
            //           style: style_orange_40,
            //         ),
            //         Text(
            //           " 有待提升",
            //           style: style_2_24,
            //         )
            //       ],
            //     )

            //     )
          ],
        ),
      )
    ];
  }

  Container _lineWidget() {
    return Container(
      height: 1.w,
      width: double.infinity,
      color: ColorUtil.separated,
    );
  }

  Widget _radarWidget(IeltsSpeakingReportModel? model) {
    String? fluencyScore = model?.data?.fluencyScore ?? "_";
    String? grammarScore = model?.data?.grammarScore ?? "_";
    String? pronunciationScore = model?.data?.pronunciationScore ?? "_";
    String? vocabularyScore = model?.data?.vocabularyScore ?? "_";
    return SizedBox(
      height: 592.w,
      width: double.infinity,
      child: Column(
        children: [
          const Spacer(),
          _radarScoreWidget("流利性和连贯性", fluencyScore),
          Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              const Spacer(),
              Column(
                children: [
                  _radarScoreWidget("发音", pronunciationScore),
                  SizedBox(
                    height: 100.w,
                  )
                ],
              ),
              Image.network(
                model?.data?.radarChartUrl ?? "",
                width: 400.w,
                fit: BoxFit.fitWidth,
              ),
              Column(
                children: [
                  _radarScoreWidget("词汇多样性", vocabularyScore),
                  SizedBox(
                    height: 100.w,
                  )
                ],
              ),
              const Spacer(),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _radarScoreWidget("语法多样性及准确性", grammarScore),
            ],
          ),
          const Spacer(),
        ],
      ),
    );
  }

  RichText _radarScoreWidget(String name, String score) {
    return RichText(
        textAlign: TextAlign.center,
        text: TextSpan(children: [
          TextSpan(
            text: name,
            style: style_2_24,
          ),
          WidgetSpan(
              child: SizedBox(
            width: 10.w,
          )),
          TextSpan(
            text: "\n$score",
            style: TextStyle(
                fontSize: 32.sp,
                fontWeight: FontWeight.w700,
                color: [
                  const Color(0xFF00B578),
                  const Color(0xFF061B1F),
                  const Color(0xFFFF8F1F),
                ][0]),
          ),
        ]));
  }

  List<Widget> _topWidget(IeltsSpeakingReportModel? model) {
    return [
      Text(
        "Congrat!",
        style: TextStyle(
            fontWeight: FontWeight.w900, fontSize: 64.sp, color: color_1),
      ),
      model?.data?.topicTitle != null
          ? Text(
              "已完成以下话题",
              style: style_2_24,
            )
          : SizedBox(
              height: 32.w,
            ),
      SizedBox(
        height: 32.w,
      ),
      model?.data?.topicTitle != null && model?.data?.topicTitle?.length == 1
          ? _chatOneTopicTargetWidget(model?.data?.topicTitle?.first)
          : SizedBox(
              height: 0.w,
            ),
      model?.data?.topicTitle != null && model?.data?.topicTitle?.length == 2
          ? _chatTargetWidget(
              model?.data?.topicTitle?.first, model?.data?.topicTitle?[1])
          : SizedBox(
              height: 0.w,
            ),
      // if(model?.data?.topicTitle?.length == 2)
      //       _chatTargetWidget(model?.data?.topicTitle?[1]),
      //       SizedBox(
      //         height: 0.w,
      //       )
    ];
  }

  Container _chatOneTopicTargetWidget(String? topicTitle) {
    return Container(
      padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.w), color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Visibility(
                  visible: true,
                  child: Image.asset("images/checkbox-circle-fill_green.png",
                      width: 36.w, height: 36.w)),
              Visibility(
                visible: true,
                child: SizedBox(
                  width: 8.w,
                ),
              ),
              Expanded(
                  child: Text(
                //获取话题标题,topicTitle是一个字符串数组,获取数组第一个元素
                topicTitle ?? "_",
                style: style_1_28,
              ))
            ],
          )
        ],
      ),
    );
  }

  Container _chatTargetWidget(String? topicTitle, String? part2TopicTitle) {
    return Container(
      padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.w), color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Visibility(
                  visible: true,
                  child: Image.asset("images/checkbox-circle-fill_green.png",
                      width: 36.w, height: 36.w)),
              Visibility(
                visible: true,
                child: SizedBox(
                  width: 8.w,
                ),
              ),
              Expanded(
                  child: Text(
                //获取话题标题,topicTitle是一个字符串数组,获取数组第一个元素
                topicTitle ?? "_",
                style: style_1_28,
              ))
            ],
          ),

          if (part2TopicTitle != null) SizedBox(height: 16.w), // 设置 Row 之间的间距
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Visibility(
                  visible: true,
                  child: Image.asset("images/checkbox-circle-fill_green.png",
                      width: 36.w, height: 36.w)),
              Visibility(
                visible: true,
                child: SizedBox(
                  width: 8.w,
                ),
              ),
              Expanded(
                  child: Text(
                //获取话题标题,topicTitle是一个字符串数组,获取数组第一个元素
                part2TopicTitle ?? "_",
                style: style_1_28,
              ))
            ],
          )
        ],
      ),
    );
  }

  Widget _reviewWidget(BuildContext context, Record? improvement) {
    return Column(
      children: [
        _originQuestionWidget(context, improvement),
        SizedBox(
          height: 20.w,
        ),
        _originTextWidget(context, improvement),
        SizedBox(
          height: 20.w,
        ),
        Row(
          children: [
            _voiceWidget("美音",
                improvement?.pronunciationEvaluationResults?.usAudioUrl ?? ""),
            SizedBox(
              width: 16.w,
            ),
            _voiceWidget("英音",
                improvement?.pronunciationEvaluationResults?.ukAudioUrl ?? ""),
            const Spacer(),
            GestureDetector(
              onTap: () {
                _gotoFollowRead(context,
                    improvement?.pronunciationEvaluationResults?.text ?? "");
              },
              child: Container(
                height: 56.w,
                width: 143.w,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.w),
                    color: const Color(0xFF2693FF)),
                child: Center(
                  child: Text(
                    "再读一遍",
                    style: style_1_24.copyWith(color: Colors.white),
                  ),
                ),
              ),
            ),
          ],
        ),
        SizedBox(
          height: 20.w,
        ),
        _voiceSoeWidget(improvement),
        Padding(
          padding: EdgeInsets.only(left: 24.w, right: 24.w),
          child: _lineWidget(),
        ),
        _vocabularyWidget(context, improvement),
        Padding(
          padding: EdgeInsets.only(left: 24.w, right: 24.w),
          child: _lineWidget(),
        ),
        _analysisWidget(context, improvement),
        Padding(
          padding: EdgeInsets.only(left: 24.w, right: 24.w),
          child: _lineWidget(),
        ),
        _suggessWidget(context, improvement),
      ],
    );
  }

  Widget _feedbackWidget1(BuildContext context, int practiceId) {
    return Container(
        padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
        width: double.infinity,
        color: Colors.white,
        child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(
            "我要反馈",
            style: style_1_28,
          ),
          const Spacer(),
          TextButton.icon(
              onPressed: () {},
              icon: Text(
                "点击反馈",
                style: style_2_24,
              ),
              label: Image.asset(
                "images/arrow_right_black.png",
                width: 12.w,
                height: 20.w,
              ))
        ]));
  }

  Widget _feedbackWidget(
      BuildContext context, int practiceId, IeltsSpeakingReportModel? model) {
    // 定义选择状态
    int selectedRating = 0; // 1: 几乎没有, 2: 有一点, 3: 很有帮助
    List<String> selectedReasons = [];
    TextEditingController feedbackController = TextEditingController();

    // 显示 Toast 提示
    void showToast(String message) {
      Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        backgroundColor: Colors.grey,
        textColor: Colors.white,
        fontSize: 16.0,
      );
    }

    return GestureDetector(
      onTap: () {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (BuildContext context) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16.0), // 底部留出 16 的距离
              child: FractionallySizedBox(
                widthFactor: 1.0,
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.7,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(16.0)),
                  ),
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        "学习反馈",
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF061B1F),
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        "这个场景对你是否有帮助",
                        style: TextStyle(
                          color: Color(0xFF061B1F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      // 单选按钮组
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildSelectableButton("几乎没有", 1, selectedRating == 1,
                              () {
                            selectedRating = 1;
                          }),
                          _buildSelectableButton("有一点", 2, selectedRating == 2,
                              () {
                            selectedRating = 2;
                          }),
                          _buildSelectableButton("很有帮助", 3, selectedRating == 3,
                              () {
                            selectedRating = 3;
                          }),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        "使用体验不好的原因（可多选）",
                        style: TextStyle(
                          color: Color(0xFF061B1F),
                        ),
                      ),
                      // 多选按钮组
                      Wrap(
                        spacing: 8.0,
                        children: [
                          _buildMultiSelectableButton("语速很慢", selectedReasons),
                          _buildMultiSelectableButton("回答不准确", selectedReasons),
                          _buildMultiSelectableButton("使用卡顿", selectedReasons),
                          _buildMultiSelectableButton("操作复杂", selectedReasons),
                          _buildMultiSelectableButton("界面不好看", selectedReasons),
                          _buildMultiSelectableButton("功能太少", selectedReasons),
                          _buildMultiSelectableButton("加载慢", selectedReasons),
                          _buildMultiSelectableButton("试用时间少", selectedReasons),
                          _buildMultiSelectableButton("会员价格贵", selectedReasons),
                        ],
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: feedbackController,
                        maxLines: 5,
                        decoration: InputDecoration(
                          hintText: "请输入您想说的话",
                          filled: true,
                          fillColor: Color(0xFFF0F8FA),
                          hintStyle: TextStyle(color: Color(0xFFA7D3DB)),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(24.0),
                            borderSide: BorderSide.none,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      // 仅更新提交反馈按钮的代码
                      Container(
                        width: double.infinity, // 让按钮宽度填满
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFF00BCD4),
                              Color(0xFF41C0FF)
                            ], // 蓝绿渐变
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                          borderRadius: BorderRadius.circular(24.0),
                        ),
                        child: ElevatedButton(
                          onPressed: () async {
                            // 调用接口逻辑
                            bool success = await Api.updateUserRate({
                              "conversation_id": practiceId,
                              "rate": selectedRating,
                              "bad_experience_reason": selectedReasons,
                              "user_feedback": feedbackController.text,
                            });
                            if (success) {
                              Navigator.of(context).pop();
                              showToast("感谢您的反馈");
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(vertical: 16.0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(24.0),
                            ),
                            elevation: 0, // 设置为 0 以避免投影影响渐变效果
                            backgroundColor:
                                Colors.transparent, // 使按钮背景透明，以显示渐变效果
                          ),
                          child: Center(
                            child: Text(
                              "提交反馈",
                              style: TextStyle(
                                color: Color(0xFF061B1F),
                                fontWeight: FontWeight.bold,
                                fontSize: 28.w,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.w),
          color: Colors.white,
        ),
        child: Row(
          children: [
            Text(
              "我要反馈 ",
              style: TextStyle(
                color: color_1,
                fontSize: 24.w,
                fontWeight: FontWeight.w400,
              ),
            ),
            const Spacer(),
            Image.asset(
              "images/arrow_right_black.png",
              width: 12.w,
              height: 20.w,
            ),
          ],
        ),
      ),
    );
  }

// Helper function for creating a selectable button (single select)
  Widget _buildSelectableButton(
      String text, int rate, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFE0F7FA) : const Color(0xFFF0F8FA),
          borderRadius: BorderRadius.circular(24.0),
          border: Border.all(
            color: isSelected ? const Color(0xFF41C0FF) : Colors.transparent,
          ),
        ),
        child: Text(
          text,
          style: TextStyle(
            color:
                isSelected ? const Color(0xFF41C0FF) : const Color(0xFF646E70),
          ),
        ),
      ),
    );
  }

// Helper function for creating a multi-selectable button
  Widget _buildMultiSelectableButton(
      String text, List<String> selectedReasons) {
    bool isSelected = selectedReasons.contains(text);
    return GestureDetector(
      onTap: () {
        if (isSelected) {
          selectedReasons.remove(text);
        } else {
          selectedReasons.add(text);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFE0F7FA) : const Color(0xFFF0F8FA),
          borderRadius: BorderRadius.circular(24.0),
          border: Border.all(
            color: isSelected ? const Color(0xFF41C0FF) : Colors.transparent,
          ),
        ),
        child: Text(
          text,
          style: TextStyle(
            color:
                isSelected ? const Color(0xFF41C0FF) : const Color(0xFF646E70),
          ),
        ),
      ),
    );
  }

  Widget _voiceSoeWidget(Record? improvement) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24.w),
                  topRight: Radius.circular(24.w)),
              color: Colors.white),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "发音",
                style: style_1_28,
              ),
              SizedBox(
                width: 147.w,
                height: 95.w,
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Image.asset(
                        "images/score_bg.png",
                        width: 147.w,
                        height: 40.w,
                      ),
                    ),
                    Align(
                        alignment: Alignment.topLeft,
                        child: Text(
                          "${improvement?.pronunciationEvaluationResults?.suggestedScore ?? "_"}",
                          style: style_green_64,
                        )),
                  ],
                ),
              ),
              Text(
                "表达流利顺畅，和你交流是件愉悦的事",
                style: style_2_28,
              ),
              SizedBox(
                height: 24.w,
              ),
              Row(
                children: [
                  Column(
                    children: [
                      _progressWidget(
                          '准确度',
                          improvement?.pronunciationEvaluationResults
                                  ?.pronAccuracy ??
                              0),
                      SizedBox(
                        height: 32.w,
                      ),
                      _progressWidget(
                          '流畅度',
                          improvement?.pronunciationEvaluationResults
                                  ?.pronFluency ??
                              0),
                      SizedBox(
                        height: 32.w,
                      ),
                      _progressWidget(
                          '完整度',
                          improvement?.pronunciationEvaluationResults
                                  ?.pronCompletion ??
                              0),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        width: 346.w,
                        height: 193.w,
                        decoration: const BoxDecoration(
                            image: DecorationImage(
                                image: AssetImage("images/voice_speech_bg.png"),
                                fit: BoxFit.fill)),
                        child: Stack(
                          alignment: Alignment.topCenter,
                          children: [
                            Positioned.fill(
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 32.w,
                                  ),
                                  Transform.rotate(
                                    angle: ((improvement
                                                    ?.pronunciationEvaluationResults
                                                    ?.speed ??
                                                0) -
                                            90) /
                                        180 *
                                        pi,
                                    alignment: Alignment.bottomCenter,
                                    child: Image.asset(
                                      "images/voice_speech_arrow.png",
                                      width: 12.w,
                                      height: 116.w,
                                      fit: BoxFit.fill,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Positioned(
                                top: 60.w,
                                child: Center(
                                  child: Text(
                                    "${improvement?.pronunciationEvaluationResults?.speed ?? "_"}",
                                    style: style_1_48,
                                  ),
                                ))
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 10.w,
                      ),
                      RichText(
                          textAlign: TextAlign.justify,
                          text: TextSpan(children: [
                            TextSpan(
                              text: improvement?.pronunciationEvaluationResults
                                      ?.speedComment ??
                                  "_",
                              style: style_bue_24,
                            ),
                            WidgetSpan(
                                child: SizedBox(
                              width: 10.w,
                            )),
                            // WidgetSpan(
                            //     alignment: PlaceholderAlignment.middle,
                            //     child: Image.asset(
                            //       "images/blue_help.png",
                            //       width: 20.w,
                            //       height: 20.w,
                            //       fit: BoxFit.fill,
                            //     )),
                          ])),
                    ],
                  )
                ],
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget _vocabularyWidget(BuildContext context, Record? improvement) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Container(
          padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
          width: double.infinity,
          color: Colors.white,
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(
              "词汇",
              style: style_1_28,
            ),
            SizedBox(
              width: 147.w,
              height: 95.w,
              child: Stack(
                children: [
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Image.asset(
                      "images/score_bg.png",
                      width: 147.w,
                      height: 40.w,
                    ),
                  ),
                  Align(
                      alignment: Alignment.topLeft,
                      child: Text(
                        improvement?.vocabularyEvaluationResult?.score ?? "_",
                        style: style_green_64,
                      )),
                ],
              ),
            ),
            // 当 correction 不为空时按照原有逻辑显示
            Text(
              improvement?.vocabularyEvaluationResult?.explanation ?? "_",
              style: style_2_28,
            ),
          ])),
      Padding(
        padding: EdgeInsets.only(left: 24.w, right: 24.w),
        child: _lineWidget(),
      ),
    ]);
  }

  Widget _analysisWidget(BuildContext context, Record? improvement) {
    IeltsSpeakingReportNotifier notifier =
        context.read<IeltsSpeakingReportNotifier>();

    // 检查 correction 是否为空或 null
    bool isGrammarCorrect =
        improvement?.grammarDetectionResults?.correction == null ||
            (improvement?.grammarDetectionResults?.correction?.isEmpty ?? true);

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Container(
          padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
          width: double.infinity,
          color: Colors.white,
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(
              "语法",
              style: style_1_28,
            ),
            if (isGrammarCorrect)
              // 当 correction 为空或 null 时显示 "语法完全正确"
              Container(
                child: Text(
                  "语法完全正确",
                  style: style_2_28,
                ),
              )
            else
              // 当 correction 不为空时按照原有逻辑显示
              Visibility(
                visible:
                    improvement?.grammarDetectionResults?.correction != null &&
                        (improvement?.grammarDetectionResults?.correction!
                                .isNotEmpty ??
                            false),
                child: Container(
                    padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
                    margin: EdgeInsets.fromLTRB(0.w, 24.w, 0.w, 24.w),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24.w),
                        color: btnColor),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "建议使用",
                          style: style_2_24,
                        ),
                        SizedBox(
                          height: 16.w,
                        ),
                        SelectableTextUtil.editableText(
                          context,
                          text: improvement
                                  ?.grammarDetectionResults?.correction ??
                              "_",
                          style: style_1_32_400,
                        ),
                        ..._translateWidgets(context,
                            improvement?.grammarDetectionResults?.correction),
                        _lineWidget(),
                        SizedBox(
                          height: 24.w,
                        ),
                        Row(
                          children: [
                            PlayButtonWidget(
                              style: PlayButtonStyle.white,
                              text: improvement
                                  ?.grammarDetectionResults?.correction,
                            ),
                            SizedBox(
                              width: 20.w,
                            ),
                            GestureDetector(
                                onTap: () {
                                  notifier.translate(improvement
                                      ?.grammarDetectionResults?.correction);
                                },
                                child: Image(
                                  image: const AssetImage(
                                      "images/chat_translate_white.png"),
                                  width: 48.w,
                                  height: 48.w,
                                )),
                            SizedBox(
                              width: 20.w,
                            ),
                            GestureDetector(
                                onTap: () {
                                  Clipboard.setData(ClipboardData(
                                      text: improvement?.grammarDetectionResults
                                              ?.correction ??
                                          ""));
                                  EasyLoading.showToast("已复制");
                                },
                                child: Image(
                                  image: const AssetImage(
                                      "images/chat_copy_white.png"),
                                  width: 48.w,
                                  height: 48.w,
                                )),
                            SizedBox(
                              width: 20.w,
                            ),
                            _collectWidget(
                                context,
                                improvement
                                        ?.grammarDetectionResults?.correction ??
                                    "",
                                1),
                            SizedBox(
                              width: 20.w,
                            ),
                            const Spacer(),
                            GestureDetector(
                                onTap: () {
                                  _gotoFollowRead(
                                      context,
                                      improvement?.grammarDetectionResults
                                              ?.correction ??
                                          "");
                                },
                                child: Image.asset(
                                  "images/fllow_btn2.png",
                                  height: 28,
                                ))
                          ],
                        ),
                      ],
                    )),
              ),
            Text(
              improvement?.grammarDetectionResults?.explanation ?? "_",
              style: style_2_28,
            ),
          ])),
      Padding(
        padding: EdgeInsets.only(left: 24.w, right: 24.w),
        child: _lineWidget(),
      ),
    ]);
  }

  void _gotoFollowRead(BuildContext context, String text) {
    showModalBottomSheet(
        isScrollControlled: true,
        useSafeArea: true,
        constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height -
                MediaQuery.of(context).padding.top),
        backgroundColor: Colors.transparent,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20))),
        context: context,
        builder: (context) {
          return FollowReadWidget(
            text: text,
            conversationId: practiceId,
          );
        });
  }

  List<Widget> _translateWidgets(BuildContext context, String? text) {
    IeltsSpeakingReportNotifier notifier =
        context.read<IeltsSpeakingReportNotifier>();

    if (notifier.translateMap[text] == null) {
      return [
        SizedBox(
          height: 24.w,
        )
      ];
    }
    return [
      SizedBox(
        height: 24.w,
      ),
      Text(
        notifier.translateMap[text]!,
        style: style_2_24,
      ),
      SizedBox(
        height: 24.w,
      ),
    ];
  }

  Widget _suggessWidget(BuildContext context, Record? improvement) {
    IeltsSpeakingReportNotifier notifier =
        context.read<IeltsSpeakingReportNotifier>();
    if (improvement?.answer == null ||
        (improvement?.answer != null && improvement!.answer!.isEmpty)) {
      return Container();
    }

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Container(
          padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(24.w),
              bottomRight: Radius.circular(24.w),
            ),
            color: Colors.white,
          ),
          //color: Colors.white,
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(
              "范文",
              style: style_1_28,
            ),
            SizedBox(
              height: 16.w,
            ),
            Wrap(
              spacing: 26.w,
              children: [
                if (improvement?.answer != null)
                  ...improvement!.answer!.map((e) {
                    return e.label == improvement.currentAnswer?.label
                        ? Container(
                            padding:
                                EdgeInsets.fromLTRB(20.w, 12.w, 20.w, 12.w),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16.w),
                                gradient: ColorUtil.blueToGreen),
                            child: Text(
                              e.label ?? "_",
                              style: style_1_24,
                            ),
                          )
                        : GestureDetector(
                            onTap: () {
                              improvement.currentAnswer = e;
                              notifier.notifyListeners();
                            },
                            child: Container(
                              padding:
                                  EdgeInsets.fromLTRB(20.w, 12.w, 20.w, 12.w),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.w),
                                  color: const Color(0xFFF0F8FA)),
                              child: Text(
                                e.label ?? "_",
                                style: style_1_24,
                              ),
                            ),
                          );
                  }),
              ],
            ),
            Container(
                padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
                margin: EdgeInsets.fromLTRB(0.w, 24.w, 0.w, 24.w),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.w), color: btnColor),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Text(
                    //   "建议使用",
                    //   style: style_2_24,
                    // ),
                    SizedBox(
                      height: 16.w,
                    ),
                    SelectableTextUtil.editableText(
                      context,
                      text: improvement?.currentAnswer?.english ?? "_",
                      style: style_1_32_400,
                    ),
                    ...[
                      SizedBox(
                        height: 24.w,
                      ),
                      Text(
                        improvement?.currentAnswer?.chinese ?? "_",
                        style: style_2_24,
                      ),
                      SizedBox(
                        height: 24.w,
                      ),
                    ],
                    _lineWidget(),
                    SizedBox(
                      height: 24.w,
                    ),
                    Row(
                      children: [
                        PlayButtonWidget(
                            style: PlayButtonStyle.white,
                            text: improvement?.currentAnswer?.english,
                            audioUrl: improvement?.currentAnswer?.audioUrl),
                        SizedBox(
                          width: 20.w,
                        ),
                        // GestureDetector(
                        //     onTap: () {
                        //       notifier
                        //           .translate(improvement?.answer?[0].english);
                        //     },
                        //     child: Image(
                        //       image: const AssetImage(
                        //           "images/chat_translate_white.png"),
                        //       width: 48.w,
                        //       height: 48.w,
                        //     )),
                        // SizedBox(
                        //   width: 20.w,
                        // ),
                        GestureDetector(
                            onTap: () {
                              Clipboard.setData(ClipboardData(
                                  text: improvement?.currentAnswer?.english ??
                                      ""));
                              EasyLoading.showToast("已复制");
                            },
                            child: Image(
                              image: const AssetImage(
                                  "images/chat_copy_white.png"),
                              width: 48.w,
                              height: 48.w,
                            )),
                        SizedBox(
                          width: 20.w,
                        ),
                        _collectWidget(context,
                            improvement?.currentAnswer?.english ?? "", 1),
                        SizedBox(
                          width: 20.w,
                        ),
                        const Spacer(),
                        GestureDetector(
                            onTap: () {
                              _gotoFollowRead(context,
                                  improvement?.currentAnswer?.english ?? "");
                            },
                            child: Image.asset(
                              "images/fllow_btn2.png",
                              height: 48.w,
                            ))
                      ],
                    ),
                  ],
                )),
            // Text(
            //   improvement?.currentAnswer?.chinese ?? "_",
            //   style: style_2_28,
            // ),
          ]))
    ]);
  }

  Widget _progressWidget(String title, int socre) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Row(
        children: [
          Text(
            title,
            style: style_2_28,
          ),
          SizedBox(
            width: 15.w,
          ),
          Text(
            "$socre分",
            style: style_1_28,
          ),
        ],
      ),
      SizedBox(
        height: 8.w,
      ),
      Container(
        height: 20.w,
        width: 240.w,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.w),
            color: const Color(0x1941C0FF)),
        child: FractionallySizedBox(
          alignment: Alignment.centerLeft,
          widthFactor: socre / 100.0,
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.w),
                gradient: ColorUtil.blueToGreen),
          ),
        ),
      )
    ]);
  }

  Widget _originTextWidget(BuildContext context, Record? improvement) {
    IeltsSpeakingReportNotifier notifier =
        context.read<IeltsSpeakingReportNotifier>();

    final words = improvement?.pronunciationEvaluationResults?.words ?? [];
    return Container(
      padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.w), color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...[
            // SizedBox(
            //   height: 24.w,
            // ),
            Text(
              "你的回答",
              style: TextStyle(fontSize: 24.w, color: const Color(0xFF646E70)),
            ),
            SizedBox(
              height: 24.w,
            ),
          ],
          Row(
            children: [
              Expanded(
                child: SelectableTextUtil.editableRichText(context,
                    textSpan: TextSpan(children: [
                      for (int i = 0; i < words.length; i++)
                        TextSpan(
                          text:
                              "${words[i].word ?? ""}${words[i].rightSymbol ?? " "}",
                          style:
                              TextStyle(fontSize: 32.sp, color: words[i].color),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              SelectableTextUtil.showVocabularyAlertWidget(
                                  context, words[i].word ?? "");
                            },
                        ),
                    ])),
              ),
            ],
          ),
          ..._translateWidgets(
              context, improvement?.grammarDetectionResults?.text),
          _lineWidget(),
          SizedBox(
            height: 24.w,
          ),
          Row(
            children: [
              PlayButtonWidget(
                style: PlayButtonStyle.blue,
                audioUrl: improvement?.pronunciationEvaluationResults?.audioUrl,
              ),
              SizedBox(
                width: 20.w,
              ),
              GestureDetector(
                  onTap: () {
                    notifier
                        .translate(improvement?.grammarDetectionResults?.text);
                  },
                  child: Image(
                    image: const AssetImage("images/chat_translate_blue.png"),
                    width: 48.w,
                    height: 48.w,
                  )),
              SizedBox(
                width: 20.w,
              ),
              GestureDetector(
                  onTap: () {
                    Clipboard.setData(ClipboardData(
                        text:
                            improvement?.grammarDetectionResults?.text ?? ""));
                    EasyLoading.showToast("已复制");
                  },
                  child: Image(
                    image: const AssetImage("images/chat_copy_blue.png"),
                    width: 48.w,
                    height: 48.w,
                  )),
              SizedBox(
                width: 20.w,
              ),
              _collectWidget(
                  context, improvement?.grammarDetectionResults?.text ?? "", 2),
              SizedBox(
                width: 20.w,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _originQuestionWidget(BuildContext context, Record? improvement) {
    IeltsSpeakingReportNotifier notifier =
        context.read<IeltsSpeakingReportNotifier>();

    return Container(
      padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.w), color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...[
            // SizedBox(
            //   height: 24.w,
            // ),
            Text(
              "雅思考官",
              style: TextStyle(fontSize: 24.w, color: const Color(0xFF646E70)),
            ),
            SizedBox(
              height: 24.w,
            ),
          ],
          Row(
            children: [
              Expanded(
                child: SelectableTextUtil.editableText(
                  context,
                  text: improvement?.question?.english ?? "",
                  style:
                      TextStyle(fontSize: 32.w, color: const Color(0xFF061B1F)),
                ),
              ),
            ],
          ),
          ...[
            SizedBox(
              height: 24.w,
            ),
            Text(
              improvement?.question?.chinese ?? "",
              style: style_2_24,
            ),
            SizedBox(
              height: 24.w,
            ),
          ],
          _lineWidget(),
          SizedBox(
            height: 24.w,
          ),
          Row(
            children: [
              PlayButtonWidget(
                style: PlayButtonStyle.blue,
                audioUrl: improvement?.questionAudioUrl,
              ),
              SizedBox(
                width: 20.w,
              ),
              GestureDetector(
                  onTap: () {
                    notifier.display(improvement?.question?.english,
                        improvement?.question?.chinese);
                  },
                  child: Image(
                    image: const AssetImage("images/chat_translate_blue.png"),
                    width: 48.w,
                    height: 48.w,
                  )),
              SizedBox(
                width: 20.w,
              ),
              GestureDetector(
                  onTap: () {
                    Clipboard.setData(ClipboardData(
                        text: improvement?.question?.english ?? ""));
                    EasyLoading.showToast("已复制");
                  },
                  child: Image(
                    image: const AssetImage("images/chat_copy_blue.png"),
                    width: 48.w,
                    height: 48.w,
                  )),
              SizedBox(
                width: 20.w,
              ),
              _collectWidget(context, improvement?.question?.english ?? "", 2),
              SizedBox(
                width: 20.w,
              ),
            ],
          ),
        ],
      ),
    );
  }

  //1 白色 2 蓝色
  Widget _collectWidget(BuildContext context, String text, int style) {
    IeltsSpeakingReportNotifier notifier =
        context.read<IeltsSpeakingReportNotifier>();

    return GestureDetector(
        onTap: () {
          notifier.collectedList.contains(text) == true
              ? notifier.cancelCollection(text)
              : notifier.collection(text);
        },
        child: Image(
          image: AssetImage(notifier.collectedList.contains(text) == true
              ? "images/chat_collected.png"
              : style == 1
                  ? "images/chat_collect_white.png"
                  : "images/chat_collect_blue.png"),
          width: 48.w,
          height: 48.w,
        ));
  }
}
