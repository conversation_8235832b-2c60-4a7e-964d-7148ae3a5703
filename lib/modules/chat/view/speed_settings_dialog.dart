import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/find/pages/explore_talk_page.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SpeedSettingsDialog extends StatefulWidget {
  final ThemeStyle themeStyle;
  const SpeedSettingsDialog({super.key, this.themeStyle = ThemeStyle.light});

  @override
  State<SpeedSettingsDialog> createState() => _SpeedSettingsDialogState();
}

class _SpeedSettingsDialogState extends State<SpeedSettingsDialog> {
  @override
  Widget build(BuildContext context) {
    num? speed =
        LoginUtil.currentUserInfoModel()?.data?.userSettings?.playbackSpeed;
    return Container(
      padding:
          EdgeInsets.only(left: 40.w, right: 40.w, bottom: 60.w, top: 20.w),
      decoration: BoxDecoration(
          color: widget.themeStyle == ThemeStyle.dark
              ? Colors.black
              : Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(40.w), topRight: Radius.circular(40.w))),
      clipBehavior: Clip.hardEdge,
      width: double.infinity,
      child: ListView(
        physics: const NeverScrollableScrollPhysics(),
        children: [
          SizedBox(
            height: 12.w,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "语速设置",
                style: style_1_36.copyWith(
                    color: widget.themeStyle == ThemeStyle.dark
                        ? Colors.white
                        : null),
              ),
              const Spacer(),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Padding(
                  padding: EdgeInsets.all(24.w),
                  child: Image(
                    image: const AssetImage("images/dialog_close.png"),
                    fit: BoxFit.fill,
                    width: 26.w,
                    height: 26.w,
                    color: widget.themeStyle == ThemeStyle.dark
                        ? Colors.white
                        : null,
                  ),
                ),
              ),
            ],
          ),
          Container(
              width: double.infinity,
              margin: EdgeInsets.fromLTRB(0.w, 24.w, 0.w, 24.w),
              decoration: BoxDecoration(
                  color: widget.themeStyle == ThemeStyle.dark
                      ? Colors.black
                      : Colors.white,
                  borderRadius: BorderRadius.circular(32.w)),
              child: GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Column(
                  children: [
                    _speechOptionWidget(context, "较慢(0.6x)", speed == 0.6, 0.6),
                    _lineWidget(padding: 24.w),
                    _speechOptionWidget(context, "稍慢(0.8x)", speed == 0.8, 0.8),
                    _lineWidget(padding: 24.w),
                    _speechOptionWidget(context, "正常(1.0x)", speed == 1, 1),
                    _lineWidget(padding: 24.w),
                    _speechOptionWidget(context, "较快(1.2x)", speed == 1.2, 1.2),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _speechOptionWidget(
      BuildContext context, String text, bool seleced, num speed) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () async {
        await Api.setPlaybackSpeed(speed);
        LoginUtil.currentUserInfoModel()?.data?.userSettings?.playbackSpeed =
            speed;
        setState(() {});
        Navigator.pop(context);
      },
      child: Container(
        height: 88.w,
        width: double.infinity,
        child: Row(
          children: [
            SizedBox(
              width: 24.w,
            ),
            Text(
              text,
              style: style_1_28_400.copyWith(
                  color: widget.themeStyle == ThemeStyle.dark
                      ? Colors.white
                      : null),
            ),
            const Spacer(),
            if (seleced)
              Image.asset(
                "images/check-right.png",
                width: 36.w,
                height: 36.w,
                color: widget.themeStyle == ThemeStyle.dark
                    ? const Color(0xFF41C0FF)
                    : null,
              ),
            SizedBox(
              width: 24.w,
            ),
          ],
        ),
      ),
    );
  }

  Container _lineWidget({double? padding}) {
    return Container(
      height: 1.w,
      margin: EdgeInsets.only(left: padding ?? 0, right: padding ?? 0),
      width: double.infinity,
      color: ColorUtil.separated,
    );
  }
}
