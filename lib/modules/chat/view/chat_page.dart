import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/check_udpate_model/check_udpate_model.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_page_provider.dart';
import 'package:flutter_app_kouyu/modules/chat/view/learning_settings.dart';
import 'package:flutter_app_kouyu/widgets/app_update_widget.dart';
import 'package:flutter_app_kouyu/widgets/overlay_manager.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_app_kouyu/modules/chat/widgets/theme_cards_section.dart';
import 'package:provider/provider.dart';
import 'package:flutter_app_kouyu/modules/chat/widgets/title_widget.dart'; // 添加引用
import 'package:flutter_app_kouyu/modules/chat/widgets/home_banner_widget.dart';
import 'package:flutter_app_kouyu/modules/chat/widgets/quick_practice_section.dart';
import 'package:flutter_app_kouyu/modules/chat/widgets/chat_widget.dart';

class ChatPage extends StatefulWidget {
  const ChatPage({super.key});
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const ChatPage());
  }

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  bool needSetup = false;
  // late VideoPlayerController _controller;
  // late Future<void> _initializeVideoPlayerFuture;
  // final VlcPlayerController _videoPlayerController =
  //     VlcPlayerController.network("");

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: LoginUtil.updateUserInfo(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            needSetup = snapshot.data?.data?.enterTheProcess ?? false;
            WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
              if (needSetup) {
                Navigator.of(context).pushNamedAndRemoveUntil(
                    "/setup_setting_one", (route) => false);
              } else {
                checkVersion();
              }
            });
          }

          return ChangeNotifierProvider<ChatPageModel>(
            create: (context) => ChatPageModel()
              ..getUserInfo()
              ..getCarouselList()
              ..getWeeklyFreeTopicList()
              ..getstudyProgressList(),
            child: Consumer<ChatPageModel>(
              builder: (context, model, _) {
                return Stack(
                  children: [
                    Scaffold(
                      backgroundColor: Colors.white,
                      body: Stack(
                        children: [
                          Image.asset(
                            "images/home_bg.png",
                            fit: BoxFit.cover,
                          ),
                          ListView(
                            children: [
                              Column(
                                children: [
                                  Stack(
                                    // 设置clipBehavior为none，允许子组件超出 Stack 边界
                                    clipBehavior: Clip.none,
                                    children: [
                                      if (model
                                              .userInfoModel
                                              ?.data
                                              ?.userSettings
                                              ?.homePageImageUrl !=
                                          null)
                                        Positioned(
                                          top: 200.w,
                                          left: 0,
                                          right: 0,
                                          bottom: 0,
                                          child: Opacity(
                                            opacity: 0.3,
                                            child: Image.network(
                                              height: 293.w,
                                              model
                                                      .userInfoModel
                                                      ?.data
                                                      ?.userSettings
                                                      ?.homePageImageUrl ??
                                                  "",
                                            ),
                                          ),
                                        ),
                                      TitleWidget(
                                        userInfoModel: model.userInfoModel,
                                      ),
                                      Positioned(
                                        top: 0,
                                        right: 32.w,
                                        bottom: -30.w,
                                        child: IgnorePointer(
                                          child: Container(
                                            alignment: Alignment.bottomRight,
                                            child: Visibility(
                                              visible: snapshot.connectionState ==
                                                        ConnectionState.done,
                                              child: CachedNetworkImage(
                                                imageUrl: model.userInfoModel
                                                                    ?.data
                                                                    ?.userSettings
                                                                    ?.characterImageUrl ??
                                                                "",
                                                height: 390.w,
                                                fit: BoxFit.fitHeight,
                                                ),
                                              ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 16.w),
                                  const ChatWidget(),
                                  const HomeBannerWidget(),
                                  const ThemeCardsSection(),
                                  const QuickPracticeSection(),
                                ],
                              ),
                            ],
                          ),
                          Positioned(
                            top: 110.w,
                            right: 40.w,
                            child: GestureDetector(
                                onTap: () {
                                  Navigator.of(context).push(LearningSettingsPage.route());
                                },
                                child: Image.asset(
                                  "images/icon_setting.png",
                                  width: 24,
                                  height: 24,
                                )),
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
          );
        });
  }

  @override
  void initState() {
    //校验支付
    super.initState();

    // final args = ModalRoute.of(context)!.settings.arguments as Map;
  }

  @override
  void dispose() {
    // _controller?.dispose();

    super.dispose();
  }

  checkVersion() async {
    if (LoginUtil.checkAppVersion == false) {
      return;
    }
    LoginUtil.checkAppVersion = false;
    CheckUdpateModel checkUdpateModel = await Api.checkUdpate();
    bool showAlert = false;
    if (checkUdpateModel.data?.forceUpdate == true) {
      //强制更新
      showAlert = true;
    } else if (checkUdpateModel.data?.needUpdate == true) {
      //非强制更新，判断上次提示的时间
      int? lastTime = await LoginUtil.getLastUpdateAlertTime();
      if (lastTime == null) {
        showAlert = true;
      } else if (DateTime.now().millisecondsSinceEpoch - lastTime >
          3 * 24 * 60 * 60 * 1000) {
        //超过3天
        showAlert = true;
      }
    }
    if (showAlert) {
      //提示app更新
      OverlayManager.getInstance().showOverlay(
        true,
        builder: (close) => AppUpdateWidget(
          close: close,
          checkUdpateModel: checkUdpateModel,
        ),
      );
      LoginUtil.resetShowUpdateAlertTime();
    }
  }

  showSendMinuts() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          scrollable: true,
          elevation: 0,
          backgroundColor: const Color(0xFFF3FBFD),
          contentPadding: EdgeInsets.zero,
          insetPadding: const EdgeInsets.symmetric(horizontal: 18),
          content: SizedBox(
            width: 800,
            child: Stack(
              children: [
                Center(
                  child: Column(
                    children: [
                      Image.asset(
                        "images/alert_face.png",
                        width: 160,
                        height: 160,
                      ),
                      Text(
                        '欢迎使用\n送您15分钟学习时长',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontSize: 20.sp,
                            fontWeight: FontWeight.w800,
                            color: const Color(0xFF061B1F)),
                      ),
                      const SizedBox(
                        height: 12,
                      ),
                      Text(
                        '邀请好友可获得更多时长哦~',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w400,
                            color: const Color(0xFF646E70)),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Container(
                        color: Colors.white,
                        child: Container(
                          height: 52,
                          margin: const EdgeInsetsDirectional.only(
                              start: 20, end: 20, bottom: 12),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            gradient: const LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Color(0xFF98ECEC),
                                Color(0xFF84E9FF),
                                Color(0xFF9BE1FF)
                              ],
                            ),
                          ),
                          child: TextButton(
                              style: ButtonStyle(
                                textStyle: MaterialStatePropertyAll(TextStyle(
                                    fontSize: 20.sp,
                                    fontWeight: FontWeight.w800)),
                              ),
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              child: Center(
                                child: Text(
                                  "开心收下",
                                  style: TextStyle(
                                      color: const Color(0xFF061B1F),
                                      fontSize: 18.sp,
                                      fontWeight: FontWeight.w800),
                                ),
                              )),
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                    ],
                  ),
                ),
                Positioned(
                  right: 20,
                  top: 20,
                  child: GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: const SizedBox(
                          width: 24,
                          child: Center(
                            child: Icon(Icons.close),
                          ))),
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
