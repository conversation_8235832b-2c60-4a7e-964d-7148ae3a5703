import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_report_model/chat_report_model.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_report_model/improvement_list.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_report_model/pronunciation_evaluation_result.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_report_model.dart';
import 'package:flutter_app_kouyu/modules/chat/view/follow_read_widget.dart';
import 'package:flutter_app_kouyu/modules/my/pages/high_frequency_vocabulary_page.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_app_kouyu/widgets/selected_text.dart';
import 'package:flutter_app_kouyu/widgets/share-widget/share_image_widget.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gif_view/gif_view.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';

class ChatReportPage extends StatelessWidget {
  final num conversationId;
  final ReportDetailType type;
  const ChatReportPage(
      {super.key, required this.conversationId, required this.type});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<ChatReportNotifier>(create: (_) {
      return ChatReportNotifier(conversationId: conversationId, type: type);
    }, child: Consumer<ChatReportNotifier>(builder: (context, model, child) {
      return Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          leadingWidth: 300,
          elevation: 0,
          backgroundColor: Colors.transparent,
          leading: CustomAppbar.leftWidget(context, text: "对话报告"),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ShareImageWidget(
                getImageFunc: model.getShareImageUrls,
              ),
            ],
          ),
        ),
        backgroundColor: const Color(0xFFF3FBFD),
        body: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            FocusScope.of(context).unfocus(); // 点击其他地方收起键盘
          },
          child: ListView(
            padding: EdgeInsets.only(top: 0.w),
            children: [
              Screenshot(
                controller: model.screenshotController,
                child: Stack(
                  children: [
                    Container(
                      padding: EdgeInsets.only(
                          left: 40.w, right: 40.w, bottom: 60.w, top: 20.w),
                      decoration: const BoxDecoration(
                          color: const Color(0xFFF3FBFD),
                          image: DecorationImage(
                              alignment: Alignment.topCenter,
                              image: AssetImage("images/report_top_score.png"),
                              fit: BoxFit.fitWidth)),
                      // height: 1000,
                      width: double.infinity,
                      child: ListView(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        children: [
                          ..._topWidget(model.model),
                          SizedBox(
                            height: 32.w,
                          ),
                          ..._scroreWidget(context, model.model),
                          SizedBox(
                            height: 32.w,
                          ),
                          Text(
                            "复盘提升",
                            style: style_1_36,
                          ),
                          Container(
                              padding:
                                  EdgeInsets.fromLTRB(0.w, 24.w, 0.w, 24.w),
                              child: ListView.separated(
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  itemBuilder: (content, index) {
                                    return _reviewWidget(
                                        context,
                                        model.model!.data!
                                            .improvementList![index]);
                                  },
                                  separatorBuilder: (content, index) {
                                    return SizedBox(
                                      height: 20.w,
                                    );
                                  },
                                  itemCount: model.model?.data?.improvementList
                                          ?.length ??
                                      0)),
                        ],
                      ),
                    ),
                    Positioned(
                        top: 155.w,
                        left: 460.w,
                        child: SizedBox(
                            height: 114.w,
                            width: 220.w,
                            child: Center(
                                child: Text(
                              "${model.model?.data?.totals ?? "_"}",
                              style: TextStyle(
                                  color: const Color(0xFFFCEEA7),
                                  fontSize: 90.sp,
                                  fontWeight: FontWeight.w800),
                            ))))
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }));
  }

  Widget _voiceWidget(String type, String url) {
    return GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
        CommonUtils.playVideo(url);
      },
      child: Container(
        height: 48.w,
        decoration: BoxDecoration(
            border: Border.all(color: ColorUtil.separated),
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.w)),
        child: Row(
          children: [
            SizedBox(
              width: 24.w,
            ),
            Text(
              type,
              style: style_1_24,
            ),
            PlayButtonWidget2(
              audioUrl: url,
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _scroreWidget(BuildContext context, ChatReportModel? model) {
    return [
      Row(
        children: [
          Text(
            "评分明细",
            style: style_1_36,
          ),
          SizedBox(
            width: 16.w,
          ),
          Text(
            model?.data?.encouragement ?? "_",
            style: style_2_24,
          ),
        ],
      ),
      Container(
        margin: EdgeInsets.only(top: 24.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24.w), color: Colors.white),
        child: Column(
          children: [
            _radarWidget(model),
            Padding(
                padding: EdgeInsets.only(left: 25.w, right: 25.w),
                child: _lineWidget()),
            Padding(
                padding: EdgeInsets.fromLTRB(32.w, 0.w, 0.w, 0.w),
                child: Row(
                  children: [
                    Text(
                      "核心词汇 ",
                      style: style_1_28_400,
                    ),
                    Text(
                      "${model?.data?.numCoreVocabularies ?? "_"}个",
                      style: style_1_32,
                    ),
                    const Spacer(),
                    TextButton.icon(
                        onPressed: () {
                          Navigator.push(
                              context,
                              HighFrequencyVocabularyPage.route(
                                  "$conversationId"));
                        },
                        icon: Text(
                          "查看详情",
                          style: style_2_24,
                        ),
                        label: Image.asset(
                          "images/arrow_right_black.png",
                          width: 12.w,
                          height: 20.w,
                        ))
                  ],
                )),
            Padding(
                padding: EdgeInsets.only(left: 25.w, right: 25.w),
                child: _lineWidget()),
            Padding(
                padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
                child: Row(
                  children: [
                    Text(
                      "对话轮数 ",
                      style: style_1_28_400,
                    ),
                    Text(
                      "${model?.data?.conversationRounds ?? "_"}轮",
                      style: style_1_32,
                    ),
                    const Spacer(),
                    Text(
                      "${model?.data?.excellentRounds ?? "_"}",
                      style: style_green_40,
                    ),
                    Text(
                      " 表现优秀",
                      style: style_2_24,
                    ),
                    Container(
                      margin: EdgeInsets.only(left: 16.w, right: 16.w),
                      width: 1.w,
                      height: 20.w,
                      color: ColorUtil.separated,
                    ),
                    Text(
                      "${model?.data?.improvementRounds ?? "_"}",
                      style: style_orange_40,
                    ),
                    Text(
                      " 有待提升",
                      style: style_2_24,
                    )
                  ],
                ))
          ],
        ),
      )
    ];
  }

  Container _lineWidget() {
    return Container(
      height: 1.w,
      width: double.infinity,
      color: ColorUtil.separated,
    );
  }

  Widget _radarWidget(ChatReportModel? model) {
    List<PronunciationEvaluationResult> list =
        model?.data?.pronunciationEvaluationResults ?? [];
    return SizedBox(
      height: 494.w,
      width: double.infinity,
      child: Column(
        children: [
          const Spacer(),
          _radarScoreWidget(list, 0),
          Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              const Spacer(),
              Column(
                children: [
                  _radarScoreWidget(list, 1),
                  SizedBox(
                    height: 100.w,
                  )
                ],
              ),
              Image.network(
                model?.data?.radarChartUrl ?? "",
                width: 400.w,
                fit: BoxFit.fitWidth,
              ),
              Column(
                children: [
                  _radarScoreWidget(list, 2),
                  SizedBox(
                    height: 100.w,
                  )
                ],
              ),
              const Spacer(),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _radarScoreWidget(list, 3),
              _radarScoreWidget(list, 4),
            ],
          ),
          const Spacer(),
        ],
      ),
    );
  }

  RichText _radarScoreWidget(List<PronunciationEvaluationResult> list, int i) {
    return RichText(
        text: TextSpan(children: [
      TextSpan(
        text: list.length > i ? list[i].name ?? "_" : "_",
        style: style_2_24,
      ),
      WidgetSpan(
          child: SizedBox(
        width: 10.w,
      )),
      TextSpan(
        text: "${list.length > i ? list[i].score ?? "_" : "_"}",
        style: TextStyle(
            fontSize: 32.sp,
            fontWeight: FontWeight.w700,
            color: [
              const Color(0xFF00B578),
              const Color(0xFF061B1F),
              const Color(0xFFFF8F1F),
            ][0]),
      ),
    ]));
  }

  List<Widget> _topWidget(ChatReportModel? model) {
    return [
      Text(
        (model?.data?.goalEnglish != null &&
                model!.data!.goalEnglish!.isNotEmpty)
            ? (model.data?.hasFinishGoal == true ? "恭喜你" : "很遗憾")
            : "",
        style: TextStyle(
            fontWeight: FontWeight.w900, fontSize: 64.sp, color: color_1),
      ),
      model?.data?.goalChinese != null
          ? Text(
              model?.data?.hasFinishGoal == true ? "已完成以下对话目标" : "当前你未完成以下对话目标",
              style: style_2_24,
            )
          : SizedBox(
              height: 32.w,
            ),
      SizedBox(
        height: 32.w,
      ),
      model?.data?.goalChinese != null
          ? _chatTargetWidget(model)
          : SizedBox(
              height: 0.w,
            ),
    ];
  }

  Container _chatTargetWidget(ChatReportModel? model) {
    return Container(
      padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.w), color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Visibility(
                  visible: model?.data?.hasFinishGoal == true,
                  child: Image.asset("images/checkbox-circle-fill_green.png",
                      width: 36.w, height: 36.w)),
              Visibility(
                visible: model?.data?.hasFinishGoal == true,
                child: SizedBox(
                  width: 8.w,
                ),
              ),
              Expanded(
                  child: Text(
                model?.data?.goalChinese ?? "_",
                style: style_1_28,
              ))
            ],
          ),
          Text(
            model?.data?.goalEnglish ?? "_",
            style: style_2_24,
          )
        ],
      ),
    );
  }

  Widget _reviewWidget(BuildContext context, ImprovementList improvement) {
    return Column(
      children: [
        _originTextWidget(context, improvement),
        SizedBox(
          height: 20.w,
        ),
        Row(
          children: [
            _voiceWidget("美音", improvement.usAudioUrl ?? ""),
            SizedBox(
              width: 16.w,
            ),
            _voiceWidget("英音", improvement.ukAudioUrl ?? ""),
            const Spacer(),
            GestureDetector(
              onTap: () {
                FocusScope.of(context).unfocus();

                _gotoFollowRead(context, improvement.original ?? "");
              },
              child: Container(
                height: 56.w,
                width: 143.w,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.w),
                    color: const Color(0xFF2693FF)),
                child: Center(
                  child: Text(
                    "再读一遍",
                    style: style_1_24.copyWith(color: Colors.white),
                  ),
                ),
              ),
            ),
          ],
        ),
        SizedBox(
          height: 20.w,
        ),
        _voiceSoeWidget(improvement),
        Padding(
          padding: EdgeInsets.only(left: 24.w, right: 24.w),
          child: _lineWidget(),
        ),
        _analysisWidget(context, improvement),
        Padding(
          padding: EdgeInsets.only(left: 24.w, right: 24.w),
          child: _lineWidget(),
        ),
        _suggessWidget(context, improvement)
      ],
    );
  }

  Widget _voiceSoeWidget(ImprovementList improvement) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24.w),
                  topRight: Radius.circular(24.w)),
              color: Colors.white),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "发音",
                style: style_1_28,
              ),
              SizedBox(
                width: 147.w,
                height: 95.w,
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Image.asset(
                        "images/score_bg.png",
                        width: 147.w,
                        height: 40.w,
                      ),
                    ),
                    Align(
                        alignment: Alignment.topLeft,
                        child: Text(
                          "${improvement.suggestScore ?? "_"}",
                          style: style_green_64,
                        )),
                  ],
                ),
              ),
              Text(
                "表达流利顺畅，和你交流是件愉悦的事",
                style: style_2_28,
              ),
              SizedBox(
                height: 24.w,
              ),
              Row(
                children: [
                  Column(
                    children: [
                      _progressWidget('准确度', improvement.pronAccuracy ?? 0),
                      SizedBox(
                        height: 32.w,
                      ),
                      _progressWidget('流畅度', improvement.pronFluency ?? 0),
                      SizedBox(
                        height: 32.w,
                      ),
                      _progressWidget('完整度度', improvement.pronCompletion ?? 0),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        width: 346.w,
                        height: 193.w,
                        decoration: const BoxDecoration(
                            image: DecorationImage(
                                image: AssetImage("images/voice_speech_bg.png"),
                                fit: BoxFit.fill)),
                        child: Stack(
                          alignment: Alignment.topCenter,
                          children: [
                            Positioned.fill(
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 32.w,
                                  ),
                                  Transform.rotate(
                                    angle: ((improvement.speed ?? 0) - 90) /
                                        180 *
                                        pi,
                                    alignment: Alignment.bottomCenter,
                                    child: Image.asset(
                                      "images/voice_speech_arrow.png",
                                      width: 12.w,
                                      height: 116.w,
                                      fit: BoxFit.fill,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Positioned(
                                top: 60.w,
                                child: Center(
                                  child: Text(
                                    "${improvement.speed ?? "_"}",
                                    style: style_1_48,
                                  ),
                                ))
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 10.w,
                      ),
                      RichText(
                          textAlign: TextAlign.justify,
                          text: TextSpan(children: [
                            TextSpan(
                              text: improvement.speedComment ?? "_",
                              style: style_bue_24,
                            ),
                            WidgetSpan(
                                child: SizedBox(
                              width: 10.w,
                            )),
                            // WidgetSpan(
                            //     alignment: PlaceholderAlignment.middle,
                            //     child: Image.asset(
                            //       "images/blue_help.png",
                            //       width: 20.w,
                            //       height: 20.w,
                            //       fit: BoxFit.fill,
                            //     )),
                          ])),
                    ],
                  )
                ],
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget _analysisWidget(BuildContext context, ImprovementList improvement) {
    ChatReportNotifier notifier = context.read<ChatReportNotifier>();

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Container(
          padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
          width: double.infinity,
          color: Colors.white,
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(
              "语法",
              style: style_1_28,
            ),
            Visibility(
              visible: improvement.amendment != null &&
                  improvement.amendment!.isNotEmpty,
              child: Container(
                  padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
                  margin: EdgeInsets.fromLTRB(0.w, 24.w, 0.w, 24.w),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24.w),
                      color: btnColor),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "建议使用",
                        style: style_2_24,
                      ),
                      SizedBox(
                        height: 16.w,
                      ),
                      SelectableTextUtil.editableText(
                        context,
                        text: improvement.amendment ?? "_",
                        style: style_1_32_400,
                      ),
                      ..._translateWidgets(context, improvement.amendment),
                      _lineWidget(),
                      SizedBox(
                        height: 24.w,
                      ),
                      Row(
                        children: [
                          PlayButtonWidget(
                            style: PlayButtonStyle.white,
                            text: improvement.amendment,
                          ),
                          SizedBox(
                            width: 20.w,
                          ),
                          GestureDetector(
                              onTap: () {
                                FocusScope.of(context).unfocus();

                                notifier.translate(improvement.amendment);
                              },
                              child: Image(
                                image: const AssetImage(
                                    "images/chat_translate_white.png"),
                                width: 48.w,
                                height: 48.w,
                              )),
                          SizedBox(
                            width: 20.w,
                          ),
                          GestureDetector(
                              onTap: () {
                                FocusScope.of(context).unfocus();

                                Clipboard.setData(ClipboardData(
                                    text: improvement.amendment ?? ""));
                                EasyLoading.showToast("已复制");
                              },
                              child: Image(
                                image: const AssetImage(
                                    "images/chat_copy_white.png"),
                                width: 48.w,
                                height: 48.w,
                              )),
                          SizedBox(
                            width: 20.w,
                          ),
                          _collectWidget(
                              context, improvement.amendment ?? "", 1),
                          SizedBox(
                            width: 20.w,
                          ),
                          const Spacer(),
                          GestureDetector(
                              onTap: () {
                                FocusScope.of(context).unfocus();

                                _gotoFollowRead(
                                    context, improvement.amendment ?? "");
                              },
                              child: Image.asset(
                                "images/fllow_btn2.png",
                                height: 28,
                              ))
                        ],
                      ),
                    ],
                  )),
            ),
            Text(
              improvement.explanation ?? "_",
              style: style_2_28,
            ),
          ])),
      Padding(
        padding: EdgeInsets.only(left: 24.w, right: 24.w),
        child: _lineWidget(),
      ),
    ]);
  }

  void _gotoFollowRead(BuildContext context, String text) {
    showModalBottomSheet(
        isScrollControlled: true,
        useSafeArea: true,
        constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height -
                MediaQuery.of(context).padding.top),
        backgroundColor: Colors.transparent,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20))),
        context: context,
        builder: (context) {
          return FollowReadWidget(
            text: text,
            conversationId: conversationId,
          );
        });
  }

  List<Widget> _translateWidgets(BuildContext context, String? text) {
    ChatReportNotifier notifier = context.read<ChatReportNotifier>();

    if (notifier.translateMap[text] == null) {
      return [
        SizedBox(
          height: 24.w,
        )
      ];
    }
    return [
      SizedBox(
        height: 24.w,
      ),
      Text(
        notifier.translateMap[text]!,
        style: style_2_24,
      ),
      SizedBox(
        height: 24.w,
      ),
    ];
  }

  Widget _suggessWidget(BuildContext context, ImprovementList improvement) {
    ChatReportNotifier notifier = context.read<ChatReportNotifier>();
    if (improvement.polishEnglish == null ||
        improvement.polishEnglish!.isEmpty) {
      return Container();
    }

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Container(
          padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
          color: Colors.white,
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(
              "润色",
              style: style_1_28,
            ),
            SizedBox(
              height: 16.w,
            ),
            Wrap(
              spacing: 26.w,
              children: [
                if (improvement.polishEnglish != null)
                  ...improvement.polishEnglish!.map((e) {
                    return e.name == improvement.currentPolishEnglish?.name
                        ? Container(
                            padding:
                                EdgeInsets.fromLTRB(20.w, 12.w, 20.w, 12.w),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16.w),
                                gradient: ColorUtil.blueToGreen),
                            child: Text(
                              e.name ?? "_",
                              style: style_1_24,
                            ),
                          )
                        : GestureDetector(
                            onTap: () {
                              FocusScope.of(context).unfocus();

                              improvement.currentPolishEnglish = e;
                              notifier.notifyListeners();
                            },
                            child: Container(
                              padding:
                                  EdgeInsets.fromLTRB(20.w, 12.w, 20.w, 12.w),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.w),
                                  color: const Color(0xFFF0F8FA)),
                              child: Text(
                                e.name ?? "_",
                                style: style_1_24,
                              ),
                            ),
                          );
                  }),
              ],
            ),
            Container(
                padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
                margin: EdgeInsets.fromLTRB(0.w, 24.w, 0.w, 24.w),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.w), color: btnColor),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "建议使用",
                      style: style_2_24,
                    ),
                    SizedBox(
                      height: 16.w,
                    ),
                    SelectableTextUtil.editableText(
                      context,
                      text:
                          improvement.currentPolishEnglish?.polishedSentence ??
                              "_",
                      style: style_1_32_400,
                    ),
                    ..._translateWidgets(context,
                        improvement.currentPolishEnglish?.polishedSentence),
                    _lineWidget(),
                    SizedBox(
                      height: 24.w,
                    ),
                    Row(
                      children: [
                        PlayButtonWidget(
                          style: PlayButtonStyle.white,
                          text: improvement
                              .currentPolishEnglish?.polishedSentence,
                        ),
                        SizedBox(
                          width: 20.w,
                        ),
                        GestureDetector(
                            onTap: () {
                              FocusScope.of(context).unfocus();

                              notifier.translate(improvement
                                  .currentPolishEnglish?.polishedSentence);
                            },
                            child: Image(
                              image: const AssetImage(
                                  "images/chat_translate_white.png"),
                              width: 48.w,
                              height: 48.w,
                            )),
                        SizedBox(
                          width: 20.w,
                        ),
                        GestureDetector(
                            onTap: () {
                              FocusScope.of(context).unfocus();

                              Clipboard.setData(ClipboardData(
                                  text: improvement.currentPolishEnglish
                                          ?.polishedSentence ??
                                      ""));
                              EasyLoading.showToast("已复制");
                            },
                            child: Image(
                              image: const AssetImage(
                                  "images/chat_copy_white.png"),
                              width: 48.w,
                              height: 48.w,
                            )),
                        SizedBox(
                          width: 20.w,
                        ),
                        _collectWidget(
                            context,
                            improvement
                                    .currentPolishEnglish?.polishedSentence ??
                                "",
                            1),
                        SizedBox(
                          width: 20.w,
                        ),
                        const Spacer(),
                        GestureDetector(
                            onTap: () {
                              FocusScope.of(context).unfocus();

                              _gotoFollowRead(
                                  context,
                                  improvement.currentPolishEnglish
                                          ?.polishedSentence ??
                                      "");
                            },
                            child: Image.asset(
                              "images/fllow_btn2.png",
                              height: 48.w,
                            ))
                      ],
                    ),
                  ],
                )),
            Text(
              improvement.currentPolishEnglish?.explanation ?? "_",
              style: style_2_28,
            ),
          ]))
    ]);
  }

  Widget _progressWidget(String title, int socre) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Row(
        children: [
          Text(
            title,
            style: style_2_28,
          ),
          SizedBox(
            width: 15.w,
          ),
          Text(
            "$socre分",
            style: style_1_28,
          ),
        ],
      ),
      SizedBox(
        height: 8.w,
      ),
      Container(
        height: 20.w,
        width: 240.w,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.w),
            color: const Color(0x1941C0FF)),
        child: FractionallySizedBox(
          alignment: Alignment.centerLeft,
          widthFactor: socre / 100.0,
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.w),
                gradient: ColorUtil.blueToGreen),
          ),
        ),
      )
    ]);
  }

  Widget _originTextWidget(BuildContext context, ImprovementList improvement) {
    ChatReportNotifier notifier = context.read<ChatReportNotifier>();

    final words = improvement.words ?? [];
    return Container(
      padding: EdgeInsets.fromLTRB(32.w, 24.w, 32.w, 24.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.w), color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: SelectableTextUtil.editableRichText(context,
                    textSpan: TextSpan(children: [
                      for (int i = 0; i < words.length; i++)
                        TextSpan(
                          text:
                              "${words[i].word ?? ""}${words[i].rightSymbol ?? " "}",
                          style:
                              TextStyle(fontSize: 32.sp, color: words[i].color),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              SelectableTextUtil.showVocabularyAlertWidget(
                                  context, words[i].word ?? "");
                            },
                        ),
                    ])),
              ),
            ],
          ),
          ..._translateWidgets(context, improvement.original),
          _lineWidget(),
          SizedBox(
            height: 24.w,
          ),
          Row(
            children: [
              PlayButtonWidget(
                style: PlayButtonStyle.blue,
                audioUrl: improvement.audioResult,
              ),
              SizedBox(
                width: 20.w,
              ),
              GestureDetector(
                  onTap: () {
                    FocusScope.of(context).unfocus();

                    notifier.translate(improvement.original);
                  },
                  child: Image(
                    image: const AssetImage("images/chat_translate_blue.png"),
                    width: 48.w,
                    height: 48.w,
                  )),
              SizedBox(
                width: 20.w,
              ),
              GestureDetector(
                  onTap: () {
                    FocusScope.of(context).unfocus();

                    Clipboard.setData(
                        ClipboardData(text: improvement.original ?? ""));
                    EasyLoading.showToast("已复制");
                  },
                  child: Image(
                    image: const AssetImage("images/chat_copy_blue.png"),
                    width: 48.w,
                    height: 48.w,
                  )),
              SizedBox(
                width: 20.w,
              ),
              _collectWidget(context,
                  improvement.currentPolishEnglish?.polishedSentence ?? "", 2),
              SizedBox(
                width: 20.w,
              ),
            ],
          ),
        ],
      ),
    );
  }

  //1 白色 2 蓝色
  Widget _collectWidget(BuildContext context, String text, int style) {
    ChatReportNotifier notifier = context.read<ChatReportNotifier>();

    return GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();

          notifier.collectedList.contains(text) == true
              ? notifier.cancelCollection(text)
              : notifier.collection(text);
        },
        child: Image(
          image: AssetImage(notifier.collectedList.contains(text) == true
              ? "images/chat_collected.png"
              : style == 1
                  ? "images/chat_collect_white.png"
                  : "images/chat_collect_blue.png"),
          width: 48.w,
          height: 48.w,
        ));
  }
}
