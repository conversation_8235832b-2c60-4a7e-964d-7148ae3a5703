import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/data.dart'
    as SoeData;

import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/follow_read_provider.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';
import 'package:flutter_app_kouyu/widgets/common-input-widget/left-ai-middle-btn-right-score.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gif_view/gif_view.dart';
import 'package:provider/provider.dart';

class FollowReadWidget extends StatelessWidget {
  final String text;
  final num conversationId;

  const FollowReadWidget(
      {super.key, required this.text, required this.conversationId});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<FollowReadProvider>(
      create: (context) =>
          FollowReadProvider(text: text, conversationId: conversationId)
            ..speakEn(),
      child: Consumer<FollowReadProvider>(
        builder: (context, model, child) {
          return Container(
            width: double.infinity,
            height: double.infinity,
            margin: EdgeInsets.only(left: 24.w, right: 24.w, bottom: 40.w),
            padding: EdgeInsets.only(
                left: 20.w, right: 20.w, bottom: 24.w, top: 24.w),
            decoration: BoxDecoration(
              image: const DecorationImage(
                  alignment: Alignment.topCenter,
                  image: AssetImage("images/flollow_read.bg.jpeg"),
                  fit: BoxFit.fill),
              borderRadius: BorderRadius.circular(40.w),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      "跟读一遍",
                      style: style_1_36,
                    ),
                    const Spacer(),
                    GestureDetector(
                      child: const Icon(Icons.close),
                      onTap: () {
                        Navigator.pop(context);
                      },
                    )
                  ],
                ),
                SizedBox(
                  height: 24.w,
                ),
                model.soeModel == null
                    ? Text(
                        text,
                        style: style_1_32_400,
                      )
                    : RichText(
                        text: TextSpan(children: [
                        for (int i = 0;
                            i < model.soeModel!.data!.words!.length;
                            i++)
                          TextSpan(
                            text:
                                "${model.soeModel!.data!.words![i].word ?? ""}${model.soeModel!.data!.words![i].rightSymbol ?? " "}",
                            style: TextStyle(
                                fontSize: 32.sp,
                                color: model.soeModel!.data!.words![i].color),
                          ),
                      ])),
                const Spacer(),
                if (model.soeModel != null) ..._soeList(model),
                model.voiceState == VoiceStateEnum.init
                    ? _followWidget(
                        context,
                        model.soeModel?.data?.defaultAudioUrl ?? "",
                        "${model.soeModel?.data?.suggestedScore ?? ""}",
                        model.soeModel?.data?.audioUrl)
                    : _speakingWidget(context),
              ],
            ),
          );
        },
      ),
    );
  }

  List<Widget> _soeList(FollowReadProvider model) {
    SoeData.Data soeModel = model.soeModel!.data!;
    return [
      SizedBox(
        width: double.infinity,
        height: 585.w,
        child: Stack(alignment: Alignment.topCenter, children: [
          Positioned(
              top: 192.w,
              child: Container(
                width: 585.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(40.w),
                  color: const Color(0xFFF0F8FA),
                ),
                child: Column(
                  children: [
                    SizedBox(
                      height: 100.w,
                    ),
                    Text(
                      soeModel.speedComment ?? "_",
                      style: style_1_28_400,
                    ),
                    SizedBox(
                      height: 24.w,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        scoreWidget(soeModel.pronFluency, "流利度"),
                        scoreWidget(soeModel.pronAccuracy, "准确度"),
                        scoreWidget(soeModel.pronCompletion, "完整度"),
                        // scoreWidget("_/分钟", "语速"),
                      ],
                    ),
                    SizedBox(
                      height: 24.w,
                    ),
                    _lineWidget(),
                    SizedBox(
                      height: 24.w,
                    ),
                    Row(
                      children: [
                        SizedBox(
                          width: 40.w,
                        ),
                        Text("${soeModel.speed}", style: style_1_40),
                        SizedBox(
                          width: 8.w,
                        ),
                        Text("单词/分钟", style: style_1_24),
                        const Spacer(),
                        Text(
                          "语速较快，水平不错",
                          style: style_1_28_400,
                        ),
                        SizedBox(
                          width: 40.w,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 40.w,
                    ),
                  ],
                ),
              )),
          Container(
            width: 392.w,
            height: 392.w,
            decoration: BoxDecoration(
              image: const DecorationImage(
                  alignment: Alignment.topCenter,
                  image: AssetImage("images/flollow_read_score_bg.png"),
                  fit: BoxFit.fill),
              borderRadius: BorderRadius.circular(40.w),
            ),
            child: Center(
                child: Text("${soeModel.suggestedScore ?? "_"}",
                    style: style_1_48.copyWith(fontSize: 50.sp))),
          )
        ]),
      ),
      SizedBox(
        height: 48.w,
      ),
    ];
  }

  Container _lineWidget() {
    return Container(
      margin: EdgeInsets.only(left: 40.w, right: 40.w),
      height: 1.w,
      width: double.infinity,
      color: ColorUtil.separated,
    );
  }

  Widget scoreWidget(int? score, String subTitle) {
    return Column(
      children: [
        Text("${score ?? "_"}",
            style: TextStyle(
                fontSize: 40.sp,
                fontWeight: FontWeight.w700,
                color: getColor(score ?? 0))),
        Text(subTitle, style: style_2_24),
      ],
    );
  }

  Color getColor(int Score) {
    if (Score <= 60) {
      return const Color.fromARGB(255, 249, 50, 0);
    } else if (Score <= 80) {
      return const Color(0xFF061B1F);
    } else if (Score <= 100) {
      return const Color.fromARGB(255, 42, 201, 92);
    }

    return const Color(0xFF061B1F);
  }

  Row _speakingWidget(BuildContext context) {
    FollowReadProvider model = context.read<FollowReadProvider>();

    return Row(
      children: [
        GestureDetector(
          onTap: () {
            model.stop();
          },
          child: Image(
            image: const AssetImage("images/chat_close.png"),
            width: 80.w,
            height: 80.w,
          ),
        ),
        SizedBox(
          width: 40.w,
        ),
        Expanded(
          child: GifView.asset(
            "images/chat_loading.gif",
            fit: BoxFit.cover,
            width: double.infinity,
            height: 48.w,
            repeat: ImageRepeat.repeat,
          ),
        ),
        SizedBox(
          width: 40.w,
        ),
        GestureDetector(
          onTap: () {
            model.sent();
          },
          child: Image(
            image: const AssetImage("images/chat_send.png"),
            width: 80.w,
            height: 80.w,
          ),
        ),
      ],
    );
  }

  Widget _followWidget(
      BuildContext context, String? url, String? score, String? userUrl) {
    FollowReadProvider model = context.read<FollowReadProvider>();
    if (score == null || score.isEmpty) {
      score = "";
    }

    return LeftAiMiddleBtnRightScore(
      aiUrl: url ?? "",
      btnOnTap: () {
        model.speakEn();
      },
      score: score,
      userUrl: userUrl ?? "",
    );
  }
}
