import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ChatReportAlert extends StatelessWidget {
  final bool finishTask;
  final bool hasTask;
  const ChatReportAlert(
      {super.key, required this.finishTask, required this.hasTask});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 24.w, right: 24.w, bottom: 50.w),
      child: Stack(
        children: [
          Positioned.fill(
            top: 52.w,
            child: Container(
              padding: EdgeInsets.only(
                  left: 40.w, right: 40.w, bottom: 60.w, top: 20.w),
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(
                    Radius.circular(40.w),
                  )),
              height: 450.w,
              clipBehavior: Clip.hardEdge,
              width: 100.w,
            ),
          ),
          if (hasTask)
            Positioned(
              top: 0,
              right: 0,
              child: Image.asset(
                  width: 298.w,
                  height: 298.w,
                  fit: BoxFit.fill,
                  finishTask
                      ? "images/report_alert_ok.png"
                      : "images/report_alert_fail.png"),
            ),
          Positioned(
            top: 100.w,
            left: 40.w,
            right: 40.w,
            child: Column(
              crossAxisAlignment: hasTask
                  ? CrossAxisAlignment.start
                  : CrossAxisAlignment.center,
              children: [
                Text(
                  hasTask
                      ? (finishTask ? "你已完成对话目标" : "你还未完成对话目标")
                      : "确定结束对话吗？",
                  style: TextStyle(
                      fontSize: 40.sp,
                      fontWeight: FontWeight.w700,
                      color: const Color(0xFF061B1F)),
                ),
                if (hasTask)
                  SizedBox(
                    height: 8.w,
                  ),
                Text(
                  !hasTask ? "" : "确定结束对话吗？",
                  style: style_1_24,
                ),
                if (hasTask)
                  SizedBox(
                    height: 40.w,
                  ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context, 1);
                  },
                  child: Container(
                    height: 102.w,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      gradient: ColorUtil.blueToGreen,
                      borderRadius: BorderRadius.all(Radius.circular(24.w)),
                    ),
                    child: Center(
                      child: Text(
                        finishTask ? "查看报告" : '结束并查看对话报告',
                        style: TextStyle(
                            fontSize: 36.sp,
                            fontWeight: FontWeight.w700,
                            color: ColorUtil.black1),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 24.w,
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Center(
                    child: Text(
                      "直接结束",
                      style: style_2_28,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
