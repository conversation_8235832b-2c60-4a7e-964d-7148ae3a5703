import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_report_model.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/sence_introduce_provider.dart';
import 'package:flutter_app_kouyu/modules/chat/view/chat_report_page.dart';
import 'package:flutter_app_kouyu/modules/chat/view/free_talk_page.dart';
import 'package:flutter_app_kouyu/modules/scene_study/pages/create_custom_scene_page.dart';
import 'package:flutter_app_kouyu/widgets/common_widget.dart';
import 'package:flutter_app_kouyu/widgets/more_setting_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../widgets/custome_appbar.dart';

class SenceIntroducePage extends StatelessWidget {
  const SenceIntroducePage({super.key});
  static Route<void> route(RouteSettings? routeSettings) {
    return MaterialPageRoute<void>(
        builder: (_) => const SenceIntroducePage(), settings: routeSettings);
  }

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)!.settings.arguments as Map;
    String topicCode = args["topic_code"]!;
    /// 是否可以学习, 从创建列表进来的
    bool canStudy = args["can_study"] ?? true;
    return ChangeNotifierProvider<SenceIntroduceProvider>(
      create: (_) =>
          SenceIntroduceProvider(topicCode: topicCode, canStudy: canStudy)..refreshData(),
      child: Consumer<SenceIntroduceProvider>(
        builder: (context, model, child) {
          return Scaffold(
              extendBodyBehindAppBar: true,
              appBar: AppBar(
                leadingWidth: 300.w,
                elevation: 0,
                backgroundColor: Colors.transparent,
                leading: CustomAppbar.leftWidget(context, text: ""),
                title: canStudy ? _buildCollectAndSetting(context, model) : null,
              ),
              backgroundColor: const Color(0xFFEDF6F7),
              body: Stack(
                children: [
                  Container(
                    width: double.infinity,
                    height: 600.w,
                    decoration: const BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage("images/scene_introduce_bg.png"),
                        fit: BoxFit.cover,
                        alignment: Alignment.topCenter,
                        opacity: 1,
                      ),
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsets.only(left: 32.w, right: 32.w, top: 200.w),
                    child: Column(
                      children: [
                        _imageTitleWidget(context, model),
                        SizedBox(height: 16.w),
                        _roleWidget(context),
                        SizedBox(height: 24.w),
                        _senceStepWidget(context),
                      ],
                    ),
                  )
                ],
              ));
        },
      ),
    );
  }

  // 图片标题组件
  Widget _imageTitleWidget(
      BuildContext context, SenceIntroduceProvider provider) {
    final imageUrl = provider.sceneDetailV2Model?.data?.imageUrl ?? "";
    final title = provider.sceneDetailV2Model?.data?.chinese ?? "";
    final usageCount = provider.sceneDetailV2Model?.data?.usageCount ?? 0;
    final courseLevelName = provider.sceneDetailV2Model?.data?.courseLevelName ?? "";
    final target = provider.sceneDetailV2Model?.data?.target ?? "";
    final nickName = provider.sceneDetailV2Model?.data?.creatorNickname ?? "";
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // 圆角图片
        ClipRRect(
          borderRadius: BorderRadius.circular(24.w),
          child: Common_NetImage(imageUrl: imageUrl),
        ),
        SizedBox(height: 24.w),
        Text(
          title,
          style: style_1_32,
        ),
        SizedBox(height: 16.w),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (nickName.isEmpty)
              Common_LevelTag(levelName: courseLevelName)
            else
              Common_InfoTag(text: '@ $nickName'),
            SizedBox(width: 24.w),
            Common_InfoTag(
                icon: Icons.people_outline, text: "$usageCount人使用"),
          ],
        ),
        SizedBox(height: 16.w),
        Text(
          target,
          style: TextStyle(
            color: const Color(0xFF646E70),
            fontSize: 26.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Container _roleWidget(BuildContext context) {
    var sceneDetailV2Model =
        context.read<SenceIntroduceProvider>().sceneDetailV2Model;
    final myRole = sceneDetailV2Model?.data?.myRole ?? "";
    final robotRole = sceneDetailV2Model?.data?.aiRole ?? "";

    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(32.w)),
      child: Row(
        children: [
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                myRole,
                style: style_1_28,
              ),
              Text(
                '你的角色',
                style: style_2_24,
              ),
            ],
          )),
          Container(
            width: 1.w,
            height: 80.w,
            color: ColorUtil.separated,
          ),
          SizedBox(
            width: 32.w,
          ),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                robotRole,
                style: style_1_28,
              ),
              Text(
                '老师的角色',
                style: style_2_24,
              ),
            ],
          )),
        ],
      ),
    );
  }

  Column _senceStepWidget(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 24.w),
        Text('课程练习', style: style_1_32),
        Container(
          margin: EdgeInsets.fromLTRB(8.w, 20.w, 8.w, 8.w),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _senceStep1(context),
                    SizedBox(height: 20.w),
                    _senceStep2(context),
                    SizedBox(height: 20.w),
                    _senceStep3(context),
                    SizedBox(height: 20.w),
                    _senceStep4(context),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _senceStep1(BuildContext context) {
    var topicDetail = context.read<SenceIntroduceProvider>().sceneDetailV2Model;
    String status = "";
    if (topicDetail?.data?.learningStatus == 1) {
      status = "进行中";
    } else if (topicDetail?.data?.learningStatus == 2) {
      status = "已完成";
    }
    return Container(
      height: 144.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(24.w)),
        color: const Color(0xFFFFFFFF),
      ),
      child: Row(
        children: [
          secneStepLeftIntroduceWidget(
            icon: "images/kecheng_icon1.png",
            title: "词汇练习",
            desc: "常用词汇句子练习",
            status: status
          ),
          const Spacer(),
          secneStepRightStartWidget(
            context,
            onTap: () {
              Navigator.of(context)
                  .pushNamed("/vocabulary_practice", arguments: {
                "topic_code": topicDetail?.data?.topicCode,
                "topic_name": topicDetail?.data?.chinese
              });
            },
            btnText: '开始学习',
          ),
        ],
      ),
    );
  }

  Widget _senceStep2(BuildContext context) {
    var topicDetail = context.read<SenceIntroduceProvider>().sceneDetailV2Model;
    return Container(
      height: 144.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(24.w)),
        color: const Color(0xFFFFFFFF),
      ),
      child: Row(
        children: [
          secneStepLeftIntroduceWidget(
            icon: "images/kecheng_icon2.png",
            title: "场景教学",
            desc: "场景对话教学引导",
            status: ""
          ),
          const Spacer(),
          secneStepRightContinueWidget(
            onTap: () {
              Navigator.of(context)
                  .pushNamed('/free_talk',
                      arguments: TalkArgument(
                          model: 1,
                          topicDetailModel: topicDetail,
                          topic: topicDetail!.data!.topicCode!))
                      .then((value) {});
            },
            isVisible: topicDetail?.data?.lastTeachConversationId != null &&
                (topicDetail?.data?.lastTeachConversationId != 0),
          ),
          secneStepRightStartWidget(
            context,
            onTap: () {
              Navigator.of(context)
                  .pushNamed('/free_talk',
                      arguments: TalkArgument(
                          model: 1,
                          topicDetailModel: topicDetail,
                          topic: topicDetail!.data!.topicCode!))
                      .then((value) {});
            },
            btnText: '新教学',
          ),
        ],
      ),
    );
  }

  Widget _senceStep3(BuildContext context) {
    var topicDetail = context.read<SenceIntroduceProvider>().sceneDetailV2Model;

    return Container(
      height: 144.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(24.w)),
        color: const Color(0xFFFFFFFF),
      ),
      child: Row(
        children: [
          secneStepLeftIntroduceWidget(
            icon: "images/kecheng_icon3.png",
            title: "实战演练",
            desc: "场景实战对话练习",
            status: ""
          ),
          const Spacer(),
          secneStepRightContinueWidget(
            onTap: () {
              Navigator.of(context)
                    .pushNamed('/free_talk',
                        arguments: TalkArgument(
                            topic: topicDetail?.data?.topicCode ?? "",
                            model: 1,
                            topicDetailModel: topicDetail))
                    .then((value) {});
            },
            isVisible: topicDetail?.data?.lastConversationId != null &&
                (topicDetail?.data?.lastConversationId != 0)
          ),
          secneStepRightStartWidget(
            context,
            onTap: () {
              Navigator.of(context)
                  .pushNamed('/free_talk',
                      arguments: TalkArgument(
                          model: 0,
                          topicDetailModel: topicDetail,
                          topic: topicDetail!.data!.topicCode!))
                  .then((value) {});
            },
            btnText: '新实战',
          ),
        ],
      ),
    );
  }

  Widget _senceStep4(BuildContext context) {
    var topicDetail = context.read<SenceIntroduceProvider>().sceneDetailV2Model;

    return Container(
      height: 144.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(24.w)),
        color: const Color(0xFFFFFFFF),
      ),
      child: Row(
        children: [
          secneStepLeftIntroduceWidget(
            icon: "images/kecheng_icon4.png",
            title: "对话报告",
            desc: "参与实战后获得评估报告",
            status: ""
          ),
          const Spacer(),
          Opacity(
            opacity: (topicDetail?.data?.hasReport == true) ? 1 : 0.4,
            child: secneStepRightStartWidget(
              context,
              onTap: () {
                if (topicDetail?.data?.hasReport == true) {
                  //对话报告
                  Navigator.push(
                      context,
                      MaterialPageRoute<void>(
                          builder: (_) => ChatReportPage(
                                conversationId:
                                    topicDetail!.data!.reportConversationId!,
                                type: ReportDetailType.view,
                              )));
                }
              },
              btnText: '查看报告',
            )
          ),
        ],
      ),
    );
  }

  Widget _buildCollectAndSetting(
      BuildContext context, SenceIntroduceProvider provider) {
    final isCollected = provider.sceneDetailV2Model?.data?.hasCollect == 1;
    final isCreator = provider.sceneDetailV2Model?.data?.isCreator == 1;
    final topicCode = provider.sceneDetailV2Model?.data?.topicCode;
    final status = provider.sceneDetailV2Model?.data?.status;
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // 一个收藏icon
        TextButton.icon(
          onPressed: () {
            provider.collect();
          },
          label: Text(isCollected ? '已收藏' : '收藏', style: style_2_28),
          icon: isCollected
              ? Icon(
                  Icons.favorite,
                  color: const Color.fromARGB(255, 242, 14, 14),
                  size: 44.w,
                )
              : Image.asset("images/heart-add-2-line.png",
                  width: 44.w, height: 44.w),
        ),
        // 根据isOwner判断是否显示什么icon
        // 如果isCreator为1并且状态为已通过，则显示编辑和删除icon
        MoreSettingWidget(
            items: isCreator && (status == 2 || status == 3)
                ? [
                    MoreSettingItem(
                        icon: Icons.edit_outlined,
                        title: '编辑场景',
                        onTap: () {
                          Navigator.of(context)
                              .push(CreateCustomScenePage.route(topicCode));
                        }),
                    MoreSettingItem(
                        icon: Icons.delete_outlined,
                        color: Colors.red,
                        title: '删除场景',
                        onTap: () {
                          showDeleteDialog(context, provider);
                        }),
                  ]
                : [
                    // 一个举报icon
                    MoreSettingItem(
                        icon: Icons.info_outline,
                        color: Colors.red,
                        title: '举报',
                        onTap: () {
                          showReportDialog(context, provider);
                        })
                  ]),
      ],
    );
  }
}

/// 练习步骤的左边的介绍组件
Widget secneStepLeftIntroduceWidget({String? icon, String? title, String? desc, String? status}) {
  return Row(
    children: [
      Padding(
        padding: EdgeInsets.only(left: 24.w, right: 24.w),
        child: Image.asset(
          icon ?? "",
          width: 64.w,
          height: 64.w,
        ),
      ),
      Padding(
        padding: EdgeInsets.only(top: 24.w, bottom: 24.w),
        child: Text.rich(TextSpan(children: [
          WidgetSpan(
            alignment: PlaceholderAlignment.middle,
            child: Container(
              padding: EdgeInsets.only(right: 8.w, top: 8.w, bottom: 8.w),
              child: Text(
                title ?? "",
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 28.sp,
                  color: ColorUtil.black1,
                ),
              ),
            ),
          ),
          WidgetSpan(
            alignment: PlaceholderAlignment.middle,
            child: Visibility(
                  visible: status?.isNotEmpty ?? false,
                  child: Container(
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      color: const Color.fromRGBO(65, 192, 255, 0.12),
                      borderRadius: BorderRadius.all(Radius.circular(12.w)),
                    ),
                    child: Text(
                      status ?? "",
                      style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.w400,
                          color: const Color(0xFF2693FF)),
                    ),
                  ),
                ),
              ),
              TextSpan(
                  text: "\n$desc",
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 24.sp,
                    color: ColorUtil.black2)),
            ])),
          ),
    ],
  );
}
  Widget secneStepRightStartWidget(BuildContext context, {void Function()? onTap, String? btnText}) {
    // 获取当前是否可以学习
    final canStudy = context.read<SenceIntroduceProvider>().canStudy;
    return Opacity(
      opacity: canStudy ? 1 : 0.4,
      child: Container(
        padding: EdgeInsets.all(8.w),
        margin: EdgeInsets.only(right: 32.w),
        width: 144.w,
        height: 56.w,
        decoration: BoxDecoration(
          color: const Color(0xFF2693FF),
          borderRadius: BorderRadius.all(Radius.circular(12.w)),
        ),
        child: GestureDetector(
          onTap: canStudy ? (onTap ?? () {}) : null,
          child: Center(
            child: Text(
              btnText ?? "",
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.w400,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 练习步骤的右边组件的 继续上次按钮
  Widget secneStepRightContinueWidget({void Function()? onTap, bool isVisible = false}) {
    return Visibility(
            visible: false, // app 中暂时隐藏 TODO: 后续需要打开
            child: GestureDetector(
              onTap: onTap ?? () {},
              child: Padding(
                padding: EdgeInsets.only(right: 12.w),
                child: Text(
                  '继续上次',
                  style: TextStyle(
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w400,
                    color: ColorUtil.blue),
                ),
              ),
      ),
    );
  }

// 显示举报弹窗
void showReportDialog(BuildContext context, SenceIntroduceProvider provider) {
  final _controller = TextEditingController(text: provider.reportDescription);
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (BuildContext context) {
      return ChangeNotifierProvider.value(
          value: provider,
          child:
              Consumer<SenceIntroduceProvider>(builder: (context, provider, _) {
            return Padding(
              padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
              child: SingleChildScrollView(
                child: Container(
                  padding: EdgeInsets.only(
                      left: 40.w, right: 40.w, top: 24.w, bottom: 50.w),
                  decoration: BoxDecoration(
                    color: const Color(0xFFEDF6F7),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(24.w),
                        topRight: Radius.circular(24.w)),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text('举报', style: style_1_36),
                          const Spacer(),
                          GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                            child: Padding(
                              padding: EdgeInsets.only(top: 24.w, bottom: 24.w, left: 24.w),
                              child: Image.asset('images/dialog_close.png',
                                  width: 24.w, height: 24.w),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 40.w),
                      Column(
                        children: [
                          Wrap(
                              spacing: 16.w,
                              runSpacing: 16.w,
                              alignment: WrapAlignment.center,
                              runAlignment: WrapAlignment.start,
                              children: provider.reportReasonList.map(
                                (reason) => GestureDetector(
                                  onTap: () {
                                    provider.selectReportReason(reason);
                                  },
                                  child: Common_DialogSelectionItemWidget(
                                    text: reason,
                                    fontColor: reason == provider.reportReason
                                        ? const Color(0xff2693FF)
                                        : const Color(0xff272733),
                                    borderColor: reason == provider.reportReason
                                        ? const Color(0xff2693FF)
                                        : const Color(0xFFEDF6F7),
                                  ),
                                ),
                              ).toList()),
                          SizedBox(height: 16.w),
                          Container(
                            padding: EdgeInsets.only(left: 16.w, right: 16.w),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16.w),
                            ),
                            child: TextField(
                                controller: _controller,
                                onChanged: (value) {
                                  provider.reportDescription = value;
                                },
                                maxLines: 4,
                                decoration: InputDecoration(
                                  border: InputBorder.none, // 没有边框样式
                                  hintText: "请输入具体原因",
                                  hintStyle: style_2_28,
                                ),
                                style: style_1_28_500),
                          ),
                          SizedBox(height: 24.w),
                          Common_BottomConfirmButton(
                            text: '确认',
                            textColor: Colors.white,
                            bgColor: const Color(0xFF2693FF),
                            splashColor: const Color(0xFF2693FF),
                            onTap: () {
                              provider.report();
                              Navigator.of(context).pop();
                            },
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ),
            );
          }));
    },
  );
}

// 一个 tab 选择组件
Widget containerTabBgWhite(
    String text, Color fontColor, Color borderColor, double? width) {
  return Container(
    width: width,
    alignment: Alignment.center,
    padding: EdgeInsets.all(24.w),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(24.w),
      border: Border.all(
        color: borderColor,
        width: 1.w,
      ),
    ),
    child: Text(text, style: style_1_28_500.copyWith(color: fontColor)),
  );
}

// 显示删除弹窗
void showDeleteDialog(BuildContext context, SenceIntroduceProvider provider) {
  final operationList = [
    {
      'title': '取消',
      'onTap': () {
        Navigator.of(context).pop();
      },
      'color': const Color(0xff272733),
    },
    {
      'title': '删除',
      'onTap': () {
        provider.delete(context);
      },
      'color': Colors.red,
    },
  ];
  showDialog(
    context: context,
    barrierDismissible: false, // 禁止点击外部关闭对话框
    builder: (BuildContext context) {
      return AlertDialog(
        backgroundColor: Colors.white,
        contentPadding: EdgeInsets.all(32.w),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('确定删除该场景吗？', style: style_1_28),
            SizedBox(height: 48.w),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: operationList
                  .map((e) => InkWell(
                        onTap: e['onTap'] as GestureTapCallback,
                        child: Container(
                          padding: EdgeInsets.only(
                              left: 74.w, right: 74.w, top: 20.w, bottom: 20.w),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.w),
                            border: Border.all(
                              color: e['color'] as Color,
                            ),
                          ),
                          child: Text(e['title'] as String,
                              style: TextStyle(
                                fontSize: 28.sp,
                                fontWeight: FontWeight.w600,
                                color: e['color'] as Color,
                              )),
                        ),
                      ))
                  .toList(),
            )
          ],
        ),
      );
    },
  );
}

