import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/scene_topics_vocabulary_model/list.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/vocabulary_partice_provider.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_app_kouyu/widgets/selected_text.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gif_view/gif_view.dart';
import 'package:provider/provider.dart';

import '../../../common/http/models/sence_topic_sentence/list.dart';

class VocabularyPracticeCardWidget extends StatelessWidget {
  const VocabularyPracticeCardWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<VocabularyPracticeProvider>(
      builder: (context, model, child) {
        int index =
            model.type == 1 ? model.vocabularyIndex : model.sentenceIndex;
        int total = model.type == 1
            ? model.sceneTopicsVocabularyModel?.data?.listSize ?? 0
            : model.senceTopicSentenceModel?.data?.listSize ?? 0;
        int currentId = model.type == 1
            ? model.sceneTopicsVocabularyModel!.data!.list![index].id!
            : model.senceTopicSentenceModel!.data!.list![index].id!;
        return SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: Column(
            children: [
              Expanded(
                child: PageView.builder(
                    key: Key("$index"),
                    controller: PageController(
                        viewportFraction: 0.9,
                        initialPage: index,
                        keepPage: false),
                    itemBuilder: (context, index) {
                      if (model.type == 1) {
                        return _vocabularyCardItem(
                            index,
                            total,
                            model
                                .sceneTopicsVocabularyModel!.data!.list![index],
                            context);
                      } else {
                        return _sentenceItemWidget(
                            index,
                            total,
                            model.senceTopicSentenceModel!.data!.list![index],
                            context);
                      }
                    },
                    onPageChanged: (value) {
                      if (model.type == 1) {
                        model.jumpToVocabularyCard(value);
                      } else {
                        model.jumpToSentenceCard(value);
                      }
                    },
                    itemCount: total),
              ),
              SizedBox(
                height: 188.w,
                width: double.infinity,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        model.jumpNext();
                      },
                      child: Container(
                        width: 307.w,
                        height: 96.w,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.w),
                            color: Colors.transparent,
                            border: Border.all(width: 2.w, color: color_1)),
                        child: Center(
                          child: Text(
                            "下次学",
                            style: style_1_32,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 16.w,
                    ),
                    GestureDetector(
                      onTap: () {
                        model.learnedVocabularySuccess(currentId);
                      },
                      child: Container(
                        width: 307.w,
                        height: 96.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24.w),
                          color: const Color(0xFF2693FF),
                        ),
                        child: Center(
                          child: Text(
                            "已学会",
                            style: style_1_32.copyWith(color: Colors.white),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        );
      },
    );
  }

  Container _vocabularyCardItem(
      int index, int total, Vocabulary vocabulary, BuildContext context) {
    VocabularyPracticeProvider model =
        context.read<VocabularyPracticeProvider>();

    String? definition = vocabulary.definition;
    List<String> definitionList = [];
    if (definition != null) {
      definitionList = definition.split("\n");
    }
    return Container(
        height: double.infinity,
        margin: EdgeInsets.only(left: 16.w, right: 16.w),
        padding: EdgeInsets.all(40.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(49.w),
          color: Colors.white,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              children: [
                Text(
                  "${index + 1}/$total",
                  style: style_2_28,
                ),
                SizedBox(
                  width: 16.w,
                ),
                Container(
                  width: 92.w,
                  height: 40.w,
                  decoration: BoxDecoration(
                      color: btnColor,
                      borderRadius: BorderRadius.circular(16.w)),
                  child: Center(
                      child: Text(
                    vocabulary.learned == true ? "已学会" : "未学习",
                    style: style_1_20,
                  )),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: () {
                    if (vocabulary.collected == true) {
                      model.cancelCollectVocabulary(vocabulary);
                    } else {
                      model.collectVocabulary(vocabulary);
                    }
                  },
                  child: Container(
                    width: 104.w,
                    height: 40.w,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.w),
                        border: Border.all(width: 1.w, color: separated)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          vocabulary.collected == true
                              ? "images/start_yellow.png"
                              : "images/start_blue.png",
                          width: 24.w,
                          height: 24.w,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Text(
                          "收藏",
                          style: style_2_24,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 36.w,
            ),
            Container(
              decoration: BoxDecoration(gradient: ColorUtil.blueToGreen),
            ),
            Text(
              vocabulary.vocabulary ?? "_",
              style: TextStyle(
                  fontSize: 48.sp, fontWeight: FontWeight.w700, color: color_1),
            ),
            SizedBox(
              height: 32.w,
            ),
            _voiceWidget("英", vocabulary.ukPhonetic ?? "_",
                vocabulary.ukPronunciationUrl ?? "_"),
            SizedBox(
              height: 20.w,
            ),
            _voiceWidget("美", vocabulary.usPhonetic ?? "_",
                vocabulary.usPronunciationUrl ?? "_"),
            SizedBox(
              height: 32.w,
            ),
            Container(
              height: 1.w,
              width: double.infinity,
              color: separated,
            ),
            SizedBox(
              height: 32.w,
            ),
            Align(
              alignment: Alignment.topLeft,
              child: Text(
                "释义",
                style: style_2_24,
              ),
            ),
            SizedBox(
              height: 16.w,
            ),
            for (int i = 0; i < definitionList.length; i++)
              _definitionWidget(definitionList[i]),
            const Spacer(),
            model.voiceState == VoiceStateEnum.init
                ? _followWidget(context, vocabulary.defaultPronunciationUrl,
                    vocabulary.score, vocabulary.userPronunciationUrl)
                : _speakingWidget(context),
          ],
        ));
  }

  Widget _definitionWidget(String definition) {
    List<String> list = definition.split(". ");
    if (list.length < 2) {
      return Container();
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: [
        SizedBox(
          width: 68.w,
          child: Align(
            alignment: Alignment.topLeft,
            child: Text(
              "${list.first}.",
              style: style_3_28,
            ),
          ),
        ),
        Expanded(
          child: Text(
            list.last,
            style: style_1_28,
          ),
        ),
      ],
    );
  }

  Container _sentenceItemWidget(
      int index, int total, Sentence sentence, BuildContext context) {
    VocabularyPracticeProvider model =
        context.read<VocabularyPracticeProvider>();

    return Container(
        height: double.infinity,
        margin: EdgeInsets.only(left: 16.w, right: 16.w),
        padding: EdgeInsets.all(40.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(49.w),
          color: Colors.white,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: ListView(
                shrinkWrap: true,
                children: [
                  Row(
                    children: [
                      Text(
                        "${index + 1}/$total",
                        style: style_2_28,
                      ),
                      SizedBox(
                        width: 16.w,
                      ),
                      Container(
                        width: 92.w,
                        height: 40.w,
                        decoration: BoxDecoration(
                            color: btnColor,
                            borderRadius: BorderRadius.circular(16.w)),
                        child: Center(
                            child: Text(
                          sentence.learned == true ? "已学会" : "未学习",
                          style: style_1_20,
                        )),
                      ),
                      const Spacer(),
                      GestureDetector(
                        onTap: () {
                          if (sentence.collected == true) {
                            model.cancelCollectionSentence(sentence);
                          } else {
                            model.collectionSentence(sentence);
                          }
                        },
                        child: Container(
                          width: 104.w,
                          height: 40.w,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16.w),
                              border: Border.all(width: 1.w, color: separated)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                sentence.collected == true
                                    ? "images/start_yellow.png"
                                    : "images/start_blue.png",
                                width: 24.w,
                                height: 24.w,
                              ),
                              SizedBox(
                                width: 8.w,
                              ),
                              Text(
                                "收藏",
                                style: style_2_24,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 36.w,
                  ),
                  SelectableTextUtil.editableText(
                    context,
                    text: sentence.sentence ?? "_",
                    style: TextStyle(
                        fontSize: 32.sp,
                        fontWeight: FontWeight.w700,
                        color: color_1),
                  ),
                  SizedBox(
                    height: 16.w,
                  ),
                  Text(
                    sentence.chineseMeaning ?? "_",
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w400,
                        color: color_2),
                  ),
                  SizedBox(
                    height: 32.w,
                  ),
                  Container(
                    height: 1.w,
                    width: double.infinity,
                    color: separated,
                  ),
                  SizedBox(
                    height: 32.w,
                  ),
                  Align(
                    alignment: Alignment.topLeft,
                    child: Text(
                      "重点词汇",
                      style: style_2_24,
                    ),
                  ),
                  SizedBox(
                    height: 16.w,
                  ),
                  Wrap(
                    spacing: 16.w, // 单词间的水平间距
                    runSpacing: 16.w, // 行间距
                    alignment: WrapAlignment.start, // 左对齐
                    children: (sentence.vocabularies ?? []).map((vocabulary) {
                      return GestureDetector(
                        onTap: () {
                          context
                              .read<VocabularyPracticeProvider>()
                              .findAndJump(vocabulary);
                        },
                        child: Container(
                          padding: EdgeInsets.fromLTRB(24.w, 16.w, 24.w, 16.w),
                          decoration: BoxDecoration(
                            color: btnColor,
                            borderRadius: BorderRadius.circular(24.w),
                          ),
                          child: Text(
                            vocabulary,
                            style: style_1_28,
                          ),
                        ),
                      );
                    }).toList(),
                  )
                ],
              ),
            ),
            model.voiceState == VoiceStateEnum.init
                ? _followWidget(context, sentence.defaultPronunciationUrl,
                    sentence.score, sentence.userPronunciationUrl)
                : _speakingWidget(context),
          ],
        ));
  }

  Row _speakingWidget(BuildContext context) {
    VocabularyPracticeProvider model =
        context.read<VocabularyPracticeProvider>();

    return Row(
      children: [
        GestureDetector(
          onTap: () {
            model.stop();
          },
          child: Image(
            image: const AssetImage("images/chat_close.png"),
            width: 80.w,
            height: 80.w,
          ),
        ),
        SizedBox(
          width: 40.w,
        ),
        Expanded(
          child: GifView.asset(
            "images/chat_loading.gif",
            fit: BoxFit.cover,
            width: double.infinity,
            height: 48.w,
            repeat: ImageRepeat.repeat,
          ),
        ),
        SizedBox(
          width: 40.w,
        ),
        GestureDetector(
          onTap: () {
            model.sent();
          },
          child: Image(
            image: const AssetImage("images/chat_send.png"),
            width: 80.w,
            height: 80.w,
          ),
        ),
      ],
    );
  }

  Row _followWidget(
      BuildContext context, String? url, String? score, String? userUrl) {
    VocabularyPracticeProvider model =
        context.read<VocabularyPracticeProvider>();

    return Row(
      children: [
        GestureDetector(
            onTap: () {
              if (CommonUtils.isPlaying == true && CommonUtils.url == url) {
                CommonUtils.stopPlay();
              } else {
                CommonUtils.playVideo(url);
              }
            },
            child: StreamBuilder<PlayState>(
                stream: CommonUtils.playStatus,
                builder: (context, snapshot) {
                  if (CommonUtils.url != url || CommonUtils.isPlaying != true) {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image(
                          image: const AssetImage("images/bt_play.png"),
                          width: 80.w,
                          height: 80.w,
                        ),
                        SizedBox(height: 4.w),
                        Text(
                          "AI发音",
                          style: style_1_24,
                        ),
                      ],
                    );
                  } else {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image(
                          image: const AssetImage("images/btn_pause.png"),
                          width: 80.w,
                          height: 80.w,
                        ),
                        SizedBox(height: 4.w),
                        Text(
                          "AI发音",
                          style: style_1_24,
                        ),
                      ],
                    );
                  }
                })),
        SizedBox(
          width: 40.w,
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              model.speakEn();
            },
            child: Container(
              height: 88.w,
              width: double.infinity,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.w),
                  color: const Color(0xFF2693FF)),
              child: Center(
                child: Text(
                  "点击跟读",
                  style: style_1_32.copyWith(color: Colors.white),
                ),
              ),
            ),
          ),
        ),
        SizedBox(
          width: 40.w,
        ),
        Column(
          children: [
            GestureDetector(
              onTap: () {
                if (userUrl != null) {
                  CommonUtils.playVideo(userUrl);
                }
              },
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(40.w), color: btnColor),
                width: 80.w,
                height: 80.w,
                child: Center(
                    child: Text(
                  score ?? "",
                  style: TextStyle(
                      color: getColor(score ?? "0"),
                      fontSize: 28.sp,
                      fontWeight: FontWeight.w700),
                )),
              ),
            ),
            SizedBox(height: 4.w),
            Text(
              "我的发音",
              style: style_1_24,
            ),
          ],
        ),
      ],
    );
  }

  Color getColor(String scoreStr) {
    int score = 0;
    try {
      score = int.parse(scoreStr);
    } catch (_) {
      score = 0;
    }
    if (score <= 60) {
      return const Color.fromARGB(255, 249, 50, 0);
    } else if (score <= 80) {
      return const Color(0xFF061B1F);
    } else if (score <= 100) {
      return const Color.fromARGB(255, 42, 201, 92);
    }

    return const Color(0xFF061B1F);
  }

  Container _voiceWidget(String type, String content, String url) {
    return Container(
      height: 56.w,
      decoration: BoxDecoration(
          color: btnColor, borderRadius: BorderRadius.circular(40.w)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 24.w,
          ),
          Text(
            type,
            style: style_2_24,
          ),
          Text(
            "/",
            style: style_3_24,
          ),
          Text(
            content,
            style: style_2_24,
          ),
          Text(
            "/",
            style: style_3_24,
          ),
          PlayButtonWidget(
            style: PlayButtonStyle.blue,
            audioUrl: url,
          ),
        ],
      ),
    );
  }
}
