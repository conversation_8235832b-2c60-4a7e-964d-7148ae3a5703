import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/change_role_cubit.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:gif_view/gif_view.dart';

class ChangeTeacherPage extends StatefulWidget {
  const ChangeTeacherPage({super.key});
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const ChangeTeacherPage());
  }

  @override
  State<ChangeTeacherPage> createState() => _ChangeTeacherPageState();
}

class _ChangeTeacherPageState extends State<ChangeTeacherPage> {
  int type = 0;
  String? feedback;
  String? phone;
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ChangeRoleCubit()..getRoleList(),
      child: BlocBuilder<ChangeRoleCubit, ChangeRoleState>(
        builder: (context, state) {
          return Scaffold(
              appBar: AppBar(
                leadingWidth: 300,
                backgroundColor: const Color(0xFFF3FBFD),
                leading: CustomAppbar.leftWidget(context, text: "切换语伴"),
              ),
              backgroundColor: const Color(0xFFF3FBFD),
              floatingActionButtonLocation:
                  FloatingActionButtonLocation.centerDocked,
              floatingActionButton: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Container(
                  height: 52,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: const Color(0xFF2693FF),
                  ),
                  child: TextButton(
                      onPressed: () {
                        context
                            .read<ChangeRoleCubit>()
                            .setRole(state.selectdData?.id?.toInt())
                            .then((success) {
                          if (success) {
                            EasyLoading.showSuccess("设置成功");
                            Navigator.pop(context);
                          }
                        });
                      },
                      style: const ButtonStyle(
                        textStyle: MaterialStatePropertyAll(TextStyle(
                            fontSize: 20, fontWeight: FontWeight.w700)),
                      ),
                      child: const Center(
                        child: Text("确认选择",
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.w800)),
                      )),
                ),
              ),
              body: Padding(
                padding: const EdgeInsets.fromLTRB(20, 20, 20, 64),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 340,
                      child: Swiper(
                        itemBuilder: (BuildContext context, int index) {
                          var data = state.roleListModel?.data?[index];
                          return ClipRRect(
                            borderRadius: const BorderRadius.all(
                              Radius.circular(20),
                            ),
                            child: Stack(
                              alignment: AlignmentDirectional.topCenter,
                              children: [
                                Image.network(
                                  width: double.infinity,
                                  data?.bgImageUrl ?? " ",
                                  fit: BoxFit.fitWidth,
                                ),
                                // Padding(
                                //   padding: const EdgeInsets.only(top: 40.0),
                                //   child: Image.network(
                                //     data?.imageUrl ?? " ",
                                //     fit: BoxFit.fitHeight,
                                //   ),
                                // ),
                              ],
                            ),
                          );
                        },
                        onIndexChanged: (index) {
                          context
                              .read<ChangeRoleCubit>()
                              .setSelectedModel(index);
                        },
                        itemCount: state.roleListModel?.data?.length ?? 0,
                        outer: true,
                        viewportFraction: 0.7,
                        scale: 0.9,
                        index: state.index,
                        loop: false,
                        curve: Curves.linearToEaseOut,
                        pagination: const SwiperPagination(
                            builder: DotSwiperPaginationBuilder(
                                size: 4,
                                activeSize: 4,
                                color: Color(0xFF8BAFB6),
                                activeColor: Color(0xFF061B1F))),
                        control: null,
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Text(state.selectdData?.name ?? "_",
                        style: const TextStyle(
                            color: Color(0xFF061B1F),
                            fontSize: 20,
                            fontWeight: FontWeight.w700)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.fromLTRB(6, 4, 6, 4),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: const Color(0xFFDDEDF0),
                              width: 0.5,
                            ),
                          ),
                          child: Text(
                              state.selectdData?.englishVariation ?? "_",
                              style: const TextStyle(
                                  color: Color(0xFF061B1F),
                                  fontSize: 11,
                                  fontWeight: FontWeight.w400)),
                        ),
                        const SizedBox(
                          width: 8,
                        ),
                        Container(
                          padding: const EdgeInsets.fromLTRB(6, 4, 6, 4),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: const Color(0xFFDDEDF0),
                              width: 0.5,
                            ),
                          ),
                          child: Text(
                              "${state.selectdData?.numberOfUsers ?? "_"}人使用",
                              style: const TextStyle(
                                  color: Color(0xFF061B1F),
                                  fontSize: 11,
                                  fontWeight: FontWeight.w400)),
                        ),
                        const SizedBox(
                          width: 8,
                        ),
                        Visibility(
                          visible: state.selectdData == state.currentData,
                          child: Container(
                            padding: const EdgeInsets.fromLTRB(6, 4, 6, 4),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: const Color(0xFF41C0FF),
                            ),
                            child: const Text("使用中",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 11,
                                    fontWeight: FontWeight.w400)),
                          ),
                        )
                      ],
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Text(state.selectdData?.greetingCn ?? "_",
                        style: const TextStyle(
                            color: Color(0xFF646E70),
                            fontSize: 12,
                            fontWeight: FontWeight.w400)),
                    const SizedBox(
                      width: 8,
                    ),
                    GestureDetector(
                      onTap: () {
                        CommonUtils.playVideo(state.selectdData?.audioUrl);
                      },
                      child: Container(
                        padding: const EdgeInsets.fromLTRB(12, 6, 12, 6),
                        margin: const EdgeInsets.only(top: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.white,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Text("查看发音",
                                style: TextStyle(
                                    color: Color(0xFF061B1F),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400)),
                            PlayButtonWidget2(
                              audioUrl: state.selectdData?.audioUrl,
                            )
                          ],
                        ),
                      ),
                    ),
                    const Spacer(),
                    const SizedBox(
                      height: 46,
                    ),
                  ],
                ),
              ));
        },
      ),
    );
  }
}
