import 'package:flutter/cupertino.dart';
import 'package:flutter_app_kouyu/modules/chat/view/radar_chart_painter.dart';

class Radar<PERSON><PERSON> extends StatelessWidget {
  final int numSides; // 边的数量
  final List<double> values; // 每个角上的数值
  final double maxValue; // 最大值，用于归一化数值

  RadarChart(
      {required this.numSides, required this.values, this.maxValue = 100.0});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: RadarChartPainter(
          numSides: numSides, values: values, maxValue: maxValue),
      child: Container(),
    );
  }
}
