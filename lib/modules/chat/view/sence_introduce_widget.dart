import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/custome_scene_model/scene_detail_v2_model.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_report_model.dart';
import 'package:flutter_app_kouyu/modules/chat/view/chat_report_page.dart';
import 'package:flutter_app_kouyu/widgets/common_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SenceIntroduceWidget extends StatelessWidget {
  final SceneDetailV2Model topicDetail;
  final num conversationId;
  final bool canCreateReport;

  const SenceIntroduceWidget(
      {super.key,
      required this.topicDetail,
      required this.conversationId,
      required this.canCreateReport});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          // height: 600.w,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage("images/scene_introduce_bg.png"),
              fit: BoxFit.cover,
              alignment: Alignment.topCenter,
              opacity: 1,
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 32.w, right: 32.w, top: 24.w),
          child: Column(
              children: [
                _titleAndCloseButtonWidget(context),
                SizedBox(height: 24.w),
                _imageTitleWidget(),
                SizedBox(height: 16.w),
                _roleWidget(),
              ],
            ),
        ),
      ],
    );
  }

  /// 弹框标题和关闭按钮
  Widget _titleAndCloseButtonWidget(BuildContext context) {
    return Row(
      children: [
        Text('场景介绍', style: style_1_36),
        const Spacer(),
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            Navigator.of(context).pop();
          },
          child: Padding(
            padding: EdgeInsets.only(top: 24.w, bottom: 24.w, left: 24.w),
            child: Image.asset('images/dialog_close.png',
                width: 24.w, height: 24.w),
          ),
      ),
    ]);
  }

  // 图片标题组件
  Widget _imageTitleWidget() {
    final imageUrl = topicDetail.data?.imageUrl ?? "";
    final title = topicDetail.data?.chinese;
    final usageCount = topicDetail.data?.usageCount;
    final courseLevelName = topicDetail.data?.courseLevelName;
    final target = topicDetail.data?.target;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // 圆角图片
        ClipRRect(
          borderRadius: BorderRadius.circular(24.w),
          child: Image.network(
            imageUrl,
            width: 200.w,
            height: 250.w,
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(height: 24.w),
        Text(
          title ?? "",
          style: style_1_32,
        ),
        SizedBox(height: 16.w),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Common_InfoTag(icon: Icons.person, text: courseLevelName ?? ""),
            SizedBox(width: 24.w),
            Common_InfoTag(
                icon: Icons.people_outline, text: "${usageCount}人使用"),
          ],
        ),
        SizedBox(height: 16.w),
        Text(
          target ?? "",
          style: TextStyle(
            color: const Color(0xFF646E70),
            fontSize: 26.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Container _roleWidget() {
    final myRole = topicDetail.data?.myRole ?? "";
    final robotRole = topicDetail.data?.aiRole ?? "";

    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(32.w)),
      child: Row(
        children: [
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                myRole,
                style: style_1_28,
              ),
              Text(
                '你的角色',
                style: style_2_24,
              ),
            ],
          )),
          Container(
            width: 1.w,
            height: 80.w,
            color: ColorUtil.separated,
          ),
          SizedBox(
            width: 32.w,
          ),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                robotRole,
                style: style_1_28,
              ),
              Text(
                '老师的角色',
                style: style_2_24,
              ),
            ],
          )),
        ],
      ),
    );
  }

  Widget _senceStepWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        canCreateReport ? createReport(context) : Navigator.pop(context);
      },
      child: Container(
          margin: EdgeInsets.only(top: 24.w),
          width: double.infinity,
          height: 100.w,
          decoration: BoxDecoration(
              gradient: ColorUtil.blueToGreen,
              borderRadius: BorderRadius.circular(32.w)),
          child: Center(
            child: Text(
              canCreateReport ? "查看报告" : '我知道了',
              style: TextStyle(
                  fontSize: 36.sp,
                  fontWeight: FontWeight.w500,
                  color: ColorUtil.black1),
            ),
          )),
    );
  }

  createReport(BuildContext context) {
    //查看报告
    Navigator.push(
        context,
        MaterialPageRoute<void>(
            builder: (_) => ChatReportPage(
                  conversationId: conversationId,
                  type: ReportDetailType.create,
                )));
  }
}
