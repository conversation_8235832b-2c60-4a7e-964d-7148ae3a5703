import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/analyze_model/analyze_model.dart';

class AnalyzeWidget extends StatelessWidget {
  final AnalyzeModel? analyzeModel;
  final String? originText;
  const AnalyzeWidget(
      {super.key, required this.analyzeModel, required this.originText});

  @override
  Widget build(BuildContext context) {
    return Container(
        width: double.infinity,
        margin: const EdgeInsetsDirectional.only(start: 20, end: 20, top: 24),
        child: ListView(
          children: [
            Row(
              children: [
                const Text(
                  "语法建议",
                  style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w900,
                      color: Color(0xFF061B1F)),
                ),
                const Spacer(),
                GestureDetector(
                  child: const Icon(Icons.close),
                  onTap: () {
                    Navigator.pop(context);
                  },
                )
              ],
            ),
            const SizedBox(
              height: 4,
            ),
            const Text("你的语法存在一些问题",
                style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF646E70))),
            const SizedBox(
              height: 20,
            ),
            Text(originText ?? "",
                style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF061B1F))),
            const SizedBox(
              height: 40,
            ),
            const Text("建议使用",
                style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF646E70))),
            const SizedBox(
              height: 2,
            ),
            Text(analyzeModel?.data?.correction ?? "",
                style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF061B1F))),
            const SizedBox(
              height: 12,
            ),
            Text(analyzeModel?.data?.explanation ?? "",
                style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF061B1F)))
          ],
        ));
  }
}
