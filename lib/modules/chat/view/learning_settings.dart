import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/user_info_model/user_info_model.dart';

import 'package:flutter_app_kouyu/common/style_util.dart';

import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:provider/provider.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/learning_settings_provider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LearningSettingsPage extends StatefulWidget {
  const LearningSettingsPage({super.key});

  @override
  State<LearningSettingsPage> createState() => _LearningSettingsPageState();
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => LearningSettingsPage());
  }
}

class _LearningSettingsPageState extends State<LearningSettingsPage> {
  String? selectEnglishLevel;
  String? selectEnglishLevelCode;
  String? selectLearningStage;
  String? selectLearningStageCode;
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<LearningSettingsProvider>(
        create: (_) => LearningSettingsProvider()..refreshData(),
        child: Consumer<LearningSettingsProvider>(
            builder: ((context, model, child) {
          return Scaffold(
            appBar: AppBar(
              leadingWidth: 300,
              backgroundColor: const Color(0xFFF3FBFD),
              leading: CustomAppbar.leftWidget(context, text: "学习设置"),
              scrolledUnderElevation: 0,
            ),
            body: Container(
              padding: EdgeInsets.fromLTRB(48.w, 40.w, 48.w, 40.w),
              //增加圆角
              // margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
              decoration: const BoxDecoration(
                //增加圆角
                // borderRadius: BorderRadius.all(Radius.circular()),
                color: const Color(0xFFF3FBFD),
              ),
              child: Container(
                decoration: const BoxDecoration(
                  //增加圆角
                  borderRadius: BorderRadius.all(Radius.circular(24)),
                ),
                child: ListView(
                  children: [
                    // 中英文混合教学
                    InkWell(
                      onTap: null,
                      child: Container(
                        height: 144.w,
                        // padding: EdgeInsets.symmetric(
                        //     horizontal: 16.w, vertical: 16.w),
                        //边距
                        padding: EdgeInsets.fromLTRB(32.w, 28.w, 32.w, 28.w),

                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(24.w),
                            topRight: Radius.circular(24.w),
                          ),
                          border: Border(
                            bottom: BorderSide(
                              color: const Color(0xFFEEEEEE),
                              width: 0.5.w,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Padding(
                                padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('中英文混合教学', style: style_1_32_600),
                                    Padding(
                                      padding: EdgeInsets.only(top: 4.w),
                                      child: Text('开启后，在教学中进行中英双语教学',
                                          style: style_4_24_400),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Transform.scale(
                              scale: 1.0,
                              child: model.isLoading 
                                  ? const CircularProgressIndicator(
                                      strokeWidth: 2.0,
                                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2693FF)),
                                    )
                                  : Switch(
                                      value: model.isMixedTeaching == 1,
                                      onChanged: (bool value) {
                                        model.updateMixedTeaching(value ? 1 : 0);
                                      },
                                      activeTrackColor: const Color(0xFF2693FF),
                                      inactiveTrackColor: const Color(0xFFCEDDE0),
                                      inactiveThumbColor: Colors.white,
                                      trackOutlineColor: MaterialStateProperty.resolveWith<Color>(
                                        (Set<MaterialState> states) => const Color(0xFFCEDDE0),
                                      ),
                                    ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const CommonDivider(),

                    // 语速设置
                    SettingItem(
                      '语速设置',
                      model.speakingSpeed == 0.6
                          ? "较慢 (0.6x)"
                          : model.speakingSpeed == 0.8
                              ? "稍慢 (0.8x)"
                              : model.speakingSpeed == 1
                                  ? "正常 (1.0x)"
                                  : model.speakingSpeed == 1.2
                                      ? "较快 (1.2x)"
                                      : "",
                      () {
                        showSpeedSettingsModalBottomSheet(context, model);
                      },
                    ),
                    const CommonDivider(),

                    // 英语水平
                    SettingItem(
                      '英语水平',
                      model.englishLevelName ?? '',
                      () {
                        showEnglishLevelModalBottomSheet(context, model);
                      },
                    ),
                    const CommonDivider(),

                    // 学习目的
                    SettingItem(
                      '学习目的',
                      model.firstLearningPurposeName,
                      () {
                        showLearningPurposeModalBottomSheet(context, model);
                      },
                    ),
                    const CommonDivider(),

                    // 学习阶段
                    // SettingItem(
                    //   title: '学习阶段',
                    //   settingValue: model.learningStage,
                    //   onTap: () {
                    //     showLearningStageModalBottomSheet(context, model);
                    //   },
                    // ),
                    InkWell(
                      onTap: () {
                        showLearningStageModalBottomSheet(context, model);
                      },
                      child: Container(
                        height: 104.w,
                        padding: EdgeInsets.fromLTRB(32.w, 28.w, 32.w, 28.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(24.w),
                            bottomRight: Radius.circular(24.w),
                          ),
                          color: Colors.white,
                          border: Border(
                            bottom: BorderSide(
                              color: const Color(0xFFEEEEEE),
                              width: 0.5.w,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('学习阶段', style: style_1_32_600),
                                ],
                              ),
                            ),
                            Text(model.learningStageName ?? "",
                                style: style_4_28_400),
                            SizedBox(width: 8.w),
                            Image.asset(
                              "images/arrow_right_black.png",
                              width: 20.w,
                              height: 20.w,
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          );
        })));
  }

  Future showEnglishLevelModalBottomSheet(
      BuildContext context, LearningSettingsProvider model) {
    // 初始化选中值
    selectEnglishLevelCode = model.englishLevel;

    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.85,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24.w),
                  topRight: Radius.circular(24.w),
                ),
              ),
              child: Stack(
                children: [
                  Positioned(
                    top: 40.w,
                    right: 48.w,
                    child: GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Image.asset(
                        "images/close_black.png",
                        width: 24.w,
                        height: 24.w,
                      ),
                    ),
                  ),
                  Column(
                    children: [
                      // 顶部栏，包含拖动条和关闭按钮
                      Container(
                        margin: EdgeInsets.symmetric(vertical: 12.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            // Spacer(),
                            // Padding(
                            //   padding: EdgeInsets.only(top: 20.w),
                            //   child: GestureDetector(
                            //     onTap: () => Navigator.pop(context),
                            //     child: Image.asset(
                            //       "images/close_black.png",
                            //       width: 24.w,
                            //       height: 24.w,
                            //     ),
                            //   ),
                            // ),
                            // SizedBox(width: 48.w),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.fromLTRB(48.w, 40.w, 48.w, 100.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "你当前的英语水平",
                                style: style_1_40_800,
                              ),
                              SizedBox(height: 4.w),
                              Text(
                                "老师会根据你的英语水平选择合适的词汇和你对话",
                                style: TextStyle(
                                  fontSize: 24.sp,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFF646E70),
                                ),
                              ),
                              SizedBox(height: 20.w),
                              Expanded(
                                child: ListView.separated(
                                  separatorBuilder: (context, index) =>
                                      SizedBox(height: 20.w),
                                  itemCount:
                                      model.englishLevelModel?.data?.length ??
                                          0,
                                  itemBuilder: (context, index) {
                                    final data =
                                        model.englishLevelModel!.data![index];
                                    return GestureDetector(
                                      onTap: () {
                                        setModalState(() {
                                          // 使用 setModalState 而不是 setState
                                          selectEnglishLevel = data.levelCn;
                                          selectEnglishLevelCode = data.levelEn;
                                        });
                                      },
                                      child: Container(
                                        padding: EdgeInsets.all(32.w),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                              color: const Color(0xFFCEDDE0)),
                                        ),
                                        child: Row(
                                          children: [
                                            Image.network(
                                              data.icon ?? "",
                                              width: 40,
                                              height: 40,
                                              fit: BoxFit.fill,
                                            ),
                                            SizedBox(width: 8.w),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    data.levelCn ?? "",
                                                    style: style_1_32_600,
                                                  ),
                                                  Text(
                                                    data.description ?? "",
                                                    style: style_4_24_400,
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Image.asset(
                                              selectEnglishLevelCode ==
                                                      data.levelEn
                                                  ? "images/check_circle_blue.png"
                                                  : "images/un_check_circle.png",
                                              width: 18,
                                              height: 18,
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  // 底部确认按钮
                  Positioned(
                    left: 48.w,
                    right: 48.w,
                    bottom: 46.w,
                    child: Container(
                      height: 104.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24.w),
                        color: const Color(0xFF2693FF),
                      ),
                      child: TextButton(
                        onPressed: () {
                          model.updateEnglishLevel(selectEnglishLevelCode ?? "",
                              selectEnglishLevel ?? "");
                          Navigator.pop(context);
                        },
                        child: Text(
                          "确认",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 36.sp,
                            fontWeight: FontWeight.w800,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Future showSpeedSettingsModalBottomSheet(
      BuildContext context, LearningSettingsProvider model) {
    // 初始化选中值
    num selectedSpeed = model.speakingSpeed ?? 0;

    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.85,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24.w),
                  topRight: Radius.circular(24.w),
                ),
              ),
              child: Stack(
                children: [
                  Positioned(
                    top: 40.w,
                    right: 48.w,
                    child: GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Image.asset(
                        "images/close_black.png",
                        width: 24.w,
                        height: 24.w,
                      ),
                    ),
                  ),
                  Column(
                    children: [
                      Container(
                        margin: EdgeInsets.symmetric(vertical: 12.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [],
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.fromLTRB(48.w, 40.w, 48.w, 100.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "语速设置",
                                style: style_1_40_800,
                              ),
                              SizedBox(height: 4.w),
                              Text(
                                "选择适合你的语速，让对话更流畅",
                                style: TextStyle(
                                  fontSize: 24.sp,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFF646E70),
                                ),
                              ),
                              SizedBox(height: 40.w),
                              Expanded(
                                child: ListView.separated(
                                  separatorBuilder: (context, index) =>
                                      SizedBox(height: 20.w),
                                  itemCount: 4,
                                  itemBuilder: (context, index) =>
                                      GestureDetector(
                                          onTap: () {
                                            setModalState(() {
                                              selectedSpeed =
                                                  model.speedList[index];
                                            });
                                          },
                                          child: _buildSpeedOption(
                                            model.speedList[index],
                                            selectedSpeed,
                                          )),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  Positioned(
                    left: 48.w,
                    right: 48.w,
                    bottom: 46.w,
                    child: Container(
                      height: 104.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24.w),
                        color: const Color(0xFF2693FF),
                      ),
                      child: TextButton(
                        onPressed: () {
                          model.updateSpeakingSpeed(selectedSpeed);
                          Navigator.pop(context);
                        },
                        child: Text(
                          "确认",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 36.sp,
                            fontWeight: FontWeight.w800,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildSpeedOption(num speed, num selectedSpeed) {
    return Container(
      padding: EdgeInsets.all(32.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFCEDDE0)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            speed == 0.6
                ? "较慢 (0.6x)"
                : speed == 0.8
                    ? "稍慢 (0.8x)"
                    : speed == 1.0
                        ? "正常 (1.0x)"
                        : "较快 (1.2x)",
            style: style_1_32_600,
          ),
          Image.asset(
            selectedSpeed == speed
                ? "images/check_circle_blue.png"
                : "images/un_check_circle.png",
            width: 18,
            height: 18,
          ),
        ],
      ),
    );
  }

  Future showLearningStageModalBottomSheet(
      BuildContext context, LearningSettingsProvider model) {
    // 初始化选中值
    String? selectLearningStage = model.learningStageName;
    String? selectLearningStageCode = model.learningStage;

    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.85,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24.w),
                  topRight: Radius.circular(24.w),
                ),
              ),
              child: Stack(
                children: [
                  Positioned(
                    top: 40.w,
                    right: 48.w,
                    child: GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Image.asset(
                        "images/close_black.png",
                        width: 24.w,
                        height: 24.w,
                      ),
                    ),
                  ),
                  Column(
                    children: [
                      Container(
                        margin: EdgeInsets.symmetric(vertical: 12.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [],
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.fromLTRB(48.w, 40.w, 48.w, 100.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "你的学习阶段",
                                style: style_1_40_800,
                              ),
                              SizedBox(height: 4.w),
                              Text(
                                "老师会根据你的学习阶段选择合适的教学内容",
                                style: TextStyle(
                                  fontSize: 24.sp,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFF646E70),
                                ),
                              ),
                              SizedBox(height: 20.w),
                              Expanded(
                                child: ListView.separated(
                                  separatorBuilder: (context, index) =>
                                      SizedBox(height: 20.w),
                                  itemCount:
                                      model.learningStageList?.data?.length ??
                                          0,
                                  itemBuilder: (context, index) {
                                    final data =
                                        model.learningStageList?.data?[index];
                                    return GestureDetector(
                                      onTap: () {
                                        setModalState(() {
                                          selectLearningStage = data?.name;
                                          selectLearningStageCode = data?.code;
                                        });
                                      },
                                      child: Container(
                                        padding: EdgeInsets.all(32.w),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                              color: const Color(0xFFCEDDE0)),
                                        ),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    data?.name ?? "",
                                                    style: style_1_32_600,
                                                  )
                                                ],
                                              ),
                                            ),
                                            Image.asset(
                                              selectLearningStage == data?.name
                                                  ? "images/check_circle_blue.png"
                                                  : "images/un_check_circle.png",
                                              width: 18,
                                              height: 18,
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  // 底部确认按钮
                  Positioned(
                    left: 48.w,
                    right: 48.w,
                    bottom: 46.w,
                    child: Container(
                      height: 104.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24.w),
                        color: const Color(0xFF2693FF),
                      ),
                      child: TextButton(
                        onPressed: () {
                          model.updateLearningStage(
                              selectLearningStageCode ?? "",
                              selectLearningStage ?? "");
                          Navigator.pop(context);
                        },
                        child: Text(
                          "确认",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 36.sp,
                            fontWeight: FontWeight.w800,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Future showLearningPurposeModalBottomSheet(
      BuildContext context, LearningSettingsProvider model) {
    // 初始化选中值列表
    List<String> selectedPurposeCodes = model.learningPurposeCode ?? [];

    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.85,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24.w),
                  topRight: Radius.circular(24.w),
                ),
              ),
              child: Stack(
                children: [
                  // 关闭按钮
                  Positioned(
                    top: 40.w,
                    right: 48.w,
                    child: GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Image.asset(
                        "images/close_black.png",
                        width: 24.w,
                        height: 24.w,
                      ),
                    ),
                  ),
                  Column(
                    children: [
                      Container(
                        margin: EdgeInsets.symmetric(vertical: 12.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [],
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.fromLTRB(48.w, 40.w, 48.w, 100.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "你的学习目的",
                                style: style_1_40_800,
                              ),
                              SizedBox(height: 4.w),
                              Text(
                                "老师会根据你的学习目的选择合适的词汇和你对话",
                                style: TextStyle(
                                  fontSize: 24.sp,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFF646E70),
                                ),
                              ),
                              SizedBox(height: 20.w),
                              Expanded(
                                child: ListView.separated(
                                  separatorBuilder: (context, index) =>
                                      SizedBox(height: 20.w),
                                  itemCount:
                                      model.learningPurposeList?.data?.length ??
                                          0,
                                  itemBuilder: (context, index) {
                                    final data =
                                        model.learningPurposeList!.data![index];
                                    return GestureDetector(
                                      onTap: () {
                                        setModalState(() {
                                          if (selectedPurposeCodes
                                              .contains(data.code)) {
                                            selectedPurposeCodes
                                                .remove(data.code);
                                          } else {
                                            selectedPurposeCodes
                                                .add(data.code!);
                                          }
                                        });
                                      },
                                      child: Container(
                                        padding: EdgeInsets.all(32.w),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                              color: const Color(0xFFCEDDE0)),
                                        ),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: Text(
                                                data.name ?? "",
                                                style: style_1_32_600,
                                              ),
                                            ),
                                            Image.asset(
                                              selectedPurposeCodes
                                                      .contains(data.code)
                                                  ? "images/check_circle_blue.png"
                                                  : "images/un_check_circle.png",
                                              width: 18,
                                              height: 18,
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  // 底部确认按钮
                  Positioned(
                    left: 48.w,
                    right: 48.w,
                    bottom: 46.w,
                    child: Container(
                      height: 104.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24.w),
                        color: const Color(0xFF2693FF),
                      ),
                      child: TextButton(
                        onPressed: () async {
                          if (selectedPurposeCodes.isEmpty) {
                            EasyLoading.showToast("至少选择一项");
                            return;
                          }
                          model.updateLearningPurpose(selectedPurposeCodes);
                          Navigator.pop(context);
                        },
                        child: Text(
                          "确认",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 36.sp,
                            fontWeight: FontWeight.w800,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget SettingItem(String title, String? settingValue, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 104.w,
        padding: EdgeInsets.fromLTRB(32.w, 28.w, 32.w, 28.w),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(
              color: const Color(0xFFEEEEEE),
              width: 0.5.w,
            ),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style: style_1_32_600),
                ],
              ),
            ),
            Text(settingValue ?? "", style: style_4_28_400),
            SizedBox(width: 8.w),
            Image.asset(
              "images/arrow_right_black.png",
              width: 20.w,
              height: 20.w,
            ),
          ],
        ),
      ),
    );
  }
}

// 通用设置项组件
// class SettingItem extends StatelessWidget {
//   final String title;
//   final VoidCallback onTap;
//   final String? subtitle;
//   final String? settingValue;
//   final Widget? trailing;

//   const SettingItem({
//     Key? key,
//     required this.title,
//     required this.onTap,
//     this.subtitle,
//     this.settingValue,
//     this.trailing,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       onTap: onTap,
//       child: Container(
//         height: 104.w,
//         padding: EdgeInsets.fromLTRB(32.w, 28.w, 32.w, 28.w),
//         decoration: BoxDecoration(
//           color: Colors.white,
//           border: Border(
//             bottom: BorderSide(
//               color: const Color(0xFFEEEEEE),
//               width: 0.5.w,
//             ),
//           ),
//         ),
//         child: Row(
//           children: [
//             Expanded(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(title, style: style_1_32_600),
//                   if (subtitle != null)
//                     Padding(
//                       padding: EdgeInsets.only(top: 4.w),
//                       child: Text(subtitle!, style: style_1_24),
//                     ),
//                 ],
//               ),
//             ),
//             Text(settingValue ?? "", style: style_4_28_400),
//             trailing ??
//                 Icon(
//                   Icons.arrow_forward_ios,
//                   size: 24.w,
//                   color: Colors.black,
//                 ),
//           ],
//         ),
//       ),
//     );
//   }
// }

// 添加通用分割线组件
class CommonDivider extends StatelessWidget {
  const CommonDivider({super.key});

  @override
  Widget build(BuildContext context) {
    return Divider(
      height: 1.w,
      thickness: 1.w,
      color: const Color(0xFFCEDDE0),
    );
  }
}
