import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/http/loading_status.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/talk_voice_provider.dart';
import 'package:flutter_app_kouyu/modules/chat/view/free_talk_page.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';

class TalkVoicePage extends StatelessWidget {
  final TalkArgument argument;
  final num conversationId;
  const TalkVoicePage(
      {super.key, required this.argument, required this.conversationId});

  static Route<dynamic> route(
      {required TalkArgument argument, required num conversationId}) {
    return MaterialPageRoute<dynamic>(
        builder: (_) => TalkVoicePage(
              argument: argument,
              conversationId: conversationId,
            ));
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => TalkVoiceProvider(
        context,
        conversationId,
        argument: argument,
      ),
      child: Consumer<TalkVoiceProvider>(
        builder: (context, model, child) {
          return Scaffold(
            appBar: TalkVoicePageAPPBar(
              argument: argument,
              pop: (p0) {
                Navigator.of(context).pop(model.chatList);
              },
              conversationId: conversationId,
            ),
            backgroundColor: Colors.transparent,
            body: model.barrageIsOpen
                ? Column(
                    children: [
                      SizedBox(
                        height: 40.w,
                      ),
                      Expanded(
                          child: Align(
                        alignment: Alignment.topCenter,
                        child: ListView.builder(
                          reverse: true,
                          shrinkWrap: true,
                          itemCount: model.chatList.length,
                          itemBuilder: (context, index) {
                            return _chatItemWidget(model
                                .chatList[model.chatList.length - index - 1]);
                          },
                        ),
                      )),
                      _speakWidget(context, model)
                    ],
                  )
                : Column(
                    children: [
                      _speakPersonWidget(model),
                      const Spacer(),
                      _speakWidget(context, model)
                    ],
                  ),
          );
        },
      ),
    );
  }

  Container _speakPersonWidget(TalkVoiceProvider model) {
    final iconWidth = 360.w;
    final playingIconWidth = 516.w;
    // model.isPlaying = true;

    return Container(
      width: double.infinity,
      child: Stack(
        alignment: AlignmentDirectional.bottomCenter,
        children: [
          if (model.isPlaying)
            Lottie.asset('lottie/ripple_animation.json',
                width: playingIconWidth,
                height: playingIconWidth,
                repeat: true),
          Padding(
            padding: EdgeInsets.only(
                top: 134.w, bottom: playingIconWidth - iconWidth),
            child: ClipRRect(
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(iconWidth / 2),
                    bottomRight: Radius.circular(iconWidth / 2)),
                child: CachedNetworkImage(
                  imageUrl: LoginUtil.currentUserInfoModel()
                          ?.data
                          ?.userSettings
                          ?.characterImageUrl ??
                      "",
                  fit: BoxFit.fitWidth,
                  width: iconWidth,
                )),
          ),
          if (model.replyStatus == LoadingStatus.inProgress)
            Positioned(
                right: 94.w,
                bottom: 180.w,
                child: Image.asset('images/sikao.gif',
                    width: 216.w, height: 216.w))
        ],
      ),
    );
  }

  Container _chatItemWidget(TalkModel model) {
    return Container(
      margin: EdgeInsets.only(left: 64.w, right: 64.w, bottom: 40.w),
      child: Text(
        model.question ?? "",
        style: style_1_32,
      ),
    );
  }
}

Widget _speakWidget(BuildContext context, TalkVoiceProvider model) {
  Widget textContainer = Container();
  if (model.connecting) {
    textContainer = Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "接通中...",
          style: style_2_28,
        )
      ],
    );
  } else if (model.isPlaying || model.replyStatus == LoadingStatus.inProgress) {
    textContainer = GestureDetector(
      onTap: () {
        model.cancelRequest();
        model.startRecording();
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 24.w,
            height: 24.w,
            color: const Color(0xFF748A99),
          ),
          SizedBox(
            height: 20.w,
          ),
          Text(
            "说话或者点击打断",
            style: style_2_28,
          )
        ],
      ),
    );
  } else if (model.voiceIsOpen) {
    textContainer = Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Lottie.asset("lottie/sikao_animation.json", width: 193.w, repeat: true),
        Text(
          "你可以开始说话",
          style: style_2_28,
        )
      ],
    );
  } else if (!model.voiceIsOpen) {
    //静音
    textContainer = Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "你已静音",
          style: style_2_28,
        )
      ],
    );
  }

  return Container(
    height: 248.w,
    margin: EdgeInsets.only(left: 64.w, right: 64.w, bottom: 68.w),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: () {
            model.toggletVoiceOpen();
          },
          child: Image.asset(
            context.read<TalkVoiceProvider>().voiceIsOpen
                ? "images/talk_voice_icon_close.png"
                : "images/talk_voice_icon_on.png",
            width: 120.w,
            height: 120.w,
          ),
        ),
        Expanded(child: textContainer),
        GestureDetector(
          onTap: () {
            Navigator.of(context).pop(model.chatList);
          },
          child: Image.asset(
            "images/talk_voice_close.png",
            width: 120.w,
            height: 120.w,
          ),
        )
      ],
    ),
  );
}

class TalkVoicePageAPPBar extends StatelessWidget
    implements PreferredSizeWidget {
  const TalkVoicePageAPPBar({
    super.key,
    required this.argument,
    required this.pop,
    required this.conversationId,
  });
  final TalkArgument argument;
  final num conversationId;

  final Function(BuildContext) pop;

  @override
  Size get preferredSize => Size.fromHeight(248.w);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Row(mainAxisSize: MainAxisSize.max, children: [
      const SizedBox(
        width: 40,
      ),
      SizedBox(
        width: 80.w,
      ),
      const Spacer(),
      Text(
        argument.topicDetailModel?.data?.chinese ?? "自由聊天",
        style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w800,
            color: Color(0xFF061B1F)),
      ),
      SizedBox(
        width: 16.w,
      ),
      Container(
          height: 40.w,
          width: 60.w,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(12)),
            border: Border.all(
              color: const Color(0xFFDDEDF0),
              width: 1,
            ),
          ),
          child: Center(
              child: Text(
            argument.model == 1 ? "教学" : "实战",
            style: style_1_20,
          ))),
      const Spacer(),
      GestureDetector(
        onTap: () {
          context.read<TalkVoiceProvider>().toggleBarrageOpen();
        },
        child: Text(
          context.read<TalkVoiceProvider>().barrageIsOpen ? "关闭弹幕" : "打开字幕",
          style: style_2_28,
        ),
      ),
      SizedBox(
        width: 40.w,
      ),
    ]));
  }
}
