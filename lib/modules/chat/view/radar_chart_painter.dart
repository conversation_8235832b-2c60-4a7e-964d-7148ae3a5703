import 'dart:math';

import 'package:flutter/material.dart';

class RadarChartPainter extends CustomPainter {
  final int numSides;
  final List<double> values;
  final double maxValue;

  RadarChartPainter(
      {required this.numSides, required this.values, this.maxValue = 100.0});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width / 2, size.height / 2);

    // 绘制雷达背景
    drawRadarBackground(canvas, center, radius);
    // 绘制雷达网格
    drawRadarGrid(canvas, center, radius);
    // 绘制雷达数据
    drawRadarData(canvas, center, radius);
    // 绘制数值文本
    // drawTextLabels(canvas, center, radius);
  }

  void drawRadarBackground(Canvas canvas, Offset center, double radius) {
    final bgPaint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    final angleStep = 2 * pi / numSides;
    for (int i = 0; i < numSides; i++) {
      final x = center.dx + radius * cos(i * angleStep);
      final y = center.dy + radius * sin(i * angleStep);
      canvas.drawLine(center, Offset(x, y), bgPaint..strokeWidth);
    }
  }

  void drawRadarGrid(Canvas canvas, Offset center, double radius) {
    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    const int gridLevel = 4; // 网格层数
    final angleStep = 2 * pi / numSides;
    final double gridRadiusStep = radius / gridLevel;

    for (int i = 1; i <= gridLevel; i++) {
      final currentRadius = gridRadiusStep * i;
      final path = Path();
      for (int j = 0; j < numSides; j++) {
        final x = center.dx + currentRadius * cos(j * angleStep);
        final y = center.dy + currentRadius * sin(j * angleStep);

        if (j == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }
      path.close();
      canvas.drawPath(path, gridPaint);
    }
  }

  void drawRadarData(Canvas canvas, Offset center, double radius) {
    final dataPaint = Paint()
      ..color = const Color(0x1F41C0FF)
      ..style = PaintingStyle.fill;

//绘制最外层的线
    final linePaint = Paint()
      ..color = const Color(0xFF41C0FF)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    final path = Path();
    final angleStep = 2 * pi / numSides;
    Offset firstOffset = Offset.zero;

    Offset lastOffset = Offset.zero;
    for (int i = 0; i < numSides; i++) {
      final normalizedValue = values[i] / maxValue;
      final x = center.dx + radius * normalizedValue * cos(i * angleStep);
      final y = center.dy + radius * normalizedValue * sin(i * angleStep);

      if (i == 0) {
        path.moveTo(x, y);
        lastOffset = Offset(x, y);
        firstOffset = Offset(x, y);
      } else {
        path.lineTo(x, y);
        canvas.drawLine(lastOffset, Offset(x, y), linePaint);
        lastOffset = Offset(x, y);
        if (i == numSides - 1) {
          canvas.drawLine(lastOffset, firstOffset, linePaint);
        }
      }
    }
    path.close();
    canvas.drawPath(path, dataPaint);
  }

  void drawTextLabels(Canvas canvas, Offset center, double radius) {
    final textPainter = TextPainter(textDirection: TextDirection.ltr);

    final angleStep = 2 * pi / numSides;
    for (int i = 0; i < numSides; i++) {
      final value = values[i].toStringAsFixed(1);
      final x = center.dx + radius * cos(i * angleStep);
      final y = center.dy + radius * sin(i * angleStep);

      textPainter.text = TextSpan(
          text: value, style: TextStyle(color: Colors.black, fontSize: 12.0));
      textPainter.layout();
      textPainter.paint(canvas,
          Offset(x - textPainter.width / 2, y - textPainter.height / 2));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
