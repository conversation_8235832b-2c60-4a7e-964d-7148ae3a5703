import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NewReportPage extends StatelessWidget {
  const NewReportPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        margin: EdgeInsets.only(top: 176.w),
        padding: EdgeInsets.fromLTRB(40.w, 48.w, 40.w, 0),
        decoration: BoxDecoration(
          color: const Color(0xFFF3FBFD),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.w),
            topRight: Radius.circular(20.w),
          ),
        ),
        child: CustomScrollView(
          scrollDirection: Axis.vertical,
          slivers: [
            _titleWidget(context),
            _originTextWidget(),
            SliverToBoxAdapter(
                child: SizedB<PERSON>(
              height: 24.w,
            )),
            _voiceListWidget()
          ],
        ),
      ),
    );
  }

  SliverToBoxAdapter _voiceListWidget() {
    return SliverToBoxAdapter(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _voiceWidget("美音", "url"),
          _voiceWidget("英音", "url"),
          _voiceWidget("我的", "url"),
          _voiceWidget("翻译", "url"),
          GestureDetector(
            onTap: () {},
            child: Container(
              height: 56.w,
              width: 143.w,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.w),
                  color: const Color(0xFF2693FF)),
              child: Center(
                child: Text(
                  "再读一遍",
                  style: style_1_24.copyWith(color: Colors.white),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  SliverToBoxAdapter _originTextWidget() {
    return SliverToBoxAdapter(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: RichText(
                text: TextSpan(children: [
              for (int i = 0; i < 3; i++)
                TextSpan(
                  text: "e32323223e32rj32prj2",
                  style: TextStyle(
                      fontSize: 32.sp,
                      color: [
                        const Color(0xFF00B578),
                        const Color(0xFF061B1F),
                        const Color(0xFFFF8F1F),
                      ][i]),
                ),
            ])),
          ),
          Container(
            width: 112.w,
            height: 48.w,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16.w),
                border: Border.all(width: 1.w, color: separated)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  "images/start_blue.png",
                  width: 24.w,
                  height: 24.w,
                ),
                SizedBox(
                  width: 8.w,
                ),
                Text(
                  "收藏",
                  style: style_2_24,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  SliverToBoxAdapter _titleWidget(BuildContext context) {
    return SliverToBoxAdapter(
      child: SizedBox(
        height: 54.w,
        child: Row(
          children: [
            Text("原文", style: style_1_36),
            const Spacer(),
            GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Image(
                image: const AssetImage("images/dialog_close.png"),
                fit: BoxFit.fill,
                width: 26.w,
                height: 26.w,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Container _voiceWidget(String type, String url) {
    return Container(
      height: 48.w,
      decoration: BoxDecoration(
          border: Border.all(color: ColorUtil.separated),
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(16.w)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 24.w,
          ),
          Text(
            type,
            style: style_2_24,
          ),
          PlayButtonWidget(
            style: PlayButtonStyle.blue,
            audioUrl: url,
          ),
        ],
      ),
    );
  }
}
