import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/carousel_list_model/carousel_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/study_progress_list_model/study_progress_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/user_info_model/user_info_model.dart';
import 'package:flutter_app_kouyu/common/http/models/weekly_free_topic_list_model/weekly_free_topic_list_model.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';

class ChatPageModel with ChangeNotifier {
  /// difficulty popularity
  CarouselListModel? carouselListModel;
  WeeklyFreeTopicListModel? weeklyFreeTopicListModel;
  StudyProgressListModel? studyProgressListModel;

  UserInfoModel? userInfoModel = LoginUtil.currentUserInfoModel();

  getUserInfo() async {
    userInfoModel = await LoginUtil.updateUserInfo();
    notifyListeners();
  }

  getCarouselList() async {
    carouselListModel = await Api.carouselList();
    notifyListeners();
  }

  getWeeklyFreeTopicList() async {
    weeklyFreeTopicListModel = await Api.weeklyFreeTopicList();
    notifyListeners();
  }

  getstudyProgressList() async {
    studyProgressListModel = await Api.todayStudyProgressList();
    notifyListeners();
  }

  updateUserInfo() {
    userInfoModel = LoginUtil.currentUserInfoModel();
    notifyListeners();
  }
}
