// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/english_level_model/english_level_model.dart';

import 'package:flutter_app_kouyu/common/http/models/learning_phase_model/learning_phase_model.dart';
import 'package:flutter_app_kouyu/common/http/models/purpuse_model/purpuse_model.dart';
import 'package:flutter_app_kouyu/common/http/models/user_info_model/user_info_model.dart';

/// 雅思成绩详情弹窗provider
class LearningSettingsProvider extends ChangeNotifier {
  UserInfoModel? userInfoModel;
  EnglishLevelModel? englishLevelModel;
  LearningPhaseModel? learningStageList;
  PurpuseModel? learningPurposeList;
  List<num> speedList = [0.6, 0.8, 1.0, 1.2];
  String? teachingMode; // 中英文混合教学模式
  num? speakingSpeed; // 语速
  String? englishLevel; // 英语水平
  String? englishLevelName; // 英语水平名称
  List<String>? learningPurposeCode; // 学习目的

  String? learningStage; // 学习阶段
  String? learningStageName; // 学习阶段名称
  num? isMixedTeaching = 1; // 中英混合教学

  String? firstLearningPurposeName; // 第一个学习目的
  static final Future<SharedPreferences> prefs =
      SharedPreferences.getInstance();
  
  bool isLoading = false; // 标记数据是否已经加载过

  refreshData() async {
    // 如果已经加载过数据，直接使用缓存中的数据
  
       isLoading = true;
    // 尝试从本地存储加载数据
    // bool loadedFromCache = await _loadFromCache();
    
    // 如果本地缓存数据加载失败，则从网络获取
    // if (!loadedFromCache) {
    //   await getUserInfo();
    //   await getAllEnglishLevels();
    //   // 获取其他设置项数据
    //   await loadSettings();
      
    //   // 缓存获取到的数据
    //   // await _saveToCache();
    // }
      await getAllEnglishLevels();
      await getUserInfo();
      await loadSettings();
      isLoading = false;


    //   // 获取其他设置项数据
    //    loadSettings();
    

    notifyListeners();
  }

  // 强制刷新数据，忽略缓存
  forceRefreshData() async {
   
    // 清除本地缓存
 
    await refreshData();
  }

   Future<UserInfoModel?> getUserInfo() async {
    userInfoModel = await LoginUtil.userInfoModel();
 
    notifyListeners();
      print('=======loadSettings end');
    // userInfoModel = await Api.getUserInfo({});
    return userInfoModel;
  }

  getAllEnglishLevels() async {
    englishLevelModel = await Api.getAllEnglishLevels();
    learningStageList = await Api.getLearningPhaseList();
    learningPurposeList = await Api.purpose();
  }

  loadSettings() async{

        isMixedTeaching = userInfoModel?.data?.userSettings?.isMixedTeaching ?? 1;
    // 语速
    speakingSpeed = userInfoModel?.data?.userSettings!.playbackSpeed;

    // 英语水平
    englishLevel = userInfoModel?.data?.englishLevel;

    englishLevelModel?.data?.forEach((element) {
      if (element.levelEn == userInfoModel!.data?.englishLevel) {
        englishLevelName = element.levelCn;
      }
    });
    // learningPurposeCode = (jsonDecode(userInfoModel?.data?.userSettings?.learningPurpose ?? '[]') as List).cast<String>();

    learningPurposeCode = userInfoModel?.data?.userSettings?.learningPurpose;


    // print('learningPurposeCode: $learningPurposeCode');

    if (learningPurposeCode?.isNotEmpty == true) {
      String learningPurposefirstCode = learningPurposeCode?.first ?? '';

      learningPurposeList?.data?.forEach((element) {
        if (element.code == learningPurposefirstCode) {
          firstLearningPurposeName = element.name;
        }
      });
    }

    // 学习阶段
    learningStage = userInfoModel?.data?.userSettings!.learningPhase;

    learningStageList?.data?.forEach((element) {
      if (element.code == userInfoModel?.data?.userSettings?.learningPhase) {
        learningStageName = element.name;
      }
    });

    // 中英混合教学


    print('=======loadSettings end');
    // notifyListeners();
  }

  
  
  // 清除本地缓存



  Future<void> updateSpeakingSpeed(num speed) async {
    await Api.setPlaybackSpeed(speed);
    speakingSpeed = speed;
    await LoginUtil.updateUserInfo();
    notifyListeners();
  }

  Future<void> updateEnglishLevel(String level, String levelName) async {
    print('level: $level');

    await Api.setEnglishLevel({
      "english_level": level,
    });
    englishLevel = level;
    englishLevelName = levelName;

    await LoginUtil.updateUserInfo();
    notifyListeners();
  }

  Future<void> updateLearningPurpose(List<String> purposeCodes) async {
    print('purposeCodes: $purposeCodes');
    await Api.setLearningPurpose({
      "purpose_code": purposeCodes,
    });

    learningPurposeCode = purposeCodes;
    
    // 更新第一个学习目的名称
    if (purposeCodes.isNotEmpty) {
      String firstCode = purposeCodes.first;
      learningPurposeList?.data?.forEach((element) {
        if (element.code == firstCode) {
          firstLearningPurposeName = element.name;
        }
      });
    } else {
      firstLearningPurposeName = null;
    }
    
    await LoginUtil.updateUserInfo();
    notifyListeners();
  }

  Future<void> updateLearningStage(String stage, String stageName) async {
    await Api.setLearningPhase(stage);
    learningStage = stage;
    learningStageName = stageName;
    await LoginUtil.updateUserInfo();
    notifyListeners();


  }

  Future<void> updateMixedTeaching(num value) async {
    try {
      await Api.setIsMixedTeaching(value);
      isMixedTeaching = value;
      await LoginUtil.updateUserInfo();
      notifyListeners();
      // await _updateCache();

 
    } catch (e) {
      LogUtil.d('更新中英文混合教学设置失败: $e');
    }
  }
}
