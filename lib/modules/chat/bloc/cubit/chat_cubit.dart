import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/loading_status.dart';
import 'package:flutter_app_kouyu/common/http/models/analyze_model/analyze_model.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_hint_model/chat_hint_model.dart';
import 'package:flutter_app_kouyu/common/http/models/collect_sentence.dart';
import 'package:flutter_app_kouyu/common/http/models/en_to_ch_model.dart';
import 'package:flutter_app_kouyu/common/http/models/audio_upload_model/audio_upload_model.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';
import 'package:flutter_app_kouyu/common/http/request_error.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/common/utils/local_audio_player.dart';
import 'package:flutter_app_kouyu/common/utils/log.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_state.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';
import 'package:flutter_app_kouyu/modules/chat/view/free_talk_page.dart';
import 'package:flutter_app_kouyu/modules/chat/view/talk_video_page.dart';
import 'package:flutter_app_kouyu/modules/chat/view/talk_voice_page.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../common/http/models/chat_hint_model/data.dart';

class ChatCubit extends ChangeNotifier implements VoiceEvent {
  final FreeTalkRepository freeTalkRepository = FreeTalkRepository();
  final TalkArgument argument;
  final ChatState state = ChatState();
  final BuildContext context;
  bool checkTargetFinish = false;
  bool showMiniVideo = false;
  //AI是否正在说话
  bool personSaying = false;
  ChatCubit(
    this.context, {
    required this.argument,
  });

  Future<void> intiGreetingStream() async {
    //取最新的model
    int index = state.list.length;
    TalkModel initModel = TalkModel(
        type: 0, replyStatus: LoadingState(status: LoadingStatus.inProgress));
    state.list.add(initModel);
    notifyListeners();
    try {
      TalkModel model = await freeTalkRepository.getTimePeriodGreeting(
          model: argument.model, topic: argument.topic);
      state.list[index] = model;
      notifyListeners();
      Log.v("audoourl:${model.audioUrl}");
      playVideo(model.audioUrl, finish: () {
        personSaying = false;
        notifyListeners();
      });
    } catch (e) {
      initModel = TalkModel(
          type: 0, replyStatus: LoadingState(status: LoadingStatus.failure));
      state.list[index] = initModel;
      notifyListeners();
    }
  }

  //不显示提示
  cleanHit() {
    state.chatHintList?.clear();
    state.chatHintStatus = null;
    notifyListeners();
  }

  chat(String? question, String filePath) async {
    Log.d("准备 发送语音");
    if (question == null || question.isEmpty) {
      return;
    }
    //添加问话
    TalkModel questionModel = TalkModel.questionModel(
        state.list.last.conversationId, question, "",
        soeStatus: const LoadingState(status: LoadingStatus.inProgress),
        analyzeStatus: const LoadingState(status: LoadingStatus.inProgress));
    if (questionModel.conversationId == null) {
      return;
    }
    state.list.add(questionModel);
    int index = state.list.length - 1;
    //获取对话
    sendChat(questionModel);
    //分析语音,教学模式不执行
    if (argument.model != 1) {
      analyze(questionModel, index);
    }
    //上传语音
    //查询是否完成对话目标
    if (!argument.topic.isEmpty &&
        checkTargetFinish == false &&
        argument.model != 1) {
      //场景模式
      _checkTargetFinish(questionModel.conversationId);
    }

    try {
      Future.delayed(Duration(seconds: 1), () async {
        //延时1s，防止录音文件有问题
        AudioUploadModel value = await uploadVoice(filePath);
        //刷新url
        state.list[index].audioUrl = value.data?.fileUrl;
        //soe
        questionModel.audioUrl = value.data?.fileUrl;
        soe(questionModel, index);
      });
    } catch (_) {
      //刷新url
      state.list[index].soeStatus =
          const LoadingState(status: LoadingStatus.failure);
      state.list[index].analyzeStatus =
          const LoadingState(status: LoadingStatus.failure);

      notifyListeners();
    }
    ;
  }

  Future<void> sendChat(
    TalkModel question,
  ) async {
    //取最新的model
    int index = state.list.length;
    TalkModel initModel = TalkModel(
        type: 0,
        replyStatus: const LoadingState(status: LoadingStatus.inProgress));
    state.list.add(initModel);
    notifyListeners();
    try {
      //请求答案
      TalkModel model = await freeTalkRepository.getChat({
        "question": question.question,
        "conversation_id": question.conversationId,
        "client_sentence_id": question.clientSentenceId,
        "topic": argument.topic,
        "mode": argument.model
      });
      state.list[index] = model;
      notifyListeners();
      playVideo(model.audioUrl, finish: () {
        personSaying = false;
        notifyListeners();
      });
    } catch (e) {
      initModel = TalkModel(
          type: 0, replyStatus: LoadingState(status: LoadingStatus.failure));
      state.list[index] = initModel;
      notifyListeners();
    }
  }

  Future<AudioUploadModel> uploadVoice(String filePath) async {
    return freeTalkRepository.upload(filePath,
        topicCode: argument.topic, sence: "scene_dialogue");
  }

  void translate(TalkModel model, int index) async {
    if (model.translateStatus?.status == LoadingStatus.inProgress) {
      return;
    }
    update(index, () => freeTalkRepository.translate(model.question));
  }

  void collection(TalkModel model, int index) async {
    if (model.collected == true) {
      await freeTalkRepository.cancelCollectionSentence(model.question);
      model.collected = false;
      notifyListeners();
      return;
    } else {
      await freeTalkRepository.collectionSentence(
          model.question, "conversation_history", "0");
      model.collected = true;
      notifyListeners();
      return;
    }
  }

  void collectionHitModel(Data data) async {
    if (data.collected == true) {
      data.collected = false;
      notifyListeners();
      return;
    }
    await freeTalkRepository.collectionSentence(
        data.english, "conversation_history", "0");
    data.collected = true;
    notifyListeners();
  }

  void update<T>(int index, Future<T> Function() request) async {
    //取最新的model
    if (T == EnToCnModel) {
      state.list[index].translateStatus =
          const LoadingState(status: LoadingStatus.inProgress);
    } else if (T == AnalyzeModel) {
      state.list[index].analyzeStatus =
          const LoadingState(status: LoadingStatus.inProgress);
    } else if (T == SoeModel) {
      state.list[index].soeStatus =
          const LoadingState(status: LoadingStatus.inProgress);
    } else if (T == CollectionSentence) {
      state.list[index].collected = true;
    } else {
      return;
    }
    notifyListeners();
    try {
      T result = await request();
      //取最新的model
      if (T == EnToCnModel) {
        state.list[index].translateStatus =
            const LoadingState(status: LoadingStatus.success);
        state.list[index].translateText = (result as EnToCnModel).data;
      } else if (T == AnalyzeModel) {
        state.list[index].analyzeStatus =
            const LoadingState(status: LoadingStatus.success);
        state.list[index].analyzeModel = result as AnalyzeModel;
      } else if (T == SoeModel) {
        state.list[index].soeStatus =
            const LoadingState(status: LoadingStatus.success);
        state.list[index].soeModel = result as SoeModel;
      } else {
        return;
      }
      notifyListeners();
    } catch (e) {
      //取最新的model
      String? errorMessage = (e as RequestError).message;
      if ((e).code == "0019" || (e).code == "0020") {
        //中文不能进行语音评测 不支持中文语法检测
        errorMessage = null;
      }

      if (T == EnToCnModel) {
        state.list[index].translateStatus =
            LoadingState(status: LoadingStatus.failure, message: errorMessage);
      } else if (T == AnalyzeModel) {
        state.list[index].analyzeStatus =
            LoadingState(status: LoadingStatus.failure, message: errorMessage);
      } else if (T == SoeModel) {
        state.list[index].soeStatus =
            LoadingState(status: LoadingStatus.failure, message: errorMessage);
      } else {
        return;
      }
      notifyListeners();
    }
  }

  void getChatHint() async {
    String? text = state.list.last.question;
    if (text == null || text.isEmpty) {
      return;
    }
    if (state.chatHintList.isNotEmpty) {
      return;
    }
    state.chatHintStatus = LoadingStatus.inProgress;
    notifyListeners();
    try {
      ChatHintModel result = await freeTalkRepository.generateNextSentence(
          argument.topicDetailModel?.data?.english,
          "scene_chat",
          state.list.last.conversationId?.toInt() ?? 0);
      state.chatHintList = result.data ?? [];
      state.chatHintStatus = LoadingStatus.success;
      notifyListeners();
    } catch (_) {
      state.chatHintStatus = LoadingStatus.failure;
      notifyListeners();
    }
  }

  void jumpVoicePage() {
    if (state.list.last.conversationId == null) {
      return;
    }
    Navigator.of(context)
        .push(TalkVoicePage.route(
            argument: argument,
            conversationId: state.list.last.conversationId!))
        .then((value) {
      if (value != null && value is List<TalkModel>) {
        state.list.addAll(value);
        notifyListeners();
      }
    });
  }

  void jumpVideoPage() {
    if (state.list.last.conversationId == null) {
      return;
    }
    Navigator.of(context)
        .push(TalkVideoPage.route(
            argument: argument,
            conversationId: state.list.last.conversationId!))
        .then((value) {
      showMiniVideo = true;
      if (value != null && value is List<TalkModel>) {
        state.list.addAll(value);
      }
      notifyListeners();
    });
  }

  Future<void> analyze(TalkModel model, int index) async {
    // if (model.analyzeStatus == LoadingStatus.inProgress) {
    //   return;
    // }
    String sence = "scenario_actual_combat";
    if (argument.model == 1) {
      sence = "teaching_mode";
    }
    update(index, () => freeTalkRepository.analyze(model, sence));
  }

  Future<void> soe(TalkModel model, int index) async {
    // if (model.soeStatus == LoadingStatus.inProgress) {
    //   return;
    // }
    String sence = "scenario_actual_combat";
    if (argument.model == 1) {
      sence = "teaching_mode";
    }
    update(index, () => freeTalkRepository.soe(model, sence));
  }

  void playVideo(String? url, {void Function()? finish}) async {
    CommonUtils.playVideo(url ?? "", finish: finish);
  }

  void _checkTargetFinish(num? conversationId) async {
    checkTargetFinish = await Api.checkTargetFinish({
      "conversation_id": conversationId,
    });
    if (checkTargetFinish) {
      showFinishTaskDialog(context);
      // 播放音效
      LocalAudioPlayer.playLocalAudio(1);
    }
  }

  showFinishTaskDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      routeSettings: const RouteSettings(arguments: {"stopPlay": false}),
      builder: (context) {
        Future.delayed(const Duration(milliseconds: 1000), () {
          Navigator.of(context).pop();
        });
        return GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: Container(
            width: double.infinity,
            height: double.infinity,
            padding: EdgeInsets.only(top: 200.w),
            child: Align(
              alignment: Alignment.topCenter,
              child: Image.asset(
                "images/finish_task_alert.png",
                width: 397.w,
                fit: BoxFit.fitWidth,
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  recordFinish(String? question, String filePath) {
    chat(question, filePath);
  }
}
