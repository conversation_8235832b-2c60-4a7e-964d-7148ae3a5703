import 'package:equatable/equatable.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/role_list_model/data.dart';
import 'package:flutter_app_kouyu/common/http/models/role_list_model/role_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/user_info_model/user_info_model.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'change_role_state.dart';

class ChangeRoleCubit extends Cubit<ChangeRoleState> {
  ChangeRoleCubit() : super(const ChangeRoleState());

  void getRoleList() async {
    UserInfoModel? userInfoModel = await LoginUtil.userInfoModel();

    String _name = userInfoModel?.data?.userSettings?.characterName ?? "";

    Data? currentData;
    int? index;
    Api.getRoleList({}).then((value) {
      for (Data data in value!.data!) {
        if (data.name == _name) {
          currentData = data;
          index = value!.data!.indexOf(data);
        }
      }
      currentData ??= value.data?.first;
      emit(state.copyWith(
          roleListModel: value,
          currentData: currentData,
          selectdData: value!.data!.first,
          index: 0));
    });
  }

  void setSelectedModel(int index) {
    emit(state.copyWith(
        selectdData: state.roleListModel!.data![index], index: index));
  }

  Future<bool> setRole(int? roleId) {
    return Api.setRole(roleId).then((value) async {
      await LoginUtil.updateUserInfo();
      return Future.value(value);
    });
  }
}
