// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/audio_upload_model/audio_upload_model.dart';
import 'package:flutter_app_kouyu/common/http/models/collect_sentence.dart';
import 'package:flutter_app_kouyu/common/http/models/scene_topics_vocabulary_model/list.dart';
import 'package:flutter_app_kouyu/common/http/models/scene_topics_vocabulary_model/scene_topics_vocabulary_model.dart';
import 'package:flutter_app_kouyu/common/http/models/sence_topic_sentence/list.dart';
import 'package:flutter_app_kouyu/common/http/models/sence_topic_sentence/sence_topic_sentence.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';
import 'package:flutter_app_kouyu/common/http/models/vocabulary_soe_model/vocabulary_soe_model.dart';
import 'package:flutter_app_kouyu/common/provider/voice_model.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';

class VocabularyPracticeProvider extends VoiceModel {
  String topicCode;
  SceneTopicsVocabularyModel? sceneTopicsVocabularyModel;
  SenceTopicSentence? senceTopicSentenceModel;

  int _vocabularyIndex = 0;

  int get vocabularyIndex => _vocabularyIndex;

  int _sentenceIndex = 0;

  Vocabulary? get currentVocabulary {
    return sceneTopicsVocabularyModel?.data?.list?[_vocabularyIndex];
  }

  Sentence? get currentSentence {
    return senceTopicSentenceModel?.data?.list?[_sentenceIndex];
  }

  @override
  void dispose() {
    stopRecording();
    super.dispose();
  }

  int get sentenceIndex => _sentenceIndex;

  int _type = 1; //1 单词 2 句子
  int _showType = 1; // 1 列表 2 卡片
  set type(int type) {
    _type = type;
    notifyListeners();
    if (_type == 1) {
      CommonUtils.playVideo(currentVocabulary?.defaultPronunciationUrl);
    } else if (_type == 2) {
      CommonUtils.playVideo(currentSentence?.defaultPronunciationUrl);
    }
  }

  int get type => _type;
  set showType(int type) {
    _showType = type;
    notifyListeners();
  }

  int get showType => _showType;

  VocabularyPracticeProvider({
    required this.topicCode,
  });

  findAndJump(String word) {
    if (sceneTopicsVocabularyModel?.data?.list == null) {
      return;
    }
    Vocabulary? vocabulary =
        sceneTopicsVocabularyModel?.data?.list!.firstWhere((element) {
      return (element.vocabulary == word);
    });
    if (vocabulary == null) {
      return;
    }
    int index = sceneTopicsVocabularyModel!.data!.list!.indexOf(vocabulary);
    jumpToVocabularyCard(index);
  }

  jumpNext() {
    stop();
    if (_type == 1) {
      if (sceneTopicsVocabularyModel?.data?.listSize == null ||
          sceneTopicsVocabularyModel!.data!.listSize! <= _vocabularyIndex) {
        return;
      }
      jumpToVocabularyCard(_vocabularyIndex + 1);
    } else {
      if (sceneTopicsVocabularyModel?.data?.listSize == null ||
          senceTopicSentenceModel!.data!.listSize! <= _sentenceIndex) {
        return;
      }
      jumpToSentenceCard(_sentenceIndex + 1);
    }
  }

  learnedVocabularySuccess(int id) async {
    await Api.saveHaveLearnedVocabulary(
        {"vocabulary_id": id, "topic_code": topicCode});
    jumpNext();
  }

  jumpToVocabularyCard(int index) {
    _vocabularyIndex = index;
    _type = 1;
    _showType = 2;
    notifyListeners();
    CommonUtils.playVideo(currentVocabulary?.defaultPronunciationUrl);
  }

  jumpToSentenceCard(int index) {
    _sentenceIndex = index;
    _type = 2;
    _showType = 2;
    notifyListeners();
    CommonUtils.playVideo(currentSentence?.defaultPronunciationUrl);
  }

  refreshData() {
    Api.sceneTopicsVocabularyModel({"topic_code": topicCode}).then((value) {
      sceneTopicsVocabularyModel = value;
      notifyListeners();
    });
    Api.sceneTopicSsentenceModel({"topic_code": topicCode}).then((value) {
      senceTopicSentenceModel = value;
      // notifyListeners();
    });
  }

  Future<bool> sent() async {
    await super.stop();
    notifyListeners();

    //上传文件
    AudioUploadModel audioUploadModel = await Api.uploadFile(filePath!,
        topicCode: topicCode, scene: "word_learning");
    //soe
    if (type == 1) {
      Vocabulary vocabulary = currentVocabulary!;
      VocabularySoeModel soe = await Api.vocabularySoeModel({
        "topic_code": topicCode,
        "vocabulary": currentVocabulary?.vocabulary,
        "user_pronunciation_url": audioUploadModel.data?.fileUrl,
        "vocabulary_id": vocabulary.id!
      });
      if (soe.data?.suggestedScore != null) {
        vocabulary.score = "${soe.data?.suggestedScore}";
        vocabulary.userPronunciationUrl = audioUploadModel.data?.fileUrl;
      }
    } else {
      Sentence sentence = currentSentence!;
      SoeModel soe = await Api.sentenceSoeModel({
        "topic_code": topicCode,
        "user_pronunciation_url": audioUploadModel.data?.fileUrl,
        "sentence_id": sentence.id!
      });
      if (soe.data?.suggestedScore != null) {
        sentence.score = "${soe.data?.suggestedScore}";
        sentence.userPronunciationUrl = audioUploadModel.data?.fileUrl;
      }
    }
    notifyListeners();

    return true;
  }

  collectionSentence(Sentence sentence) async {
    await Api.collectionSentence(sentence.sentence, 'course_study', null);
    sentence.collected = true;
    notifyListeners();
  }

  cancelCollectionSentence(Sentence sentence) async {
    await Api.cancelCollectionSentence(sentence.sentence);
    sentence.collected = false;
    notifyListeners();
  }

  collectVocabulary(Vocabulary vocabulary) async {
    await Api.collectVocabulary(vocabulary.vocabulary);
    vocabulary.collected = true;
    notifyListeners();
  }

  cancelCollectVocabulary(Vocabulary vocabulary) async {
    await Api.cancelCollectVocabulary(vocabulary.vocabulary);
    vocabulary.collected = false;
    notifyListeners();
  }

  followFinish() {}
}
