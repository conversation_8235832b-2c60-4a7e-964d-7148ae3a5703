part of 'voice_cubit.dart';

enum VoiceStateEnum {
  init,
  recording,
}

enum VoiceTypeEnum {
  init,
  ch,
  cnTanslating,
  cnTanslated,
  cnTanslateFaild,
  follow,
  en,
  textInput,
}

extension VoiceTypeEnumExtension on VoiceTypeEnum {
  // model 0: 自由聊天 1: 教学模式

  String title({int? model}) {
    switch (this) {
      case VoiceTypeEnum.ch:
        return "请说中文，用中文表达你的意图。";
      case VoiceTypeEnum.en:
      case VoiceTypeEnum.follow:
        if (model == 1) {
          return "支持中英文";
        } else {
          return "请说英语";
        }
      case VoiceTypeEnum.cnTanslating:
        return "翻译中...";
      case VoiceTypeEnum.cnTanslateFaild:
        return "翻译失败";
      default:
        return "";
    }
  }
}

final class VoiceState extends Equatable {
  final VoiceStateEnum voiceState;
  final VoiceTypeEnum voiceType;
  final String text;
  final int seconds;
  final ChToEnAudioModel? chToEnAudioModel;
  final String? followTest;
  const VoiceState(
      {this.chToEnAudioModel,
      this.voiceState = VoiceStateEnum.init,
      this.voiceType = VoiceTypeEnum.init,
      this.text = '',
      this.seconds = 0,
      this.followTest});

  @override
  List<Object?> get props =>
      [voiceState, voiceType, seconds, text, chToEnAudioModel, followTest];

  VoiceState copywith(
      {VoiceStateEnum? voiceState,
      VoiceTypeEnum? voiceType,
      String? text,
      int? seconds,
      ChToEnAudioModel? chToEnAudioModel,
      String? followTest}) {
    return VoiceState(
        voiceState: voiceState ?? this.voiceState,
        voiceType: voiceType ?? this.voiceType,
        text: text ?? this.text,
        seconds: seconds ?? this.seconds,
        chToEnAudioModel: chToEnAudioModel ?? this.chToEnAudioModel,
        followTest: followTest ?? this.followTest);
  }

  String getTitle({int? model}) {
    if (voiceType == VoiceTypeEnum.cnTanslated) {
      return chToEnAudioModel?.data?.question ?? "_";
    }
    if (voiceType == VoiceTypeEnum.cnTanslating) {
      return voiceType.title(model: model);
    }
    if (voiceType == VoiceTypeEnum.cnTanslateFaild) {
      return "翻译失败,请重试";
    }
    return text.isNotEmpty ? text : voiceType.title(model: model);
  }

  String getTitle2({int? model}) {
    if (voiceType == VoiceTypeEnum.cnTanslated) {
      return chToEnAudioModel?.data?.question ?? "_";
    }
    if (voiceType == VoiceTypeEnum.cnTanslating) {
      return voiceType.title(model: model);
    }
    if (voiceType == VoiceTypeEnum.cnTanslateFaild) {
      return "翻译失败,请重试";
    }
    return text.isNotEmpty ? text : "";
  }

  String getTopTitle({int? model}) {
    return voiceType.title(model: model);
  }
}
