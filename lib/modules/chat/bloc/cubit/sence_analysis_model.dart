import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/loading_status.dart';
import 'package:flutter_app_kouyu/common/http/models/analyze_model/analyze_model.dart';
import 'package:flutter_app_kouyu/common/http/models/english_tap_model/datum.dart';
import 'package:flutter_app_kouyu/common/http/models/english_tap_model/english_tap_model.dart';
import 'package:flutter_app_kouyu/common/http/models/sentenc_polishing_model/sentenc_polishing_model.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';
import 'package:flutter_app_kouyu/modules/chat/view/follow_read_widget.dart';
import 'package:flutter_app_kouyu/modules/chat/view/free_talk_page.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';

class SenceAnalysisModel extends ChangeNotifier {
  final SoeModel? soeModel;
  final AnalyzeModel? analyzeModel;
  final TalkModel talkModel;
  final TalkArgument argument;

  final FreeTalkRepository freeTalkRepository = FreeTalkRepository();
  EnglishTapModel? englishTapModel;
  Datum? polishedEnglishLabel;
  List<String> collectedList = [];
  Map<String, String?> translateMap = {};
  SenceAnalysisModel(
      {this.soeModel,
      this.analyzeModel,
      required this.talkModel,
      required this.argument}) {
    Api.getPolishedEnglishLabelList(null).then((value) {
      englishTapModel = value;
      if (englishTapModel?.data?.isNotEmpty == true) {
        setPolishedEnglishLabel(englishTapModel!.data!.first);
      }
    });
  }

  translate(String? text) {
    if (text == null) {
      return;
    }
    freeTalkRepository.translate(text).then((value) {
      translateMap[text] = value.data;
      notifyListeners();
    });
  }

  setPolishedEnglishLabel(Datum polishedEnglishLabel) async {
    this.polishedEnglishLabel = polishedEnglishLabel;
    notifyListeners();
    if (polishedEnglishLabel.sentencPolishingModel != null) {
      return;
    }
    String sence = "scenario_actual_combat";
    if (argument.model == 1) {
      sence = "teaching_mode";
    }
    polishedEnglishLabel.loadingStatus = LoadingStatus.inProgress;
    //获取润色接口
    try {
      polishedEnglishLabel.sentencPolishingModel = await Api.sentencePolishing({
        "text": talkModel.question,
        "conversation_id": talkModel.conversationId,
        "client_sentence_id": talkModel.clientSentenceId,
        "scene": sence,
        "type": polishedEnglishLabel.type
      });
      polishedEnglishLabel.loadingStatus = LoadingStatus.success;
    } catch (_) {
      polishedEnglishLabel.loadingStatus = LoadingStatus.failure;
    } finally {
      notifyListeners();
    }
  }

  collection(String? text) async {
    await freeTalkRepository.collectVocabulary(text);
    if (text != null) {
      collectedList.add(text);
    }
    notifyListeners();
  }

  void gotoFollowRead(BuildContext context, String text) {
    showModalBottomSheet(
        isScrollControlled: true,
        useSafeArea: true,
        constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height -
                MediaQuery.of(context).padding.top),
        backgroundColor: Colors.transparent,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20))),
        context: context,
        builder: (context) {
          return FollowReadWidget(
            text: text,
            conversationId: talkModel.conversationId!,
          );
        });
  }
}
