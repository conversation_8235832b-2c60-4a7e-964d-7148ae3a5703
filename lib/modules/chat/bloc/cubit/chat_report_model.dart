import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_report_model/chat_report_model.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_report_model/improvement_list.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_report_model/polish_english.dart';
import 'package:flutter_app_kouyu/common/http/models/custome_scene_model/get_share_image_model.dart';
import 'package:flutter_app_kouyu/common/utils/local_audio_player.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';
import 'package:flutter_app_kouyu/widgets/share-widget/utils.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';

enum ReportDetailType {
  //1 创建  2 查看
  create,
  view,
}

class ChatReportNotifier extends ChangeNotifier {
  // 页面截图控制器
  final ScreenshotController screenshotController = ScreenshotController();
  
  final num conversationId;
  //1 创建  2 查看
  final ReportDetailType type;
  ChatReportModel? model;
  Map<String, String?> translateMap = {};
  final FreeTalkRepository freeTalkRepository = FreeTalkRepository();
  List<String> collectedList = [];

  ChatReportNotifier({required this.conversationId, required this.type}) {
    requestData();
  }

  void requestData() async {
    if (type == ReportDetailType.create) {
      EasyLoading.show(status: "生成报告中...");
      try {
        await Api.generateConversationReport(
            {"conversation_id": conversationId}).then((value) {
          model = value;
          notifyListeners();
        });

        // 播放音效
        LocalAudioPlayer.playLocalAudio(2);
      } finally {
        EasyLoading.dismiss();
      }
    } else {
      Api.getConversationReport({"conversation_id": conversationId})
          .then((value) {
        model = value;

        notifyListeners();
      });
    }
  }

  translate(String? text) {
    if (text == null) {
      return;
    }
    freeTalkRepository.translate(text).then((value) {
      translateMap[text] = value.data;
      notifyListeners();
    });
  }

  collection(String? text) async {
    await freeTalkRepository.collectionSentence(
        text, "conversation_report", "$conversationId");
    if (text != null) {
      collectedList.add(text);
    }
    notifyListeners();
  }

  cancelCollection(String? text) async {
    await freeTalkRepository.cancelCollectionSentence(text);
    if (text != null) {
      collectedList.remove(text);
    }
    notifyListeners();
  }


  /// 获取分享图
  Future<List<String>> getShareImageUrls() async {
    final serverUrls = await getShareImageFromServer();
    final screenshotUrl = await getShareImageFromScreenshot();
    return [screenshotUrl, ...serverUrls];
  }

  /// 从截图获取分享图
  Future<String> getShareImageFromScreenshot() async {
    final screenshotUrl = await captureAndSaveScreenshot(screenshotController, "chat_report");
    return screenshotUrl;

    // todo 合并二维码
  }

  /// 从网络获取分享图
  Future<List<String>> getShareImageFromServer() async {
    GetShareImageModel result = await Api.getShareImageList(conversationId, "chat_report");

    if (result.data != null && result.data!.isNotEmpty) {
      return result.data!.map((e) => e.poster_url ?? "").toList();
    }
    return []; 
  }
}
