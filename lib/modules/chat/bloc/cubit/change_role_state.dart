// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'change_role_cubit.dart';

class ChangeRoleState extends Equatable {
  const ChangeRoleState(
      {this.roleListModel, this.selectdData, this.currentData, this.index});
  final RoleListModel? roleListModel;
  final Data? selectdData;
  final Data? currentData;
  final int? index;
  @override
  List<Object?> get props => [roleListModel, selectdData, currentData, index];

  ChangeRoleState copyWith(
      {RoleListModel? roleListModel,
      Data? selectdData,
      Data? currentData,
      int? index}) {
    return ChangeRoleState(
        roleListModel: roleListModel ?? this.roleListModel,
        selectdData: selectdData ?? this.selectdData,
        currentData: currentData ?? this.currentData,
        index: index ?? this.index);
  }
}
