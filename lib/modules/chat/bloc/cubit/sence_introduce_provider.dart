// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/custome_scene_model/scene_detail_v2_model.dart';
import 'package:fluttertoast/fluttertoast.dart';

// 默认的举报原因列表
List<String> DefaultReportReasonList = [
  '有害/不安全',
  '色情低俗',
  '不适宜内容',
  '违法违规',
  '侵权',
  '其他',
];

class SenceIntroduceProvider extends ChangeNotifier {
  String topicCode;
  SceneDetailV2Model? sceneDetailV2Model;

  List<String> reportReasonList = DefaultReportReasonList;
  // 举报原因
  String reportReason = DefaultReportReasonList[0];
  // 原因描述
  String reportDescription = '';

  bool canStudy;

  SenceIntroduceProvider({
    required this.topicCode,
    required this.canStudy,
    this.sceneDetailV2Model,
  });

  refreshData() {
    getRebackReasonList();
    Api.getSceneDetailV2(topicCode).then((value) {
      sceneDetailV2Model = value;
      notifyListeners();
    });
  }

  // 收藏
  collect() {
    final isCollect = sceneDetailV2Model?.data?.hasCollect == 1 ? false : true;
    Api.collectScene(topicCode, isCollect).then((value) {
      sceneDetailV2Model?.data?.hasCollect = isCollect ? 1 : 0;
      notifyListeners();
    });
  }

  // 获取举报原因列表
  getRebackReasonList() {
    Api.getSceneRebackReasonList().then((value) {
      reportReasonList = value.data ?? DefaultReportReasonList;
      reportReason = reportReasonList[0];
      notifyListeners();
    });
  }

  // 举报
  report() {
    final reason = '$reportReason $reportDescription';
    Api.rebackScene(topicCode, reason).then((value) {
      Fluttertoast.showToast(msg: '举报成功', gravity: ToastGravity.CENTER);
    });
  }

  // 删除
  delete(BuildContext context) {
    final id = sceneDetailV2Model?.data?.id;
    Api.deleteCustomScene(id!).then((value) {
      print('删除成功');
      Navigator.of(context).popUntil((route) => route.isFirst);
    });
  }

  // 选择举报原因
  selectReportReason(String reason) {
    reportReason = reason;
    notifyListeners();
  }
}
