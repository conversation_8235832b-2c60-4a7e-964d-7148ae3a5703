import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/modules/my/bloc/cubit/my_page_cubit.dart';
import 'package:flutter_app_kouyu/my_app.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_page_provider.dart';
import 'package:provider/provider.dart';

class HomeBannerWidget extends StatelessWidget {
  const HomeBannerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ChatPageModel>(
      builder: (context, provider, _) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 24.w),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 316.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(32.w),
                    color: const Color(0xFFFFF3E0),
                  ),
                  child: CarouselSlider(
                    options: CarouselOptions(
                      height: 316.w,
                      // 如果轮播图数量大于1，则开启无限轮播
                      enableInfiniteScroll: (provider.carouselListModel?.data?.length ?? 0) > 1,
                      autoPlay: (provider.carouselListModel?.data?.length ?? 0) > 1,
                      viewportFraction: 1.0,
                      autoPlayInterval: const Duration(seconds: 3),
                    ),
                    items: provider.carouselListModel?.data?.map((item) {
                          return GestureDetector(
                            onTap: () {
                              if (item.type == 'member_recharge') {
                                Navigator.of(context)
                                    .pushNamed('/open_vip')
                                    .then((value) => context
                                        .read<MyPageCubit>()
                                        .initUserInfo());
                              }
                            },
                            child: Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(32.w),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(32.w),
                                child: Image.network(
                                  item.imageUrl ?? "",
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          );
                        }).toList() ??
                        [],
                  ),
                ),
              ),
              SizedBox(width: 20.w),
              Expanded(
                child: Column(
                  children: [
                    _buildFunctionItem(
                      "雅思模考",
                      "真人模拟考试",
                      "images/ielts_exam.png",
                      "images/home_ielts_bg.png",
                      onTap: () {
                        myHomePageKey.currentState?.changeIeltsPage();
                      },
                    ),
                    SizedBox(height: 20.w),
                    _buildFunctionItem(
                      "学习工具",
                      "趣味英语学习",
                      "images/study_tools.png",
                      "images/home_study_bg.png",
                      onTap: () {
                        myHomePageKey.currentState?.changeLeanToolsPage();
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFunctionItem(
      String title, String subtitle, String iconPath, String backgroundImage,
      {VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 148.w,
        padding: EdgeInsets.fromLTRB(32.w, 26.w, 0.w, 26.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(32.w),
          image: DecorationImage(
            image: AssetImage(backgroundImage),
            fit: BoxFit.cover,
          ),
        ),
        child: Row(
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 32.sp,
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF272733),
                  ),
                ),
                SizedBox(height: 4.w),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF748A99),
                  ),
                ),
              ],
            ),
            SizedBox(width: 12.w),
            Image.asset(
              iconPath,
              width: 120.w,
              height: 120.w,
            ),
          ],
        ),
      ),
    );
  }
}
