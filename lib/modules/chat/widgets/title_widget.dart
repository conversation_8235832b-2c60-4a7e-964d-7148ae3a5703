// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gif_view/gif_view.dart';

import 'package:flutter_app_kouyu/common/http/models/user_info_model/user_info_model.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_page_provider.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';

class TitleWidget extends StatelessWidget {
  final UserInfoModel? userInfoModel;
  const TitleWidget({
    super.key,
    required this.userInfoModel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsetsDirectional.only(start: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    "Hi,${userInfoModel?.data?.nickname ?? "_"}",
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                      fontSize: 40.sp,
                      color: const Color(0xFF272733),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Visibility(
                    visible: userInfoModel?.data?.memberType == 1,
                    child: Image.asset(
                      "images/vip.png",
                      width: 86.w,
                      height: 40.w,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 19),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  Navigator.of(context)
                      .pushNamed('/change_teacher')
                      .then((value) {
                    context.read<ChatPageModel>().updateUserInfo();
                    ;
                  });
                },
                child: Row(
                  children: [
                    Text(
                      '我是你的口语老师',
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF4E5766),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Image.asset(
                      "images/chat_change_teacher.png",
                      height: 12,
                      width: 12,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '切换',
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF2693FF),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16.w),
              Text(
                userInfoModel?.data?.userSettings?.characterName ?? "_",
                style: TextStyle(
                  fontSize: 80.sp,
                  fontWeight: FontWeight.w900,
                  color: const Color(0xFF272733),
                ),
              ),
              SizedBox(height: 16.w),
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: const Color(0xFFCEDDE0),
                        width: 1.w,
                      ),
                      borderRadius: BorderRadius.circular(12.w),
                    ),
                    child: Text(
                      userInfoModel
                              ?.data?.userSettings?.characterEnglishVariation ??
                          "_",
                      style: TextStyle(
                        fontSize: 20.sp,
                        color: const Color(0xFF272733),
                      ),
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 4.w),
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: const Color(0xFFCEDDE0),
                        width: 1.w,
                      ),
                      borderRadius: BorderRadius.circular(12.w),
                    ),
                    child: Text(
                      userInfoModel?.data?.userSettings?.characterMbti ?? "_",
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF272733),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16.w),
              GestureDetector(
                onTap: () {
                  CommonUtils.playVideo(
                      userInfoModel?.data?.userSettings?.characterAudioUrl);
                },
                child: Container(
                  width: 191.w,
                  height: 64.w,
                  padding: EdgeInsets.only(left: 14.w, right: 14.w, top: 12.w, bottom: 12.w),
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.w),
                    color: const Color.fromRGBO(255, 255, 255, 0.9),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      PlayButtonWidget(
                        style: PlayButtonStyle.opacity,
                        audioUrl:
                          userInfoModel?.data?.userSettings?.characterAudioUrl,
                        bgColor: Colors.transparent,
                        image: Image.asset(
                          "images/say_hello_staic.png",
                          fit: BoxFit.fill,
                          width: 36.w,
                          height: 36.w,
                        ),
                        gifImage: GifView.asset(
                          "images/say_hello_staic.gif",
                          fit: BoxFit.fill,
                          width: 36.w,
                          height: 36.w,
                          repeat: ImageRepeat.repeat,
                        ),
                      ),
                      Text('Say Hello', style: style_1_24)
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
