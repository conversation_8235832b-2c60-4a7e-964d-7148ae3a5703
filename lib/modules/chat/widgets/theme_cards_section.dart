import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_page_provider.dart';
import 'package:flutter_app_kouyu/my_app.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class ThemeCardsSection extends StatelessWidget {
  const ThemeCardsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '本周限免主题',
                style: TextStyle(
                  color: const Color(0xFF272733),
                  fontWeight: FontWeight.w700,
                  fontSize: 36.sp,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  // 切换到场景学习tab
                  myHomePageKey.currentState?.changeSceneStudyPage();
                },
                child: Text(
                  '查看更多',
                  style: style_1_24,
                ),
              ),
            ],
          ),
          SizedBox(height: 24.w),
          SizedBox(
            height: 366.w,
            child: Consumer<ChatPageModel>(
              builder: (context, provider, _) {
                return LayoutBuilder(
                  builder: (context, constraints) {
                    double cardWidth = (constraints.maxWidth - 40.w) / 3;
                    return ListView(
                      scrollDirection: Axis.horizontal,
                      children: provider.weeklyFreeTopicListModel?.data
                              ?.map((item) => Row(
                                    children: [
                                      _buildThemeCard(
                                        context,
                                        item.topicCode ?? "",
                                        item.chinese ?? "",
                                        "${item.usageCount ?? 0}人使用·${item.courseLevelName ?? "初级"}",
                                        cardWidth,
                                        imageUrl: item.imageUrl,
                                      ),
                                      SizedBox(width: 20.w),
                                    ],
                                  ))
                              .toList() ??
                          [],
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeCard(BuildContext context, String topicCode, String title,
      String subtitle, double width,
      {String? imageUrl}) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamed('/sence_introduce',
            arguments: {"topic_code": topicCode});
      },
      child: Container(
        width: width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: [
                Container(
                  height: 260.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    image: DecorationImage(
                      image: NetworkImage(imageUrl ?? ""),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Positioned(
                  left: 12.w,
                  top: 12.w,
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.w),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12.w),
                    ),
                    child: Text(
                      '本周限免',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20.sp,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: const Color(0xFF272733),
                      fontWeight: FontWeight.w700,
                      fontSize: 28.sp,
                    ),
                  ),
                  SizedBox(height: 8.w),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: const Color(0xFF748A99),
                      fontSize: 20.sp,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
