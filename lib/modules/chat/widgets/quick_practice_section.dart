import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_page_provider.dart';
import 'package:provider/provider.dart';

class QuickPracticeSection extends StatelessWidget {
  const QuickPracticeSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 40.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '快速练习',
            style: TextStyle(
              fontSize: 36.sp,
              fontWeight: FontWeight.w700,
              color: const Color(0xFF272733),
            ),
          ),
          SizedBox(height: 24.w),
          Consumer<ChatPageModel>(
            builder: (context, provider, _) {
              return ListView(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: provider.studyProgressListModel?.data
                        ?.map((item) => Column(
                              children: [
                                _buildPracticeCard(
                                  context,
                                  item.code ?? "",
                                  item.englishName ?? "",
                                  item.name ?? "",
                                  item.description ?? "",
                                  status: _getStatusText(
                                    item.isCompleted ?? 0,
                                    item.completedCount ?? 0,
                                    item.totalCount ?? 0,
                                  ),
                                  statusColor: _getStatusColor(
                                    item.isCompleted ?? 0,
                                    item.completedCount ?? 0,
                                  ),
                                  imageUrl: item.imageUrl,
                                ),
                                SizedBox(height: 20.w),
                              ],
                            ))
                        .toList() ??
                    [],
              );
            },
          ),
        ],
      ),
    );
  }

  String _getStatusText(int isCompleted, int completedCount, int totalCount) {
    if (isCompleted == 1) {
      return "今日已学习";
    } else {
      if (completedCount == 0) {
        return "未学习";
      } else {
        return "进度：$completedCount/$totalCount";
      }
    }
  }

  Color _getStatusColor(int isCompleted, int completedCount) {
    if (isCompleted == 1) {
      return Colors.green;
    } else {
      return completedCount == 0 ? Colors.grey : Colors.blue;
    }
  }

  Widget _buildPracticeCard(BuildContext context, String code,
      String englishTitle, String chineseTitle, String description,
      {required String status, required Color statusColor, String? imageUrl}) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context)
            .pushNamed('/quick_study',
                arguments: code) // 直接传递code字符串，不需要转换为枚举
            .then((value) {
              context.read<ChatPageModel>().getstudyProgressList();
            });
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              width: 100,
              height: 120,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                image: imageUrl != null
                    ? DecorationImage(
                        image: NetworkImage(imageUrl),
                        fit: BoxFit.cover,
                      )
                    : null,
                color: imageUrl == null ? Colors.grey[200] : null,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: SizedBox(
                height: 120,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 4),
                        Text(
                          englishTitle,
                          style: TextStyle(
                            color: const Color(0xFF748A99),
                            fontWeight: FontWeight.w300,
                            fontSize: 24.sp,
                          ),
                        ),
                        SizedBox(height: 8.w),
                        Text(
                          chineseTitle,
                          style: TextStyle(
                            color: const Color(0xFF272733),
                            fontWeight: FontWeight.w700,
                            fontSize: 30.sp,
                          ),
                        ),
                        SizedBox(height: 8.w),
                        Text(
                          description,
                          style: TextStyle(
                            color: const Color(0xFF4E5766),
                            fontWeight: FontWeight.w500,
                            fontSize: 26.sp,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      status,
                      style: TextStyle(
                        color: statusColor,
                        fontSize: 24.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
