import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_app_kouyu/modules/chat/view/free_talk_page.dart';
import 'package:flutter_app_kouyu/common/in_app_purchase/cubit/in_app_purchase_cubit.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChatWidget extends StatelessWidget {
  const ChatWidget({super.key});

  @override
  Widget build(BuildContext context) {
    context.read<InAppPurchaseCubit>().listen();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 40.w),
      child: SizedBox(
        height: 148.w,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(32.w),
            image: const DecorationImage(
              image: AssetImage("images/home_chat_bg.png"),
              fit: BoxFit.cover,
            ),
          ),
          padding: EdgeInsets.symmetric(horizontal: 32.w),
          child: Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '零基础自由聊',
                    style: TextStyle(
                        fontSize: 32.sp,
                        fontWeight: FontWeight.w700,
                        color: const Color(0xFFFFFFFF)),
                  ),
                  SizedBox(
                    height: 4.w,
                  ),
                  Text('每天10分钟，练出好口语',
                      style: TextStyle(
                          fontSize: 24.sp, color: const Color(0xFFFFFFFF))),
                ],
              ),
              const Spacer(),
              Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context)
                          .pushNamed('/free_talk',
                              arguments: const TalkArgument(model: 1))
                          .then((value) {});
                    },
                    child: Image.asset(
                      "images/home_teach_chat.png",
                      width: 151.w,
                      fit: BoxFit.fitWidth,
                    ),
                  ),
                  SizedBox(
                    width: 12.w,
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context)
                          .pushNamed('/free_talk',
                              arguments: const TalkArgument(model: 0))
                          .then((value) {});
                    },
                    child: Image.asset(
                      "images/home_free_chat.png",
                      width: 151.w,
                      fit: BoxFit.fitWidth,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
