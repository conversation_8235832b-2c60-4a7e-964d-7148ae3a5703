import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/quick_study_model/quick_study_result_model.dart';
import 'package:flutter_app_kouyu/common/utils/local_audio_player.dart';
import 'package:fluttertoast/fluttertoast.dart';

class StudyResultPageProvider extends ChangeNotifier {
  // 快速学习练习接口的返回结果
  QuickStudyResultModel? quickStudyResultModel;

  // 快速练习类型默认类型
  String type = "vocabulary";
  // 是否正在加载
  bool isLoading = false;

  loadData(String type, int particeId) async {
    try {
      this.type = type;
      isLoading = true;
      notifyListeners();
      quickStudyResultModel =
          await Api.getQuickStudyResult(type, particeId);

      // 播放音效
      LocalAudioPlayer.playLocalAudio(2);
      // mock 数据
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  void collectAllWords() {
    // 批量收藏
    final textList = quickStudyResultModel?.data?.vocabularies
            ?.map((e) => e.word ?? '')
            .toList() ??
        [];
    Api.batchCollect(textList.where((word) => word.isNotEmpty).toList())
        .then((value) {
      if (value.code == '1') {
        Fluttertoast.showToast(msg: '收藏成功', gravity: ToastGravity.CENTER);
        // 所有单词收藏状态更新
        quickStudyResultModel?.data?.vocabularies?.forEach((element) {
          element.isCollected = true;
        });
        notifyListeners();
      }
    });
  }

  ///批量收藏短语动词
  void batchCollectPhrasalVerb() {
    final phrasalVerbs = quickStudyResultModel?.data?.vocabularies;
    final idList = phrasalVerbs?.map((e) => e.id ?? 0).toList() ?? [];
    Api.batchCollectPhrasalVerb(idList)
        .then((value) {
      if (value.code == '1') {
        Fluttertoast.showToast(msg: '收藏成功', gravity: ToastGravity.CENTER);
        // 所有单词收藏状态更新
        quickStudyResultModel?.data?.vocabularies?.forEach((element) {
          element.isCollected = true;
        });
        notifyListeners();
      }
    });
  }

  void collectWord(String word) {
    ///print('collectWord: $word');
    // 如果已经收藏, 则取消收藏
    final vocabularies = quickStudyResultModel?.data?.vocabularies;
    final item = vocabularies?.firstWhere((e) => e.word == word);
    final isCollected = item?.isCollected ?? false;
    if (isCollected) {
      item?.isCollected = false;
      // 取消收藏
      Api.cancelCollectVocabulary(word);
    } else {
      item?.isCollected = true;
      // 收藏
      Api.collectVocabulary(word);
    }
    notifyListeners();
  }

  
  ///收藏短语动词
  
  void collectPhrasalVerb(int phrasalVerbId) {
    // 如果已经收藏, 则取消收藏
    final phrasalVerbs = quickStudyResultModel?.data?.vocabularies;
    final item = phrasalVerbs?.firstWhere((e) => e.id == phrasalVerbId);
    final isCollected = item?.isCollected ?? false;
    if (isCollected) {
      item?.isCollected = false;
      // 取消收藏
      Api.cancelCollectPhrasalVerb(phrasalVerbId);
    } else {
      item?.isCollected = true;
      // 收藏
      Api.collectPhrasalVerb(phrasalVerbId);
    }
    notifyListeners();
  }


  void collectCollocation(int collocationId) {
    // 如果已经收藏, 则取消收藏
    final collocations = quickStudyResultModel?.data?.vocabularies;
    final item = collocations?.firstWhere((e) => e.id == collocationId);
    final isCollected = item?.isCollected ?? false;
    if (isCollected) {
      item?.isCollected = false;
      // 取消收藏
      Api.cancelCollectCollocation(collocationId);
    } else {
      item?.isCollected = true;
      // 收藏
      Api.collectCollocation(collocationId);
    }
    notifyListeners();
  }

  void batchCollectCollocation() {
    final collocations = quickStudyResultModel?.data?.vocabularies;
    final idList = collocations?.map((e) => e.id ?? 0).toList() ?? [];
    Api.batchCollectCollocation(idList)
        .then((value) {
      if (value.code == '1') {
        Fluttertoast.showToast(msg: '收藏成功', gravity: ToastGravity.CENTER);
        // 所有单词收藏状态更新
        quickStudyResultModel?.data?.vocabularies?.forEach((element) {
          element.isCollected = true;
        });
        notifyListeners();
      }
    });
  }
}