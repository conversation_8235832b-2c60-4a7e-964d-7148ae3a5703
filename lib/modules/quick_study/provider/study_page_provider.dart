import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/quick_study_model/quick_study_question_model.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/common/utils/local_audio_player.dart';
import 'package:flutter_app_kouyu/modules/quick_study/pages/study_result_page.dart';
import 'package:fluttertoast/fluttertoast.dart';

// 填充适配符, 遇到这个符号, 需要根据正确答案填充
const CORRECT_ANSWER_HOLDER = 'CORRECT_ANSWER_HOLDER';

// 反馈的难度列表
const REBACK_DIFFICULTY_LIST = [
  {'desc': '轻松搞定😉', 'code': 1},
  {'desc': '正合我意😊', 'code': 2},
  {'desc': '有点吃力😅', 'code': 3},
];

// 练习题
class PracticeItem {
  // 练习ID
  int practiceId;
  // 题目ID
  int questionId;
  // 问题内容列表
  List<String> questionContentList;
  // 完整的问题
  String fullQuestion = '';
  // 正确答案
  String correctAnswer;
  // 答案列表
  List<String> answerList;
  // 翻译文案数组
  List<String> translationContentList;
  // 正确答案的翻译文案
  String correctAnswerTranslation;

  // 是否已经收藏
  bool isCollected = false;
  // 是否已经回答
  bool isAnswered = false;
  // 是否回答正确
  bool isCorrect = false;
  // 选择的答案
  String selectedAnswer = '';

  PracticeItem({
    required this.practiceId,
    required this.questionId,
    required this.questionContentList,
    required this.correctAnswer,
    required this.answerList,
    required this.translationContentList,
    required this.correctAnswerTranslation,
  }) {
    // 组合得到完整的问题, 遇到 CORRECT_ANSWER_HOLDER 则用正确答案填充
    fullQuestion = questionContentList
        .map((e) => e == CORRECT_ANSWER_HOLDER ? correctAnswer : e)
        .join(' ');
  }
}

class StudyPageProvider extends ChangeNotifier {
  // 快速学习练习接口的返回结果
  QuickStudyQuestionModel? quickStudyQuestionModel;
  // 快速练习类型改为String
  String type = "vocabulary";  // 默认值设为vocabulary
  // 当前正在回答的题目
  int currentQuestionIndex = 0;
  // 总题目数量
  int totalQuestionNum = 0;
  // 当前正在回答的题目
  PracticeItem currentPracticeItem = PracticeItem(
    practiceId: 0,
    questionId: 0,
    questionContentList: [],
    correctAnswer: '',
    answerList: [],
    translationContentList: [],
    correctAnswerTranslation: '',
  );

  // 练习列表
  List<PracticeItem> practiceItemList = [];

  // 是否正在初始化数据
  bool isLoading = false;

  // 选择的难度
  int difficultyCode = REBACK_DIFFICULTY_LIST[1]['code'] as int;

  loadData(String practiceType) async {
    try {
      type = practiceType;
      isLoading = true;
      notifyListeners();
      quickStudyQuestionModel = await Api.getQuickStudyPracticeList(practiceType);
      practiceItemList = quickStudyQuestionModel?.data
              ?.map((e) => PracticeItem(
                    practiceId: e.practiceRoundId!,
                    questionId: e.id!,
                    questionContentList: e.questionArray!,
                    correctAnswer: e.correctAnswer!,
                    answerList: e.answers!,
                    translationContentList: e.translationArray!,
                    correctAnswerTranslation: e.correctAnswerTranslation!,
                  )..isAnswered = e.isFinish == 1)
              .toList() ??
          [];
      totalQuestionNum = practiceItemList.length;
      // 自动选择第一个还没有回答的题目
      final nextItem = findNextUnansweredQuestion();
      if (nextItem == null) {
        currentPracticeItem = practiceItemList.last;
      } else {
        currentPracticeItem = nextItem;
      }
      currentQuestionIndex = practiceItemList.indexOf(currentPracticeItem) + 1;
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  void checkAnswer(String answer) {
    if (currentPracticeItem.isAnswered) return;
    currentPracticeItem.isAnswered = true;
    currentPracticeItem.selectedAnswer = answer;
    // 检查答案是否正确
    if (currentPracticeItem.correctAnswer == answer) {
      currentPracticeItem.isCorrect = true;
    } else {
      currentPracticeItem.isCorrect = false;
      // 回答错误的情况下, 需要将错题重新加入到练习列表中
      practiceItemList.add(PracticeItem(
        practiceId: currentPracticeItem.practiceId,
        questionId: currentPracticeItem.questionId,
        questionContentList: currentPracticeItem.questionContentList,
        correctAnswer: currentPracticeItem.correctAnswer,
        answerList: currentPracticeItem.answerList,
        translationContentList: currentPracticeItem.translationContentList,
        correctAnswerTranslation: currentPracticeItem.correctAnswerTranslation,
      ));
      totalQuestionNum = practiceItemList.length;
    }

    // 播放音效
    LocalAudioPlayer.playLocalAudio(currentPracticeItem.isCorrect ? 4 : 3);

    // 上报答案
    Api.quickStudyAnswer(type, answer, currentPracticeItem.questionId);

    notifyListeners();
  }

  void nextQuestion(BuildContext context) {
    final nextItem = findNextUnansweredQuestion();

    if (nextItem == null) {
      Navigator.of(context).push(
        StudyResultPage.route(
          practiceId: currentPracticeItem.practiceId,
          type: type,
        ),
      );
      return;
    }
    currentPracticeItem = nextItem;
    currentQuestionIndex = practiceItemList.indexOf(nextItem) + 1;
    notifyListeners();
  }

  // 找到下一个还没有回答的题目
  PracticeItem? findNextUnansweredQuestion() {
    for (var item in practiceItemList) {
      if (!item.isAnswered) {
        return item;
      }
    }
    return null;
  }

  /// 选择难度
  void selectDifficulty(int code) {
    difficultyCode = code;
    notifyListeners();
  }

  void setDifficulty() {
    // 反馈难度
    Api.rebackDifficulty(type, difficultyCode);
    // 反馈成功 toast
    Fluttertoast.showToast(
      msg: '反馈成功',
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.CENTER,
      timeInSecForIosWeb: 1,
    );
  }

  void collectQuestion() async {
    if (currentPracticeItem.isCollected) {
      // 取消收藏
      await Api.cancelCollectVocabulary(currentPracticeItem.fullQuestion);
      currentPracticeItem.isCollected = false;
    } else {
      // 收藏题目
      await Api.collectVocabulary(currentPracticeItem.fullQuestion);
      currentPracticeItem.isCollected = true;
    }
    notifyListeners();
  }
}
