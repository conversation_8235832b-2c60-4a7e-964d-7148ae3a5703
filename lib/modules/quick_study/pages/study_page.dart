import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../widgets/common_widget.dart';
import '../provider/study_page_provider.dart';

class StudyPage extends StatelessWidget {
  // 快速练习类型
  final String type;

  static Route<void> route({required String type}) {
    return MaterialPageRoute<void>(builder: (_) => StudyPage(type: type));
  }

  const StudyPage({super.key, required this.type});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => StudyPageProvider()..loadData(type),
      child: Consumer<StudyPageProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            // 显示加载指示器, 页面白色背景
            return Container(
              color: Colors.white, // 设置背景为白色
              child: const Center(
                // 背景蓝色
                child: CircularProgressIndicator(
                  color: Color(0xFF2693FF),
                ), // 加载指示器
              ),
            );
          }
          return Stack(
            children: [
              Container(
                width: double.infinity,
                height: double.infinity,
                color: const Color(0xFFEDF6F7),
                // 顶部有背景图片, 图片高度为100, 宽度为屏幕宽度, 位置在顶部
                child: Padding(
                  padding: EdgeInsets.only(top: 0.w),
                  child: Align(
                    alignment: Alignment.topCenter,
                    child: Image.asset(
                      "images/quick_study_bg.png",
                      width: double.infinity,
                      height: 300.w,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
              Scaffold(
                backgroundColor: Colors.transparent,
                floatingActionButtonLocation:
                    FloatingActionButtonLocation.centerDocked,
                body: Padding(
                  padding: EdgeInsets.only(
                    top: 100.w, // 顶部留出100.w的空间
                  ),
                  child: Column(
                    children: [
                      _buildTopBar(context, provider),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              _questionSection(context, provider),
                            ],
                          ),
                        ),
                      ),
                      _bottomTip(context, provider),
                    ],
                  ),
                ),
              )
            ],
          );
        },
      ),
    );
  }
}

Widget _buildTopBar(BuildContext context, StudyPageProvider provider) {
  return Padding(
    padding: const EdgeInsets.all(8.0),
    child: Row(
      children: [
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => showDialog(
              context: context,
              builder: (context) => _exitPracticeDialog(context, provider)),
        ),
        Expanded(
          child: LinearProgressIndicator(
            value: provider.currentQuestionIndex /
                provider.totalQuestionNum, // 这里可以通过provider控制进度
            backgroundColor: Colors.white,
            valueColor: const AlwaysStoppedAnimation<Color>(
              Color(0xFF84E9FF),
            ),
            minHeight: 20.w,
            // 圆角
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: () {
            // 弹出难度反馈弹窗
            _showDifficultyFeedback(context, provider);
          },
        ),
      ],
    ),
  );
}

Widget _questionSection(BuildContext context, StudyPageProvider provider) {
  return Padding(
    padding: EdgeInsets.all(32.w),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min, // 添加这行
      children: [
        _questionShowContent(context, provider),
        SizedBox(height: 24.w),
        _answerOptions(context, provider),
      ],
    ),
  );
}

// 问题内容展示组件
Widget _questionShowContent(BuildContext context, StudyPageProvider provider) {
  return Container(
    width: double.infinity,
    height: 600.w,
    // 圆角
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(24),
      color: Colors.white,
    ),
    child: Padding(
      padding: EdgeInsets.all(32.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // Container(
          //   alignment: Alignment.topRight,
          //   child: _collectButton(context, provider),
          // ),
          SizedBox(height: 20.w),
          Expanded(
            child: Column(
              children: [
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    children: _buildTextSpans(provider.currentPracticeItem),
                  ),
                ),
                const Spacer(), // 添加这个将推动翻译文案到底部
                Padding(
                  padding: EdgeInsets.only(bottom: 10.w),
                  child: RichText(
                    text: TextSpan(
                      // 自动换行
                      children: _buildTranslationTextSpans(
                          provider.currentPracticeItem),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _collectButton(BuildContext context, StudyPageProvider provider) {
  // 一个收藏按钮, 有border, 左边是收藏图标, 右边是收藏文案, 点击后调用provider的collectQuestion方法
  final isCollected = provider.currentPracticeItem.isCollected;
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
    decoration: BoxDecoration(
      // 带透明度的灰色
      border: Border.all(
          color: isCollected
              ? const Color(0xFF2693FF)
              : const Color.fromRGBO(0, 0, 0, 0.12)),
      borderRadius: BorderRadius.circular(8),
    ),
    child: GestureDetector(
      onTap: () {
        provider.collectQuestion();
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(isCollected ? Icons.favorite : Icons.favorite_border,
              size: 12,
              color: isCollected ? const Color(0xFF2693FF) : Colors.grey),
          const SizedBox(width: 4),
          Text(isCollected ? '已收藏' : '收藏',
              style: TextStyle(
                  fontSize: 12,
                  color: isCollected ? const Color(0xFF2693FF) : Colors.grey)),
        ],
      ),
    ),
  );
}

// 添加一个辅助方法来构建 TextSpan 列表
List<InlineSpan> _buildTextSpans(PracticeItem item) {
  List<InlineSpan> spans = [];

  for (var content in item.questionContentList) {
    if (content == CORRECT_ANSWER_HOLDER) {
      spans.add(
        TextSpan(
          text: (item.isAnswered && item.isCorrect) ? item.correctAnswer : '______',
          style: TextStyle(
            fontSize: 46.sp,
            color:
                (item.isAnswered && item.isCorrect) ? const Color(0xFF2693FF) : const Color.fromRGBO(65, 192, 255, 0),
            fontWeight: FontWeight.w500,
            backgroundColor: const Color.fromRGBO(65, 192, 255, 0.12),
          ),
        ),
      );
    } else {
      spans.add(
        TextSpan(
          text: content,
          style: TextStyle(
            fontSize: 46.sp,
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }

    // 在每个片段后添加一个小空格，改善视觉效果
    spans.add(const TextSpan(text: ' '));
  }

  return spans;
}

// 添加一个辅助方法来构建 TextTranslationSpan 列表
List<InlineSpan> _buildTranslationTextSpans(PracticeItem item) {
  List<InlineSpan> spans = [];

  for (var content in item.translationContentList) {
    if (content == CORRECT_ANSWER_HOLDER) {
      spans.add(
        TextSpan(
          text: item.correctAnswerTranslation,
          style: TextStyle(
            fontSize: 28.sp,
            color: const Color(0xFF2693FF),
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    } else {
      spans.add(
        TextSpan(
          text: content,
          style: TextStyle(
            fontSize: 28.sp,
            color: Colors.grey,
          ),
        ),
      );
    }

    // 在每个片段后添加一个小空格，改善视觉效果
    // spans.add(const TextSpan(text: ' '));
  }

  return spans;
}

Widget _answerOptions(BuildContext context, StudyPageProvider provider) {
  final options = provider.currentPracticeItem.answerList;

  // 一个根据选项是否正确, 返回颜色的函数
  Color _getOptionBorderColor(String option) {
    // 如果还未回答, 则返回透明
    if (!provider.currentPracticeItem.isAnswered) return Colors.transparent;
    // 如果已回答, 则根据是否正确返回颜色
    if (provider.currentPracticeItem.isCorrect &&
        provider.currentPracticeItem.selectedAnswer == option) {
      return const Color.fromRGBO(0, 181, 120, 1);
    }
    if (!provider.currentPracticeItem.isCorrect &&
        provider.currentPracticeItem.selectedAnswer == option) {
      return const Color.fromRGBO(250, 81, 81, 1);
    }
    return Colors.transparent;
  }

  // 一个根据选项是否正确, 返回颜色的函数
  Color _getOptionTextColor(String option) {
    if (!provider.currentPracticeItem.isAnswered) return Colors.black;
    if (provider.currentPracticeItem.isCorrect &&
        provider.currentPracticeItem.selectedAnswer == option) {
      return const Color.fromRGBO(0, 181, 120, 1);
    }
    if (!provider.currentPracticeItem.isCorrect &&
        provider.currentPracticeItem.selectedAnswer == option) {
      return const Color.fromRGBO(250, 81, 81, 1);
    }
    return Colors.black;
  }

  return Column(
    children: options.map((option) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 0, vertical: 12.w),
        child: InkWell(
          onTap: () => provider.checkAnswer(option),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              border:
                  Border.all(color: _getOptionBorderColor(option), width: 0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                option,
                style: TextStyle(
                  color: _getOptionTextColor(option),
                  fontSize: 32.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      );
    }).toList(),
  );
}

Widget _bottomTip(BuildContext context, StudyPageProvider provider) {
  final isAnswered = provider.currentPracticeItem.isAnswered;
  final isCorrect = provider.currentPracticeItem.isCorrect;
  final correctAnswer = provider.currentPracticeItem.correctAnswer;
  // 如果还未回答, 则不显示
  if (!isAnswered) return const SizedBox.shrink();

  return Container(
    padding: EdgeInsets.only(left: 32.w, right: 32.w, top: 32.w, bottom: 54.w),
    color: isCorrect
        ? const Color.fromRGBO(0, 181, 120, 0.1)
        : const Color.fromRGBO(250, 81, 81, 0.1),
    width: double.infinity,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      // 背景色
      children: [
        // 正确/错误图标和文字
        Row(
          children: [
            Icon(
              isCorrect ? Icons.check_circle : Icons.cancel,
              color:
                  isCorrect ? const Color(0xFF00B578) : const Color(0xFFFA5151),
              size: 40.w,
            ),
            SizedBox(width: 8.w),
            Text(
              isCorrect ? '正确' : '错误',
              style: TextStyle(
                color: isCorrect
                    ? const Color(0xFF00B578)
                    : const Color(0xFFFA5151),
                fontSize: 32.sp,
              ),
            ),
          ],
        ),
        SizedBox(height: 4.w),
        // 答案解释文本
        Text(
          isCorrect ? '恭喜你答对啦! 🎉' : 'Answer: $correctAnswer',
          style: TextStyle(
            color:
                isCorrect ? const Color(0xFF00B578) : const Color(0xFFFA5151),
            fontSize: 28.sp,
          ),
        ),
        SizedBox(height: 8.w),
        // 下一题/好的按钮
        SizedBox(
          width: double.infinity,
          height: 96.w,
          child: ElevatedButton(
            onPressed: () {
              provider.nextQuestion(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  isCorrect ? const Color(0xFF00B578) : const Color(0xFFFA5151),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              isCorrect ? '下一题' : '好的',
              style: TextStyle(
                color: Colors.white,
                fontSize: 32.sp,
              ),
            ),
          ),
        ),
      ],
    ),
  );
}

// 难度反馈组件
void _showDifficultyFeedback(BuildContext context, StudyPageProvider provider) {
  showModalBottomSheet(
    context: context,
    builder: (BuildContext context) {
      return ChangeNotifierProvider.value(
          value: provider,
          child: Consumer<StudyPageProvider>(builder: (context, provider, _) {
            return Container(
              padding: EdgeInsets.only(
                  left: 40.w, right: 40.w, top: 24.w, bottom: 50.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(24.w),
                    topRight: Radius.circular(24.w)),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text('练习反馈', style: style_1_36),
                      const Spacer(),
                      GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Padding(
                          padding: EdgeInsets.only(top: 24.w, bottom: 24.w, left: 24.w),
                          child: Image.asset('images/dialog_close.png',
                              width: 24.w, height: 24.w),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 40.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('课程难度怎么样?', style: style_1_28_400),
                      SizedBox(height: 16.w),
                      Wrap(
                          spacing: 16.w,
                          runSpacing: 16.w,
                          alignment: WrapAlignment.center,
                          runAlignment: WrapAlignment.start,
                          children: REBACK_DIFFICULTY_LIST
                              .map(
                                (item) => GestureDetector(
                                  onTap: () {
                                    provider
                                        .selectDifficulty(item['code'] as int);
                                  },
                                  child: Common_DialogSelectionItemWidget(
                                    text: item['desc'] as String,
                                    fontColor: item['code'] as int ==
                                            provider.difficultyCode
                                        ? const Color(0xff2693FF)
                                        : const Color(0xff272733),
                                    borderColor: item['code'] as int ==
                                            provider.difficultyCode
                                        ? const Color(0xff2693FF)
                                        : const Color(0xFFEDF6F7),
                                    bgColor: const Color(0xFFEDF6F7),
                                  ),
                                ),
                              )
                              .toList()),
                      SizedBox(height: 16.w),
                      SizedBox(height: 24.w),
                      Common_BottomConfirmButton(
                        text: '提交反馈',
                        textColor: Colors.white,
                        bgColor: const Color(0xFF2693FF),
                        splashColor: const Color(0xFF2693FF),
                        onTap: () {
                          provider.setDifficulty();
                          Navigator.of(context).pop();
                        },
                      ),
                    ],
                  )
                ],
              ),
            );
          }));
    },
  );
}

// 推出练习弹框组件
Widget _exitPracticeDialog(BuildContext context, StudyPageProvider provider) {
  final operationList = [
    {
      'title': '取消',
      'onTap': () {
        Navigator.of(context).pop();
      },
      'btnBorderColor': Colors.black,
      'btnColor': Colors.transparent,
      'fontColor': Colors.black,
    },
    {
      'title': '确认',
      'onTap': () {
        Navigator.of(context).popUntil((route) => route.isFirst);
      },
      'btnBorderColor': const Color(0xFF2693FF),
      'btnColor': const Color(0xFF2693FF),
      'fontColor': Colors.white,
    },
  ];
  return AlertDialog(
        backgroundColor: Colors.white,
        contentPadding: EdgeInsets.all(32.w),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('确定要退出练习吗？', style: style_1_28),
            SizedBox(height: 48.w),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: operationList
                  .map((e) => InkWell(
                        onTap: e['onTap'] as GestureTapCallback,
                        child: Container(
                          padding: EdgeInsets.only(
                              left: 74.w, right: 74.w, top: 20.w, bottom: 20.w),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.w),
                            color: e['btnColor'] as Color,
                            border: Border.all(
                              color: e['btnBorderColor'] as Color,
                            ),
                          ),
                          child: Text(e['title'] as String,
                              style: TextStyle(
                                fontSize: 28.sp,
                                fontWeight: FontWeight.w600,
                                color: e['fontColor'] as Color,
                              )),
                        ),
                      ))
                  .toList(),
            )
          ],
        ),
      );
}
