import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/modules/quick_study/pages/study_page.dart';
import 'package:flutter_app_kouyu/modules/quick_study/provider/study_result_page_provider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class StudyResultPage extends StatelessWidget {
  // 快速练习类型
  final String type;
  // 练习ID
  final int practiceId;

  const StudyResultPage({
    super.key,
    required this.practiceId,
    required this.type,
  });

  static Route<void> route(
      {required int practiceId, required String type}) {
    return MaterialPageRoute<void>(
      builder: (_) => StudyResultPage(practiceId: practiceId, type: type),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => StudyResultPageProvider()..loadData(type, practiceId),
      child: Consumer<StudyResultPageProvider>(
        builder: (context, provider, child) {
          return Scaffold(
            backgroundColor: const Color(0xFFFFFFFF),
            body: Stack(
              children: [
                Positioned(
                  top: 20,
                  left: -20,
                  right: 0,
                  child: Image.asset(
                    'images/quick_study_bg.png', // 请确保替换为实际的背景图路径
                    fit: BoxFit.fill,
                    height: 200, // 可以根据需要调整背景图高度
                  ),
                ),
                Positioned(
                  top: 20,
                  left: -20,
                  right: 0,
                  child: Image.asset(
                    'images/quick_study_result_page_bg.png', // 请确保替换为实际的背景图路径
                    fit: BoxFit.fill,
                    height: 200, // 可以根据需要调整背景图高度
                  ),
                ),
                Column(
                  children: [
                    _buildHeader(provider),
                    _buildWordList(context, provider),
                    _buildBottomButtons(context),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(StudyResultPageProvider provider) {
    final correctRate = provider.quickStudyResultModel?.data?.accuracy ?? 0;
    final description = provider.quickStudyResultModel?.data?.description ?? '';
    final english = provider.quickStudyResultModel?.data?.english ?? '';
    final chinese = provider.quickStudyResultModel?.data?.chinese ?? '';

    return Container(
      width: double.infinity,
      alignment: Alignment.topLeft,
      padding: EdgeInsets.only(top: 200.w, left: 48.w, right: 48.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            english,
            style: TextStyle(
              fontSize: 58.sp,
              fontWeight: FontWeight.w900,
              foreground: Paint()
                ..shader = const LinearGradient(
                  colors: [
                    Color(0xFFFFC300),
                    Color(0xFFFF8F1F),
                  ],
                ).createShader(const Rect.fromLTWH(0.0, 0.0, 200.0, 70.0)),
            ),
          ),
          SizedBox(height: 20.w),
          Text(
            chinese,
            style: TextStyle(
              fontSize: 58.sp,
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: 10.w),
          Text(
            description,
            style: TextStyle(
              fontSize: 24.sp,
              color: const Color(0xFF4E5766),
            ),
          ),
          SizedBox(height: 20.w),
          Row(
            children: [
              CorrectProgressIndicator(
                progress: correctRate / 100,
                size: 24,
                progressColor: const Color(0xFF007AFF),
                backgroundColor: const Color.fromRGBO(65, 192, 255, 0.12),
                strokeWidth: 4,
              ),
              SizedBox(width: 15.w),
              Text(
                '正确率${correctRate}%',
                style: TextStyle(
                  fontSize: 30.sp,
                  color: const Color(0xFF000000),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget _buildWordList(
      BuildContext context, StudyResultPageProvider provider) {
    final vocabularies =
        provider.quickStudyResultModel?.data?.vocabularies ?? [];

    return Expanded(
      child: Container(
        margin: EdgeInsets.only(top: 80.w),
        padding: EdgeInsets.all(48.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '相关词汇',
                  style: TextStyle(
                    fontSize: 32.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton.icon(
                  onPressed: () {
                    // 一键收藏
                    if (type == 'vocabulary') {
                      provider.collectAllWords();
                    } else if (type == 'phrasal_verbs') {
                      provider.batchCollectPhrasalVerb();
                    } else if (type == 'collocations') {
                      provider.batchCollectCollocation();
                    }
                  },
                  // 蓝色图标和文字
                  icon: const Icon(
                    Icons.favorite_border,
                    color: Color(0xFF2693FF),
                  ),
                  label: const Text(
                    '一键收藏',
                    style: TextStyle(
                      color: Color(0xFF2693FF),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 10.w),
            // 一条分割线
            const Divider(
              color: Color(0xFFEEEEEE),
              height: 1,
            ),
            SizedBox(height: 10.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (var vocabulary in vocabularies)
                    _buildWordItem(provider, vocabulary.id!, vocabulary.word!, vocabulary.chinese!, vocabulary.isCollected ?? false),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWordItem(StudyResultPageProvider provider, int id, String word, String translation, bool isCollected) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: 550.w,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  word,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 15,
                  ),
                ),
                SizedBox(height: 4.w),
                Text(
                  translation,
                  style: const TextStyle(
                    color: Color(0xFF4E5766),
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
          // 一个收藏图标
          IconButton(
            onPressed: () {
              // 收藏
              if (type == 'vocabulary') {
                provider.collectWord(word);
              } else if (type == 'phrasal_verbs') {
                provider.collectPhrasalVerb(id);
              } else if (type == 'collocations') {
                provider.collectCollocation(id);
              }
            },
            icon: Icon(
              isCollected ? Icons.favorite : Icons.favorite_border,
              color: isCollected ? const Color.fromARGB(255, 244, 20, 23) : const Color(0xFFB8CDDB),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(48.w),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: () => {
                // 清除所有路由
                Navigator.of(context).popUntil((route) => route.isFirst)
              },
              style: ElevatedButton.styleFrom(
                elevation: 0, // 去掉阴影
                backgroundColor: Colors.white,
                foregroundColor: Colors.black,
                padding: EdgeInsets.symmetric(vertical: 28.w),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: const BorderSide(color: Colors.black, width: 0.5),
                ),
              ),
              child: const Text(
                '退出练习',
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          SizedBox(width: 24.w),
          Expanded(
            child: ElevatedButton(
              onPressed: () => {
                // 重新进入练习页面
                Navigator.push(context, StudyPage.route(type: type)),
              },
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 28.w),
                backgroundColor: const Color(0xFF2693FF),
                foregroundColor: Colors.black,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                '继续练习',
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CorrectProgressIndicator extends StatelessWidget {
  final double progress; // 0.0 到 1.0 之间的值
  final double size;
  final Color progressColor;
  final Color backgroundColor;
  final double strokeWidth;

  const CorrectProgressIndicator({
    super.key,
    required this.progress,
    this.size = 100,
    this.progressColor = const Color(0xFF2693FF),
    this.backgroundColor = const Color(0xFFE5E5EA),
    this.strokeWidth = 10,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CustomPaint(
        painter: CircularProgressPainter(
          progress: progress,
          progressColor: progressColor,
          backgroundColor: backgroundColor,
          strokeWidth: strokeWidth,
        ),
      ),
    );
  }
}

// 圆形进度条
class CircularProgressPainter extends CustomPainter {
  final double progress;
  final Color progressColor;
  final Color backgroundColor;
  final double strokeWidth;

  CircularProgressPainter({
    required this.progress,
    required this.progressColor,
    required this.backgroundColor,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // 绘制背景圆环
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    canvas.drawCircle(center, radius, backgroundPaint);

    // 绘制进度圆环
    final progressPaint = Paint()
      ..color = progressColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -pi / 2, // 从12点钟方向开始
      2 * pi * progress, // 根据进度画圆弧
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(CircularProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.progressColor != progressColor ||
        oldDelegate.backgroundColor != backgroundColor ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}
