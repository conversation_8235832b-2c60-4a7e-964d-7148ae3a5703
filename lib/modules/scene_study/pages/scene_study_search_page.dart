import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../provider/scene_study_search_provider.dart';
import 'topic_card.dart';

class SceneStudySearchPage extends StatefulWidget {
  const SceneStudySearchPage({super.key});

  static Route<void> route() {
    return MaterialPageRoute<void>(
        builder: (_) => const SceneStudySearchPage());
  }

  @override
  _SceneStudySearchPageState createState() => _SceneStudySearchPageState();
}

class _SceneStudySearchPageState extends State<SceneStudySearchPage> {
  late ScrollController _scrollController;
   // 添加控制器和焦点节点
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  // 添加一个 late 变量来存储 provider
  late SceneStudySearchProvider _provider;

  @override
  void initState() {
    super.initState();

    _provider = SceneStudySearchProvider()..loadData();
    
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    // 在下一帧中请求焦点
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      _focusNode.requestFocus();
      _provider.inputFocusedChange(true);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      // 当滚动到达底部时请求下一页数据
      _provider.getNextPageData(); // 调用加载下一页数据的方法
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _provider,
      child: Consumer<SceneStudySearchProvider>(
        builder: (context, provider, child) {
          return Scaffold(
            backgroundColor: const Color(0xFFFFFFFF),
            body: Stack(
              children: [
                Container(
                  padding: EdgeInsets.only(left: 32.w, right: 32.w, top: 160.w),
                  child: Column(
                    children: [
                      _buildSearchBar(context, provider),
                      _buildSearchHistory(context, provider),
                      SizedBox(height: 32.w),
                      _buildSearchResults(context, provider),
                      // 正在搜索时, 展示loading
                      if (provider.isSearching)
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 10),
                            child: SizedBox(
                              width: 40.w,
                              height: 40.w,
                              child: const CircularProgressIndicator(
                                strokeWidth: 3,
                                color: Color.fromARGB(255, 167, 201, 236),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                )
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSearchBar(
      BuildContext context, SceneStudySearchProvider provider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          padding:
              EdgeInsets.only(left: 12.w, right: 12.w, top: 6.w, bottom: 6.w),
          width: 600.w,
          decoration: BoxDecoration(
            color: const Color(0xFFEDF6F7),
            borderRadius: BorderRadius.circular(30.w),
          ),
          child: Row(children: [
            // 搜索图标
            const Icon(
              Icons.search,
              size: 20,
              color: Colors.grey,
            ),
            const SizedBox(width: 8),
            // 搜索输入框,  进入页面时自动选中
            Expanded(
              child: TextField(
                controller: _controller, // 添加控制器
                focusNode: _focusNode, // 添加焦点节点
                onSubmitted: (value) => {
                  provider.performSearch(query: value),
                  provider.inputFocusedChange(false),
                },
                // 输入框聚焦时, 显示搜索记录
                onTap: () {
                  provider.inputFocusedChange(true);
                },
                // 输入框失去焦点
                onTapOutside: (value) {
                  provider.isInputFocused = false;
                },
                // 自动选中
                decoration: const InputDecoration(
                  hintText: '输入场景名称搜索',
                  hintStyle: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                  border: InputBorder.none, // 移除下划线
                  isDense: true, // 使输入框更紧凑
                  contentPadding: EdgeInsets.symmetric(vertical: 8),
                ),
              ),
            ),
          ]),
        ),
        GestureDetector(
          onTap: () {
            // 清空搜索框
            provider.clearSearch();
            // 返回上一页
            Navigator.pop(context);
          },
          child: const Text(
            '取消',
            style: TextStyle(
              color: Color(0xFF272733),
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSearchHistory(
      BuildContext context, SceneStudySearchProvider provider) {
    // 如果不存在搜索历史，则不显示
    if (provider.searchHistory.isEmpty) {
      return const SizedBox.shrink();
    }

    // 输入框没有聚焦, 则不显示搜索历史
    if (!provider.isInputFocused) {
      return const SizedBox.shrink();
    }

    // 限制最多显示10条历史记录
    final displayHistory = provider.searchHistory.take(10).toList();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ListView.builder(
          padding:
              EdgeInsets.only(top: 12.w, bottom: 12.w), // 移除 ListView 的默认内边距
          shrinkWrap: true, // 自适应内容高度
          physics: const NeverScrollableScrollPhysics(), // 禁用滚动
          itemCount: displayHistory.length,
          itemBuilder: (context, index) {
            return Container(
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFEEEEEE),
                    width: 0.5,
                  ),
                ),
              ),
              child: ListTile(
                contentPadding: EdgeInsets.symmetric(horizontal: 32.w),
                minLeadingWidth: 0,
                leading: const Icon(
                  Icons.history_outlined,
                  color: Color(0xFF999999),
                  size: 20,
                ),
                title: Text(
                  displayHistory[index],
                  style: TextStyle(
                    fontSize: 28.sp,
                    color: const Color(0xFF272733),
                  ),
                ),
                trailing: GestureDetector(
                  onTap: () {
                    provider.removeFromHistory(displayHistory[index]);
                  },
                  child: const Icon(
                    Icons.close,
                    color: Color(0xFF999999),
                    size: 20,
                  ),
                ),
                onTap: () {
                  provider.performSearch(query: displayHistory[index]);
                },
              ),
            );
          },
        ),
        // 清空搜索历史
        Container(
          width: double.infinity,
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(vertical: 12.w),
          child: GestureDetector(
            onTap: () {
              provider.clearSearchHistory();
            },
            child: Text(
              '清空全部搜索记录',
              style: TextStyle(
                fontSize: 28.sp,
                color: const Color(0xFF666666),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResults(
      BuildContext context, SceneStudySearchProvider provider) {
    // 先过滤有搜索结果的分类
    final searchResults = provider.searchResults
        .where((item) => item.sceneList.isNotEmpty)
        .toList();

    final currentSearchResultIndex = provider.currentSearchResultIndex;

    // 两个搜索结果都是0则不展示搜索结果
    // 正在搜索时, 不展示搜索结果
    if (provider.isSearching || searchResults.isEmpty) {
      return const SizedBox.shrink();
    }

    return Expanded(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: searchResults
                .asMap()
                .entries
                .map((e) => GestureDetector(
                      onTap: () {
                        provider.selectSearchResultCategory(e.key);
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: Column(
                          children: [
                            Text(
                              e.value.name,
                              style: TextStyle(
                                color: currentSearchResultIndex == e.key
                                    ? Colors.black
                                    : Colors.grey,
                                fontSize:
                                    currentSearchResultIndex == e.key ? 18 : 16,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ))
                .toList(),
          ),
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.only(top: 24.w),
              controller: _scrollController,
              itemCount:
                  searchResults[currentSearchResultIndex].sceneList.length,
              itemBuilder: (context, index) => TopicCard(context,
                  topic:
                      searchResults[currentSearchResultIndex].sceneList[index]),
            ),
          ),
        ],
      ),
    );
  }
}
