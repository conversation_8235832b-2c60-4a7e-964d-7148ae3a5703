import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/modules/scene_study/provider/my_collect_page_provider.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'topic_card.dart';

class MyCollectPage extends StatefulWidget {
  MyCollectPage({super.key});

  State<MyCollectPage> createState() => _SceneStudyPageState();

  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => MyCollectPage());
  }
}

class _SceneStudyPageState extends State<MyCollectPage> {
  late ScrollController _scrollController;
  late MyCollectPageProvider myCollectPageProvider;
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);

    myCollectPageProvider = MyCollectPageProvider()..loadData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.offset >=
        _scrollController.position.maxScrollExtent) {
      // 当滚动到达底部时请求下一页数据
      myCollectPageProvider.getNextPageData(); // 调用加载下一页数据的方法
    }
  }
  
  void _onRefresh() async {
    // 执行刷新操作
    await myCollectPageProvider.refreshData();
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    // 执行加载更多操作
    await myCollectPageProvider.getNextPageData();
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: myCollectPageProvider,
      child: Consumer<MyCollectPageProvider>(
        builder: (context, provider, child) {
          return Scaffold(
            backgroundColor: Colors.white,
            appBar: PreferredSize(
              preferredSize: Size.fromHeight(90.w), // 调整 AppBar 的高度以适应背景图
              child: Container(
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('images/scene_study_bg.png'), // 你的背景图路径
                    fit: BoxFit.fill,
                  ),
                ),
                child: AppBar(
                  backgroundColor: Colors.transparent, // AppBar 背景透明
                  leadingWidth: 300.w,
                  leading: CustomAppbar.leftWidget(context, text: "我的收藏"),
                ),
              ),
            ),
            body: Padding(
              padding: EdgeInsets.all(32.w),
              child: SmartRefresher(
                controller: _refreshController,
                enablePullDown: true,
                enablePullUp: true,
                header: const WaterDropHeader(),
                footer: const ClassicFooter(),
                onRefresh: _onRefresh,
                onLoading: _onLoading,
                child: ListView(
                  controller: _scrollController,
                  children: provider.collectList
                      .map((ele) => TopicCard(context, topic: ele))
                      .toList(),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
