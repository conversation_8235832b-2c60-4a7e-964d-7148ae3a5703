import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/modules/scene_study/pages/my_create_topic_card.dart';
import 'package:flutter_app_kouyu/widgets/common_widget.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../provider/my_create_page_provider.dart';

class MyCreatePage extends StatefulWidget {
  MyCreatePage({super.key});

  State<MyCreatePage> createState() => _SceneStudyPageState();

  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => MyCreatePage());
  }
}

class _SceneStudyPageState extends State<MyCreatePage> {
  late ScrollController _scrollController;
  late MyCreatePageProvider myCreatePageProvider;
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);

    myCreatePageProvider = MyCreatePageProvider()..loadData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.offset >=
        _scrollController.position.maxScrollExtent) {
      // 当滚动到达底部时请求下一页数据
      myCreatePageProvider.getNextPageData(); // 调用加载下一页数据的方法
    }
  }
  
  void _onRefresh() async {
    // 执行刷新操作
    await myCreatePageProvider.refreshData();
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    // 执行加载更多操作
    await myCreatePageProvider.getNextPageData();
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: myCreatePageProvider,
      child: Consumer<MyCreatePageProvider>(
        builder: (context, provider, child) {
          final curTabIndex = provider.curTabIndex;
          final curTabItem = provider.createTabList.isNotEmpty ? provider.createTabList[curTabIndex] : null;
          final sceneList = curTabItem?.sceneList ?? [];

          return Scaffold(
            backgroundColor: Colors.white,
            appBar: PreferredSize(
              preferredSize: Size.fromHeight(90.w), // 调整 AppBar 的高度以适应背景图
              child: Container(
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('images/scene_study_bg.png'), // 你的背景图路径
                    fit: BoxFit.fill,
                  ),
                ),
                child: AppBar(
                  backgroundColor: Colors.transparent, // AppBar 背景透明
                  leadingWidth: 300.w,
                  leading: CustomAppbar.leftWidget(context, text: "我创建的"),
                ),
              ),
            ),
            body: Padding(
              padding: EdgeInsets.all(32.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Common_TabListWidget(
                    items: provider.createTabList
                        .map((e) => CommonTabItem(
                            title: e.title, count: e.count))
                        .toList(),
                    onTabSelected: (title) {
                      provider.selectTab(title);
                    },
                    selectedIndex: curTabIndex,
                    showCount: true,
                  ),
                  SizedBox(height: 24.w),
                  Expanded(
                    child: SmartRefresher(
                      controller: _refreshController,
                      enablePullDown: true,
                      enablePullUp: true,
                      header: const WaterDropHeader(),
                      footer: const ClassicFooter(),
                      onRefresh: _onRefresh,
                      onLoading: _onLoading,
                      child: ListView(
                        controller: _scrollController,
                        children: sceneList
                            .map((ele) => MyCreateTopicCard(context, topic: ele))
                            .toList(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
