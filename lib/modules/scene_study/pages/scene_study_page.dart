import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/widgets/common_widget.dart';
import 'package:flutter_app_kouyu/widgets/more_setting_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../provider/scene_study_page_provider.dart';
import 'create_custom_scene_page.dart';
import 'my_collect_page.dart';
import 'my_create_page.dart';
import 'scene_study_search_page.dart';
import 'topic_card.dart';

class SceneStudyPage extends StatefulWidget {
  final int tabIndex;
  final PageController pageController = PageController();

  SceneStudyPage({super.key, required this.tabIndex});

  State<SceneStudyPage> createState() => _SceneStudyPageState();

  static Route<void> route({required int tabIndex}) {
    return MaterialPageRoute<void>(
        builder: (_) => SceneStudyPage(tabIndex: tabIndex));
  }
}

class _SceneStudyPageState extends State<SceneStudyPage> {
  final PageController pageController = PageController();
  // 为每个标签页创建单独的滚动控制器
  final ScrollController _topicScrollController = ScrollController();
  final ScrollController _customScrollController = ScrollController();
  
  // 为每个标签页创建单独的 RefreshController
  final RefreshController _topicRefreshController = RefreshController(initialRefresh: false);
  final RefreshController _customRefreshController = RefreshController(initialRefresh: false);

  late SceneStudyPageProvider sceneStudyPageProvider;

  @override
  void dispose() {
    pageController.dispose();
    _topicScrollController.dispose();
    _customScrollController.dispose();
    _topicRefreshController.dispose();
    _customRefreshController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _topicScrollController.addListener(() => _onScroll(0));
    _customScrollController.addListener(() => _onScroll(1));

    sceneStudyPageProvider = SceneStudyPageProvider()..loadData(widget.tabIndex);
  }

  void _onScroll(int tabIndex) {
    final controller = tabIndex == 0 ? _topicScrollController : _customScrollController;
    if (controller.offset >= controller.position.maxScrollExtent) {
      // 当滚动到达底部时请求下一页数据
      sceneStudyPageProvider.getNextPageData(); // 调用加载下一页数据的方法
    }
  }

  void _onRefresh(int tabIndex) async {
    // 执行刷新操作
    await sceneStudyPageProvider.refreshData(tabIndex);
    if (tabIndex == 0) {
      _topicRefreshController.refreshCompleted();
    } else {
      _customRefreshController.refreshCompleted();
    }
  }

  void _onLoading(int tabIndex) async {
    // 执行加载更多操作
    await sceneStudyPageProvider.getNextPageData();
    if (tabIndex == 0) {
      _topicRefreshController.loadComplete();
    } else {
      _customRefreshController.loadComplete();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: sceneStudyPageProvider,
      child: Consumer<SceneStudyPageProvider>(
        builder: (context, provider, child) {
          return Scaffold(
            backgroundColor: Colors.white,
            appBar: PreferredSize(
              preferredSize: Size.fromHeight(90.w), // 调整 AppBar 的高度以适应背景图
              child: Container(
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('images/scene_study_bg.png'), // 你的背景图路径
                    fit: BoxFit.fill,
                  ),
                ),
                child: AppBar(
                  automaticallyImplyLeading: false, // 这行代码会禁用自动添加的返回按钮
                  backgroundColor: Colors.transparent, // AppBar 背景透明
                  elevation: 0,
                  title: _buildTabBar(context, provider),
                ),
              ),
            ),
            body: PageView(
              controller: pageController,
              onPageChanged: (index) {
                provider.selectTab(index);
              },
              children: [
                _buildTabContent(context, provider, 0),
                _buildTabContent(context, provider, 1),
              ], // 根据选中的tab显示不同内容
            ),
          );
        },
      ),
    );
  }

  // 构建不同tab对应的内容
  Widget _buildTabContent(
      BuildContext context, SceneStudyPageProvider provider, int index) {
    switch (index) {
      case 0:
        return _buildListContent([
          _buildTopicTabList(context, provider, index),
          SizedBox(height: 32.w),
          ...provider.topicSceneTypeList.isEmpty
              ? []
              : provider.topicSceneTypeList[provider.currentTopicSceneTypeIndex]
                  .sceneList
                  .map((item) {
                  return TopicCard(context, topic: item);
                }),
        ], index);
      case 1: // 自定义场景
        return Stack(
          children: [
            _buildListContent([
              _buildTopicTabList(context, provider, index),
              SizedBox(height: 32.w),
              ...provider.customSceneTypeList.isEmpty
                  ? []
                  : provider
                      .customSceneTypeList[provider.currentCustomSceneTypeIndex]
                      .sceneList
                      .map((item) {
                      return TopicCard(context, topic: item);
                    }),
            ], index),
            // 自定义场景在页面右下角有个添加图标, 点击进入创建自定义场景页面
            Positioned(
              bottom: 20.w,
              right: 20.w,
              child: GestureDetector(
                onTap: () {
                  Navigator.of(context).push(CreateCustomScenePage.route(''));
                },
                child: Image.asset(
                  'images/create_scene_entrance_icon.png',
                  width: 200.w,
                  height: 200.w,
                ),
              ),
            ),
          ],
        );
      default:
        return _buildListContent([], index); // 默认返回空列表
    }
  }

  // 构建列表容器
  Widget _buildListContent(List<Widget> children, int tabIndex) {
    return SmartRefresher(
      controller: tabIndex == 0 ? _topicRefreshController : _customRefreshController,
      enablePullDown: true,
      enablePullUp: true,
      header: const WaterDropHeader(),
      footer: const ClassicFooter(),
      onRefresh: () => _onRefresh(tabIndex),
      onLoading: () => _onLoading(tabIndex),
      child: ListView(
        controller: tabIndex == 0 ? _topicScrollController : _customScrollController,
        padding:
            EdgeInsets.only(left: 32.w, right: 32.w, bottom: 32.w, top: 16.w),
        children: children,
      ),
    );
  }

  Widget _buildTabBar(BuildContext context, SceneStudyPageProvider provider) {
    final curTabIndex = provider.curTabIndex;
    return Row(
      children: [
        ...SCENE_STUDY_TAB_LIST.map((tab) {
          int index = tab['tabIndex'] as int;
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              provider.selectTab(index);
              // 主动触发pageView的滚动
              pageController.animateToPage(index,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut);
            },
            child: Padding(
              padding: const EdgeInsets.only(right: 8),
              child: Column(
                children: [
                  Text(
                    tab['title'] as String,
                    style: TextStyle(
                      color: curTabIndex == index ? Colors.black : Colors.grey,
                      fontSize: curTabIndex == index ? 20 : 16,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    width: 20,
                    height: 5,
                    decoration: BoxDecoration(
                      color: provider.curTabIndex == index
                          ? const Color(0xFF2693FF)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
        const Spacer(),
        Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
          GestureDetector(
            onTap: () {
              Navigator.of(context).push(SceneStudySearchPage.route());
            },
            child: const Icon(Icons.search, color: Colors.black, size: 24),
          ),
          SizedBox(width: 24.w),
          MoreSettingWidget(items: [
            MoreSettingItem(
              iconImage: 'images/my_collect.png',
              title: '我的收藏',
              onTap: () {
                Navigator.of(context).push(MyCollectPage.route());
              },
            ),
            MoreSettingItem(
              iconImage: 'images/my_create.png',
              title: '我创建的',
              onTap: () {
                Navigator.of(context).push(MyCreatePage.route());
              },
            )
          ]),
        ]),
        // 搜索按钮，点击跳转搜索页面
      ],
    );
  }

  // 主题tab列表
  Widget _buildTopicTabList(
      BuildContext context, SceneStudyPageProvider provider, int tabIndex) {
    if (tabIndex == 0) {
      return Common_TabListWidget(
        items: provider.topicSceneTypeList
            .map((item) => CommonTabItem(title: item.name))
            .toList(),
        onTabSelected: (string) {
          final item = provider.topicSceneTypeList
              .firstWhere((item) => item.name == string);
          provider.selectSceneType(item);
        },
        selectedIndex: provider.currentTopicSceneTypeIndex,
      );
    } else {
      return Common_TabListWidget(
        items: provider.customSceneTypeList
            .map((item) => CommonTabItem(title: item.name))
            .toList(),
        onTabSelected: (string) {
          final item = provider.customSceneTypeList
              .firstWhere((item) => item.name == string);
          provider.selectSceneType(item);
        },
        selectedIndex: provider.currentCustomSceneTypeIndex,
      );
    }
  }
}

