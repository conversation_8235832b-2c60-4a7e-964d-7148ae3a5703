import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../widgets/common_widget.dart';

enum SceneProvider {
  system,
  community,
}

// 通用的场景主题和自定义场景主题的结构
class CommonSceneStructure {
  final SceneProvider provider;
  final String topicCode;
  final String title;
  final String? courseLevelName;
  final String? nickName;
  final String imageUrl;
  final int usageCount;
  final String target;
  final int? status;
  final String? rejectReason;
  final int? free;

  CommonSceneStructure({
    required this.provider,
    required this.topicCode,
    required this.title,
    this.courseLevelName,
    this.nickName,
    required this.imageUrl,
    required this.usageCount,
    required this.target,
    this.status,
    this.rejectReason,
    this.free,
  });
}

class TopicCard extends StatelessWidget {
  final CommonSceneStructure topic;
  final VoidCallback? onTap;

  const TopicCard(
    BuildContext context, {
    super.key,
    required this.topic,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ??
          () {
            Navigator.of(context).pushNamed('/sence_introduce',
                arguments: {"topic_code": topic.topicCode});
          },
      child: Card(
        margin: const EdgeInsets.only(bottom: 20),
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.all(0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Common_NetImage(
                  imageUrl: topic.imageUrl,
                  width: 160.w,
                  height: 200.w,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.only(bottom: 3, top: 3),
                  height: 200.w,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          if (topic.free == 1)
                            ...[
                              Image.asset(
                                'images/free_icon.png',
                                width: 70.w,
                                height: 45.w,
                              ),
                              SizedBox(width: 8.w),
                            ],
                          Text(
                            topic.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        topic.target,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      const Spacer(),
                      Wrap(
                        spacing: 12,
                        runSpacing: 4,
                        children: [
                          Common_InfoTag(
                            icon: Icons.people_outline,
                            text: '${topic.usageCount}人使用',
                          ),
                          if (topic.provider == SceneProvider.system)
                            Common_LevelTag(
                                levelName: topic.courseLevelName ?? '')
                          else
                            Common_InfoTag(
                              text: '@ ${topic.nickName}',
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
