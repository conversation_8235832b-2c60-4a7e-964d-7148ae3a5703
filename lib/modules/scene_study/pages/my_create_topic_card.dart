import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../widgets/common_widget.dart';
import 'topic_card.dart';

// 我创建的页面-卡片, 带有状态

enum MyCreateTopicCardStatus {
  pending(1), // 1. 待审核
  passed(2), // 2. 审核通过
  rejected(3); // 3. 已驳回

  final int value;

  const MyCreateTopicCardStatus(this.value);

  static MyCreateTopicCardStatus fromValue(int value) {
    return MyCreateTopicCardStatus.values
        .firstWhere((status) => status.value == value);
  }
}

//

class MyCreateTopicCard extends StatelessWidget {
  final CommonSceneStructure topic;
  final VoidCallback? onTap;

  const MyCreateTopicCard(
    BuildContext context, {
    Key? key,
    required this.topic,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final canStudy = MyCreateTopicCardStatus.fromValue(topic.status ?? 0) == MyCreateTopicCardStatus.passed;
    return GestureDetector(
      onTap: onTap ??
          () {
            Navigator.of(context).pushNamed('/sence_introduce',
                arguments: {"topic_code": topic.topicCode, "can_study": canStudy});
          },
      child: Card(
        margin: const EdgeInsets.only(bottom: 20),
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.all(0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Common_NetImage(
                  imageUrl: topic.imageUrl,
                  width: 160.w,
                  height: 200.w,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.only(bottom: 3, top: 3),
                  height: 200.w,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Text(
                              topic.title,
                              style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          ),
                          // SizedBox(width: 24.w),
                          _buildStatusTag(MyCreateTopicCardStatus.fromValue(
                              topic.status ?? 0)),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        topic.target,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      const Spacer(),
                      Wrap(
                        spacing: 12,
                        runSpacing: 4,
                        children: [
                          Common_InfoTag(
                            icon: Icons.people_outline,
                            text: '${topic.usageCount}人使用',
                          ),
                          Common_InfoTag(
                            text: '@ ${topic.nickName}',
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 状态标签
  Widget _buildStatusTag(MyCreateTopicCardStatus status) {
    // 根据状态返回不同的颜色
    Color getColor() {
      switch (status) {
        case MyCreateTopicCardStatus.pending:
          return Color(0xFF2693FF);
        case MyCreateTopicCardStatus.rejected:
          return Color(0xFFFA5151);
        default:
          return Color(0xFF2693FF);
      }
    }

    // 根据状态返回不同的文字
    String getText() {
      switch (status) {
        case MyCreateTopicCardStatus.pending:
          return '审核中';
        case MyCreateTopicCardStatus.rejected:
          return '未通过审核: ${topic.rejectReason}';
        default:
          return '审核通过';
      }
    }

    // 根据状态返回不同的背景色
    Color getBackgroundColor() {
      switch (status) {
        case MyCreateTopicCardStatus.pending:
          return Color.fromRGBO(38, 147, 255, 0.1);
        case MyCreateTopicCardStatus.rejected:
          return Color.fromRGBO(250, 81, 81, 0.1);
        default:
          return Color(0xFF2693FF);
      }
    }

    // 如果已通过审核, 则不显示状态标签
    if (status == MyCreateTopicCardStatus.passed) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.only(left: 8.w, right: 9.w, top: 4.w, bottom: 4.w),
      decoration: BoxDecoration(
          color: getBackgroundColor(), borderRadius: BorderRadius.circular(4)),
      child: Text(
        getText(),
        style: TextStyle(
          color: getColor(),
          fontSize: 24.sp,
        ),
      ),
    );
  }
}
