import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:provider/provider.dart';

import '../../../widgets/common_widget.dart';
import '../provider/create_custom_scene_page_provider.dart';

class CreateCustomScenePage extends StatefulWidget {
  final String? sceneCode;
  const CreateCustomScenePage({super.key, this.sceneCode});

  State<CreateCustomScenePage> createState() => _CreateCustomScenePageState();

  static Route<void> route(String? sceneCode) {
    return MaterialPageRoute<void>(
      builder: (_) => CreateCustomScenePage(sceneCode: sceneCode),
    );
  }
}

class _CreateCustomScenePageState extends State<CreateCustomScenePage> {
  // 为每个输入框创建独立的控制器
  final TextEditingController _yourRoleController = TextEditingController();
  final TextEditingController _aiRoleController = TextEditingController();
  final FocusNode _yourRoleFocusNode = FocusNode();
  final FocusNode _aiRoleFocusNode = FocusNode();

  // 添加一个 late 变量来存储 provider
  late CreateCustomScenePageProvider _provider;

  @override
  void initState() {
    super.initState();

    // 初始化 provider
    _provider = CreateCustomScenePageProvider()..loadData(widget.sceneCode);

    // 添加焦点监听
    _yourRoleFocusNode
        .addListener(() => _handleFocusChange(_yourRoleFocusNode, '你的角色'));
    _aiRoleFocusNode
        .addListener(() => _handleFocusChange(_aiRoleFocusNode, 'AI的角色'));
  }

  void _handleFocusChange(FocusNode focusNode, String labelText) {
    _provider.modifyFocusState(focusNode.hasFocus, labelText);
  }

  @override
  void dispose() {
    _yourRoleController.dispose();
    _aiRoleController.dispose();
    _yourRoleFocusNode.dispose();
    _aiRoleFocusNode.dispose();
    super.dispose();
  }

  Widget _buildFieldInput(CreateCustomScenePageProvider provider,
      String labelText, bool isRequired) {
    // 根据 labelText 选择对应的控制器
    final controller =
        labelText == '你的角色' ? _yourRoleController : _aiRoleController;
    final focusNode =
        labelText == '你的角色' ? _yourRoleFocusNode : _aiRoleFocusNode;

    final hintText = labelText == '你的角色' ? '请输入你的角色' : '请输入AI的角色';
    final roleText = labelText == '你的角色' ? provider.yourRole : provider.aiRole;

    String onChanged(String value) {
      if (labelText == '你的角色') {
        provider.yourRole = value;
      } else if (labelText == 'AI的角色') {
        provider.aiRole = value;
      }
      provider.saveJsonDataCache();
      return value;
    }

    // 当前是否正在聚焦
    bool isFocus = labelText == '你的角色'
        ? provider.isMyRoleInputFocus
        : provider.isAiRoleInputFocus;
    bool isRandomGenerating = labelText == '你的角色'
        ? provider.isRandomGeneratingYourRole
        : provider.isRandomGeneratingAiRole;

    // 更新控制器的文本
    controller.text = roleText;
    // 保持光标位置在末尾
    controller.selection = TextSelection.fromPosition(
      TextPosition(offset: controller.text.length),
    );

    return Container(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 0.w, bottom: 0.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.w),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (isRequired) _redRequiredChat(),
          Text(
            labelText,
            style: style_1_28,
          ),
          // const Spacer(),
          SizedBox(width: 100.w),
          Expanded(
            flex: 1,
            child: TextField(
              controller: controller,
              focusNode: focusNode,
              textAlign: TextAlign.right,
              onChanged: onChanged,
              style: style_1_28_500,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: hintText,
                hintStyle: style_2_28,
              ),
            ),
          ),

          Visibility(
            visible: isFocus,
            child: Row(
              children: [
                SizedBox(width: 16.w),
                // 一个 X 图标, 点击清楚输入框内容
                GestureDetector(
                  onTap: () {
                    controller.clear();
                    onChanged('');
                  },
                  child: Image.asset('images/close-circle-fill.png',
                      width: 32.w, height: 32.w),
                ),
                SizedBox(width: 16.w),
                magicOrLoadingWidget(isRandomGenerating, () {
                  if (labelText == '你的角色') {
                    provider.randomGenerateMyRole();
                  } else if (labelText == 'AI的角色') {
                    provider.randomGenerateAiRole();
                  }
                }),
              ],
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _provider,
      child: Consumer<CreateCustomScenePageProvider>(
        builder: (context, provider, child) {
          return Scaffold(
            backgroundColor: const Color(0xFFEDF6F7),
            appBar: PreferredSize(
              preferredSize: Size.fromHeight(100.w), // 调整 AppBar 的高度以适应背景图
              child: Container(
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('images/create_scene_bg.png'), // 你的背景图路径
                    fit: BoxFit.cover,
                  ),
                ),
                child: AppBar(
                  backgroundColor: Colors.transparent, // AppBar 背景透明
                  leadingWidth: 300.w,
                  leading: CustomAppbar.leftWidget(context,
                      text: provider.pageMode == 'create' ? "创建场景" : "编辑场景"),
                  title: _buildRandomGenerateBtn(provider),
                ),
              ),
            ),
            body: GestureDetector(
              onTap: () {
                // 点击空白处收起键盘
                FocusScope.of(context).unfocus();
              },
              child: Stack(
                children: [
                  SingleChildScrollView(
                    // 背景色
                    padding: EdgeInsets.only(
                        left: 32.w, right: 32.w, top: 12.w, bottom: 100.w),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildFieldInput(provider, '你的角色', true),
                          SizedBox(height: 16.w),
                          _buildFieldInput(provider, 'AI的角色', true),
                          SizedBox(height: 16.w),
                          _buildDescriptionAreaInput(context, provider),
                          SizedBox(height: 16.w),
                          _buildPrivacyTypeSelect(context, provider),
                          SizedBox(height: 16.w),
                          _buildTargetAreaInput(context, provider),
                          SizedBox(height: 16.w),
                          Common_BottomConfirmButton(
                            text: provider.pageMode == 'create' ? '创建场景' : '保存',
                            bgColor: const Color(0xFF2693FF),
                            splashColor: const Color(0xFF2693FF),
                            textColor: Colors.white,
                            onTap: () {
                              if (provider.aiRole.isEmpty || provider.yourRole.isEmpty || provider.description.isEmpty) {
                                Fluttertoast.showToast(msg: '请先设置角色和描述', gravity: ToastGravity.CENTER);
                              } else {
                                showPermissionConfirmDialog(context, provider);
                              }
                            }),
                          ]),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

/// 一个红色的*号, 用于提示必填项
Widget _redRequiredChat() {
  return Text(
    '* ',
    style: TextStyle(
      fontSize: 26.sp,
      color: Colors.red,
      fontWeight: FontWeight.w500,
    ),
  );
}

// 构建 场景描述 textArea 输入框, 背景为白色, 下方支持选择手机图片
Widget _buildDescriptionAreaInput(
    BuildContext context, CreateCustomScenePageProvider provider) {
  final _controller = TextEditingController(text: provider.description);
  return Container(
    padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 20.w, bottom: 20.w),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16.w),
    ),
    child: Column(
      children: [
        Row(
          children: [
            _redRequiredChat(),
            Text('场景描述', style: style_1_28),
            const Spacer(),
            magicOrLoadingWidget(provider.isRandomGeneratingDescription, () {
              provider.randomGenerateDescription();
            })
          ],
        ),
        TextField(
            controller: _controller,
            onChanged: (value) {
              provider.description = value;
              provider.saveJsonDataCache();
            },
            maxLines: 7,
            maxLength: MAX_DESCRIPTION_LENGTH,
            decoration: InputDecoration(
              border: InputBorder.none, // 没有边框样式
              hintText: "请输入场景描述",
              hintStyle: style_2_28,
            ),
            style: style_1_28_500),
        // 图片上传 icon
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            if (provider.imageUrl.isNotEmpty) ...[
              Row(
                children: [
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          provider.imageUrl,
                          width: 200.w,
                          height: 200.w,
                          fit: BoxFit.cover,
                        ),
                      ),
                      // 删除按钮
                      Positioned(
                        right: -40.w, // 调整按钮的右边距
                        top: -40.w, // 调整按钮的上边距
                        child: IconButton(
                          onPressed: () {
                            provider.deleteImage();
                          },
                          icon: Image.asset(
                            'images/close-circle-fill.png', // 使用自定义图片路径
                            width: 48.w, // 设置图标宽度
                            height: 48.w, // 设置图标高度
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ] else ...[
              GestureDetector(
                onTap: () {
                  // 选择图片
                  provider.pickImage(context);
                },
                child: Container(
                  width: 200.w,
                  height: 200.w,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16.w),
                    border: Border.all(color: const Color(0xFFDDEDF0)),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        'images/add-circle-fill.png',
                        width: 50.w,
                        height: 50.w,
                      ),
                      SizedBox(height: 10.w),
                      Text('添加场景封面', style: style_1_24),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ],
    ),
  );
}

// 构建 目标 textArea 输入框, 背景为白色
Widget _buildTargetAreaInput(
    BuildContext context, CreateCustomScenePageProvider provider) {
  final _controller = TextEditingController(text: provider.target);
  return Container(
    padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 20.w, bottom: 20.w),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16.w),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text('对话目标', style: style_1_28),
            const Spacer(),
            magicOrLoadingWidget(provider.isRandomGeneratingTarget, () {
              provider.randomGenerateSceneTarget();
            })
          ],
        ),
        TextField(
            controller: _controller,
            onChanged: (value) {
              provider.target = value;
              provider.saveJsonDataCache();
            },
            maxLines: 3,
            decoration: InputDecoration(
              border: InputBorder.none, // 没有边框样式
              hintText: "例如: 学老师寻求帮助",
              hintStyle: style_2_28,
            ),
            style: style_1_28_500),
      ],
    ),
  );
}

// 构建一个拉下选择, 选择隐私类型, 公开还是私密
Widget _buildPrivacyTypeSelect(
    BuildContext context, CreateCustomScenePageProvider provider) {
  return Container(
    padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 24.w, bottom: 24.w),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16.w),
    ),
    child: GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        showPermissionSelectDialog(context, provider);
      },
      child: SizedBox(
        width: double.infinity,
        child: Row(
          children: [
            Text(provider.publicPermission.description, style: style_1_28),
            const Spacer(),
            const Icon(Icons.arrow_forward_ios, size: 12)
          ],
        ),
      ),
    ),
  );
}

// 一个根据状态展示魔法棒还是loading的组件
Widget magicOrLoadingWidget(bool pending, Function onTap) {
  if (pending) {
    return _loadingCircle();
  } else {
    // 一个魔法棒图标, 点击调用函数
    return GestureDetector(
      onTap: () {
        onTap();
      },
      child: Image.asset('images/magic-line.png', width: 40.w, height: 40.w),
    );
  }
}

// 【随机生成】组件, 左边一个魔法棒图标, 右边是文案, 文案为【随机生成】, 点击后调用 provider.randomGenerate() 方法
Widget _buildRandomGenerateBtn(CreateCustomScenePageProvider provider) {
  return GestureDetector(
    onTap: () {
      provider.randomGenerate();
    },
    child: Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (provider.isRandomGenerating)
          _loadingCircle()
        else ...[
          Image.asset('images/magic-line.png', width: 40.w, height: 40.w),
          SizedBox(width: 8.w),
          Text('随机生成', style: style_2_28),
        ],
      ],
    ),
  );
}

Widget _loadingCircle() {
  return SizedBox(
    width: 40.w,
    height: 40.w,
    child: const CircularProgressIndicator(
      strokeWidth: 3,
      color: Color.fromARGB(255, 167, 236, 176),
    ),
  );
}

void showPermissionConfirmDialog(
    BuildContext context, CreateCustomScenePageProvider provider) {
  showModalBottomSheet(
    context: context,
    builder: (BuildContext context) {
      return ChangeNotifierProvider.value(
        value: provider,
        child: Consumer<CreateCustomScenePageProvider>(
            builder: (context, provider, _) {
          return Container(
            padding: EdgeInsets.only(
                left: 48.w, right: 48.w, top: 32.w, bottom: 100.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24.w),
                  topRight: Radius.circular(24.w)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text('确认场景权限', style: style_1_36),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: Image.asset('images/dialog_close.png',
                          width: 24.w, height: 24.w),
                    ),
                  ],
                ),
                SizedBox(height: 24.w),
                Container(
                  padding: EdgeInsets.only(left: 24.w, right: 24.w),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF0F8FA),
                    borderRadius: BorderRadius.circular(16.w),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: PermissionList.map((e) => GestureDetector(
                          onTap: () {
                            provider.selectPublicPermission(e);
                          },
                          child: Container(
                            padding: EdgeInsets.only(top: 24.w, bottom: 24.w),
                            decoration: e.name == 'public'
                                ? BoxDecoration(
                                    border: Border(
                                      bottom: BorderSide(
                                          color: const Color.fromRGBO(
                                              0, 0, 0, 0.1),
                                          width: 1.w),
                                    ),
                                  )
                                : null,
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(e.description,
                                      style: TextStyle(
                                        fontSize: 28.sp,
                                        fontWeight:
                                            provider.publicPermission == e
                                                ? FontWeight.w600
                                                : FontWeight.normal,
                                        color: provider.publicPermission == e
                                            ? const Color(0xFF2693FF)
                                            : Colors.black,
                                      )),
                                ),
                                // 一个✅, 如果选中, 则显示✅, 否则不显示
                                if (provider.publicPermission == e)
                                  Image.asset("images/check-fill.png",
                                      width: 30.w, height: 30.w),
                              ],
                            ),
                          ),
                        )).toList(),
                  ),
                ),
                SizedBox(height: 24.w),
                Text(
                  '请注意保护个人信息，确保未侵犯第三方权益。',
                  style: style_2_24,
                ),
                SizedBox(height: 24.w),
                Common_BottomConfirmButton(
                  text: '确认',
                  bgColor: const Color(0xFF2693FF),
                  splashColor: const Color(0xFF2693FF),
                  textColor: Colors.white,
                  onTap: () async {
                    provider.createScene(context);
                  },
                ),
              ],
            ),
          );
        }),
      );
    },
  );
}

void showPermissionSelectDialog(
    BuildContext context, CreateCustomScenePageProvider provider) {
  showModalBottomSheet(
    context: context,
    builder: (BuildContext context) {
      return Container(
        padding:
            EdgeInsets.only(left: 48.w, right: 48.w, top: 32.w, bottom: 100.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(24.w), topRight: Radius.circular(24.w)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: PermissionList.map((e) => GestureDetector(
                onTap: () {
                  provider.selectPublicPermission(e);
                  Navigator.of(context).pop();
                },
                child: Container(
                  padding: EdgeInsets.only(top: 24.w, bottom: 24.w),
                  decoration: e.name == 'public'
                      ? BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                                color: const Color.fromRGBO(0, 0, 0, 0.1),
                                width: 1.w),
                          ),
                        )
                      : null,
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(e.description,
                            style: TextStyle(
                              fontSize: 28.sp,
                              fontWeight: provider.publicPermission == e
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                              color: provider.publicPermission == e
                                  ? const Color(0xFF2693FF)
                                  : Colors.black,
                            )),
                      ),
                      // 一个✅, 如果选中, 则显示✅, 否则不显示
                      if (provider.publicPermission == e)
                        Image.asset("images/check-fill.png",
                            width: 30.w, height: 30.w),
                    ],
                  ),
                ),
              )).toList(),
        ),
      );
    },
  );
}
