import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import '../pages/topic_card.dart';

class SceneType {
  String code;
  String name;

  // 是否选中
  bool isSelected = false;
  // 对应的场景列表 TopicScene.Datum ｜ CustomSceneListItem
  List<CommonSceneStructure> sceneList = [];
  // 当前页码
  int curPgae = 0;
  // 总页码
  int totalPage = 1;

  SceneType({required this.code, required this.name});
}

// 上方tap栏列表
const SCENE_STUDY_TAB_LIST = [
  {
    'title': '主题精选',
    'tabIndex': 0,
  },
  {
    'title': '社区场景',
    'tabIndex': 1,
  },
];

class SceneStudyPageProvider extends ChangeNotifier {
  int curTabIndex = 0;

  // 主题聊天场景类型列表
  List<SceneType> topicSceneTypeList = [];
  // 当前选中的类型索引
  int currentTopicSceneTypeIndex = 0;

  // 自定义场景类型列表
  List<SceneType> customSceneTypeList = [];
  // 当前选中的自定义场景类型索引
  int currentCustomSceneTypeIndex = 0;
  // 是否正在加载下一页数据
  bool isPageLoading = false;

  // 这里可以添加需要的状态和方法
  // 例如：加载数据、管理选项等
  loadData(int tabIndex) async {
    curTabIndex = tabIndex;
    // 加载数据
    await getTopicChatTypeList().then((value) {
      pageGetTopicChatList();
    });
    await getCustomSceneTypeList().then((value) {
      pageGetCustomSceneList();
    });

    notifyListeners();
  }

  // 选择tab
  void selectTab(int tabIndex) {
    curTabIndex = tabIndex;
    notifyListeners();
  }

  // 选择场景类型
  void selectSceneType(SceneType item) {
    item.isSelected = true;
    if (curTabIndex == 0) {
      currentTopicSceneTypeIndex = topicSceneTypeList.indexOf(item);
      // 其他类型取消选中
      for (var element in topicSceneTypeList) {
        if (element != item) {
          element.isSelected = false;
        }
      }
      // 获取场景列表
      pageGetTopicChatList();
    } else {
      currentCustomSceneTypeIndex = customSceneTypeList.indexOf(item);
      // 其他类型取消选中
      for (var element in customSceneTypeList) {
        if (element != item) {
          element.isSelected = false;
        }
      }
      // 获取场景列表
      pageGetCustomSceneList();
    }
    notifyListeners();
  }

  // 获取主题聊天类型列表
  getTopicChatTypeList() async {
    final response = await Api.sceneTopicsClassList();
    topicSceneTypeList = response.data
            ?.map((e) => SceneType(code: e.code ?? '', name: e.name ?? ''))
            .toList() ??
        [];
    // 默认选中第一个
    currentTopicSceneTypeIndex = 0;
    topicSceneTypeList[0].isSelected = true;
  }

  // 获取主题聊天列表
  Future<void> pageGetTopicChatList() async {
    // 判断当前类型下的场景列表是否存在
    final item = topicSceneTypeList[currentTopicSceneTypeIndex];
    if (item.curPgae >= item.totalPage) return;

    final response = await Api.getSystemSceneList(
      item.curPgae + 1,
      10,
      '',
      '',
      item.code,
    );
    item.totalPage = response.data!.totalPages!;
    item.curPgae = response.data!.currentPage!;

    // 列表填充
    item.sceneList.addAll(response.data!.items!.map((e) => CommonSceneStructure(
          provider: SceneProvider.system,
          topicCode: e.topicCode!,
          title: e.chinese!,
          imageUrl: e.imageUrl!,
          courseLevelName: e.courseLevelName,
          usageCount: e.usageCount!,
          target: e.target!,
          free: e.free,
        )));
    notifyListeners();
  }

  // 获取自定义场景类型列表
  getCustomSceneTypeList() async {
    final response = await Api.customeSceneTypeList();
    customSceneTypeList = response.data
            ?.map((e) => SceneType(code: e.value ?? '', name: e.key ?? ''))
            .toList() ??
        [];
    // 默认选中第一个
    currentCustomSceneTypeIndex = 0;
    customSceneTypeList[0].isSelected = true;
  }

  // 分页获取自定义场景列表
  Future<void> pageGetCustomSceneList() async {
    final item = customSceneTypeList[currentCustomSceneTypeIndex];
    if (item.curPgae >= item.totalPage) return;

    final response = await Api.getCustomSceneList(
      item.curPgae + 1,
      10,
      '',
      item.code,
    );
    item.totalPage = response.data!.totalPages!;
    item.curPgae = response.data!.currentPage!;
    // 列表填充
    item.sceneList.addAll(response.data!.items!.map((e) => CommonSceneStructure(
          provider: SceneProvider.community,
          topicCode: e.topicCode!,
          title: e.chinese ?? '',
          imageUrl: e.imageUrl ?? '',
          nickName: e.nickName ?? '',
          courseLevelName: '',
          usageCount: e.usageCount ?? 0,
          target: e.target ?? '',
        )));
    notifyListeners();
  }

  // 刷新数据
  Future<void> refreshData(int tabIndex) async {
    curTabIndex = tabIndex;
    
    // 重置页码和数据
    if (topicSceneTypeList.isNotEmpty) {
      for (var element in topicSceneTypeList) {
        element.curPgae = 0;
        element.totalPage = 1;
        element.sceneList.clear();
      }
    }
    
    if (customSceneTypeList.isNotEmpty) {
      for (var element in customSceneTypeList) {
        element.curPgae = 0;
        element.totalPage = 1;
        element.sceneList.clear();
      }
    }
    
    // 重新加载数据
    await getTopicChatTypeList().then((value) {
      pageGetTopicChatList();
    });
    await getCustomSceneTypeList().then((value) {
      pageGetCustomSceneList();
    });
    
    notifyListeners();
    return;
  }

  Future<void> getNextPageData() async {
    if (isPageLoading) return;
    isPageLoading = true;

    if (curTabIndex == 0) {
      await pageGetTopicChatList();
    } else {
      await pageGetCustomSceneList();
    }
    isPageLoading = false;
  }
}
