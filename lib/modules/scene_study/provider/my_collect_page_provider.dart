import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/modules/scene_study/pages/topic_card.dart';

class MyCollectPageProvider extends ChangeNotifier {
  List<CommonSceneStructure> collectList = [];
  int curPage = 0;
  int totalPage = 1;
  /// 是否正在加载下一页数据
  bool isPageLoading = false;

  Future<void> loadData() async {
    // 获取收藏列表
    await pageGetCollectList();
    notifyListeners();
  }

  // 分页获取收藏列表
  Future<void> pageGetCollectList() async {
    if (curPage >= totalPage) return;

    final response = await Api.getMyCollectSceneList(
      curPage + 1,
      10,
    );
    totalPage = response.data!.totalPages!;
    curPage = response.data!.currentPage!;

    // 列表填充
    collectList.addAll(response.data!.items!.map((e) => CommonSceneStructure(
          provider: SceneProvider.values.byName(e.dialogueProvider!),
          topicCode: e.topicCode!,
          title: e.chinese ?? '',
          imageUrl: e.imageUrl ?? '',
          nickName: e.nickname ?? '',
          courseLevelName: e.courseLevelName,
          usageCount: e.usageCount ?? 0,
          target: e.target!,
        )));

    notifyListeners();
  }

  Future<void> getNextPageData() async {
    if (isPageLoading) return;
    isPageLoading = true;
    await pageGetCollectList();
    isPageLoading = false;
  }

  // 刷新数据
  Future<void> refreshData() async {
    // 重置页码和数据
    curPage = 0;
    totalPage = 1;
    collectList.clear();
    
    // 重新加载数据
    await pageGetCollectList();
    notifyListeners();
  }
}
