import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/modules/scene_study/pages/topic_card.dart';

class CreateTabListItem {
  int status;
  String title;
  int count;
  // 是否选中
  bool isSelected = false;
  List<CommonSceneStructure> sceneList = [];
  // 当前页码
  int curPgae = 0;
  // 总页码
  int totalPage = 1;

  CreateTabListItem({required this.status, required this.title, required this.count});
}

class MyCreatePageProvider extends ChangeNotifier {
  List<CreateTabListItem> createTabList = [];
  int curTabIndex = 0;
  /// 是否正在加载下一页数据
  bool isPageLoading = false;

  Future<void> loadData() async {
    await initTabList();
    await pageGetCreateList();
    notifyListeners();
  }

  // 初始化 createList
  initTabList() async {
    final response = await Api.getMyCreateListStatusCategory(); 
    if (response.data != null) {
      response.data!.forEach((element) {
        createTabList.add(CreateTabListItem(status: element.value ?? 0, title: element.key ?? '全部', count: element.count ?? 0));
      });
    }
  }

  // 分页获取收藏列表
  Future<void> pageGetCreateList() async {
    final item = createTabList[curTabIndex];
    if (item.curPgae >= item.totalPage) return;

    final response = await Api.getMyCreateCustomeSceneList(
      item.curPgae + 1,
      10,
      item.status,
    );
    item.totalPage = response.data!.totalPages!;
    item.curPgae = response.data!.currentPage!;
    // 列表填充
    item.sceneList.addAll(response.data!.items!.where((element) => [1,2,3].contains(element.status)).map((e) => CommonSceneStructure(
          provider: SceneProvider.community,
          topicCode: e.topicCode ?? '',
          title: e.chinese ?? '',
          imageUrl: e.imageUrl ?? '',
          nickName: e.nickName ?? '',
          courseLevelName: '',
          usageCount: 0,
          target: e.target ?? '',
          status: e.status,
          rejectReason: e.rejectReason,
        )));
    notifyListeners();
  }

  Future<void> getNextPageData() async {
    if (isPageLoading) return;
    isPageLoading = true;
    await pageGetCreateList();
    isPageLoading = false;
  }

  // 选择tab
  Future<void> selectTab(String title) async {
    curTabIndex = createTabList.indexWhere((element) => element.title == title);
    pageGetCreateList();
    notifyListeners();
  }
  
  // 刷新数据
  Future<void> refreshData() async {
    // 重置所有tab的页码和数据
    for (var tab in createTabList) {
      tab.curPgae = 0;
      tab.totalPage = 1;
      tab.sceneList.clear();
    }
    
    // 重新加载tab列表和当前选中的tab数据
    await initTabList();
    await pageGetCreateList();
    notifyListeners();
  }
}
