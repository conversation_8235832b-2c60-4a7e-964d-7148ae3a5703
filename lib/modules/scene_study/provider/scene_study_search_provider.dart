import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../pages/topic_card.dart';

const String SEARCH_HISTORY_KEY = 'secne_search_history';

class SceneCategory {
  String name;
  List<CommonSceneStructure> sceneList = [];
  // 当前第几页
  int curPage = 0;
  // 总页数
  int totalPage = 1;

  SceneCategory({required this.name});
}

class SceneStudySearchProvider extends ChangeNotifier {
  String searchQuery = '';
  List<String> searchHistory = [];

  List<SceneCategory> searchResults = [
    SceneCategory(name: '主题精选'),
    SceneCategory(name: '社区场景'),
  ];
  // 当前选中的搜索结果分类索引
  int currentSearchResultIndex = 0;

  // 是否正在搜索
  bool isSearching = false;
  // 输入框是否属于聚焦状态
  bool isInputFocused = false;

  loadData() async {
    loadSearchHistory();
  }

  // 加载搜索历史
  Future<void> loadSearchHistory() async {
    final prefs = await SharedPreferences.getInstance();
    searchHistory = prefs.getStringList(SEARCH_HISTORY_KEY) ?? [];
    notifyListeners();
  }

  Future<void> addToSearchHistory(String query) async {
    if (!searchHistory.contains(query)) {
      searchHistory.insert(0, query); // 添加到开头
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(SEARCH_HISTORY_KEY, searchHistory);
    }
  }

  void performSearch({String? query}) async {
    if (query == null || query.isEmpty) {
      return;
    }

    searchQuery = query;
    // 加入到历史中, 加到第一位
    addToSearchHistory(query);
    // TODO 如果正在搜索, 则取消上个请求, 等http模块改版完再处理

    isSearching = true;
    notifyListeners();

    await getSystemThemeSceneSearchResults();
    await getCustomSceneSearchResults();

    isSearching = false;
    notifyListeners();
  }

  void clearSearch() {
    searchQuery = '';
    notifyListeners();
  }

  void removeFromHistory(String item) {
    searchHistory.remove(item);
    notifyListeners();
  }

  void clearSearchHistory() async {
    searchHistory.clear();
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(SEARCH_HISTORY_KEY);
    notifyListeners();
  }

  // 点击搜索结果分类
  void selectSearchResultCategory(int index) {
    currentSearchResultIndex = index;
    notifyListeners();
  }

  // 输入框聚焦状态改变
  void inputFocusedChange(bool focused) {
    isInputFocused = focused;
    notifyListeners();
  }

  // 获取系统主题场景搜索结果
  Future<void> getSystemThemeSceneSearchResults() async {
    final item = searchResults[0];
    if (item.curPage >= item.totalPage) return;

    final response = await Api.getSystemSceneList(
      item.curPage + 1,
      10,
      searchQuery,
      '',
      '',
    );
    item.totalPage = response.data!.totalPages!;
    item.curPage = response.data!.currentPage!;
    // 列表填充
    item.sceneList.addAll(response.data!.items!.map((e) => CommonSceneStructure(
          provider: SceneProvider.system,
          topicCode: e.topicCode ?? '',
          title: e.chinese ?? '',
          imageUrl: e.imageUrl ?? '',
          nickName: '',
          courseLevelName: '',
          usageCount: e.usageCount ?? 0,
          target: e.target ?? '',
        )));
    notifyListeners();
  }

  // 获取自定义场景搜索结果
  Future<void> getCustomSceneSearchResults() async {
    final item = searchResults[1];
    if (item.curPage >= item.totalPage) return;

    final response =
        await Api.getCustomSceneList(item.curPage + 1, 10, searchQuery, '');
    item.totalPage = response.data!.totalPages!;
    item.curPage = response.data!.currentPage!;
    // 列表填充
    item.sceneList.addAll(response.data!.items!.map((e) => CommonSceneStructure(
          provider: SceneProvider.community,
          topicCode: e.topicCode ?? '',
          title: e.chinese ?? '',
          imageUrl: e.imageUrl ?? '',
          nickName: e.nickName ?? '',
          courseLevelName: '',
          usageCount: e.usageCount ?? 0,
          target: e.target ?? '',
        )));
    notifyListeners();
  }

  Future<void> getNextPageData() async {
    if (currentSearchResultIndex == 0) {
      await getSystemThemeSceneSearchResults();
    } else {
      await getCustomSceneSearchResults();
    }
  }
}
