import 'dart:async';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/alipay/cubit/alipay_cubit.dart';
import 'package:flutter_app_kouyu/common/config/constant.dart';
import 'package:flutter_app_kouyu/common/http/models/promotion_config/promotion_config.dart';
import 'package:flutter_app_kouyu/common/http/models/vip_list_model/data.dart'
    as vip_data;
import 'package:flutter_app_kouyu/common/http/models/vip_list_model/icon_list.dart';
import 'package:flutter_app_kouyu/common/in_app_purchase/cubit/in_app_purchase_cubit.dart';
import 'package:flutter_app_kouyu/common/in_app_purchase/in_app_purchase.dart';
import 'package:flutter_app_kouyu/common/utils/log.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/vip/cubit/open_vip_cubit.dart';
import 'package:flutter_app_kouyu/modules/vip/view/android_pay_widget.dart';
import 'package:flutter_app_kouyu/repository/vip_repository.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/weview_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_platform_alert/flutter_platform_alert.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class VipOpenPage extends StatefulWidget {
  const VipOpenPage({super.key});
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const VipOpenPage());
  }

  @override
  State<VipOpenPage> createState() => _VipOpenPageState();
}

class _VipOpenPageState extends State<VipOpenPage>
    with TickerProviderStateMixin {
  Timer? _timer;
  late AnimationController _animationController;
  late Animation<double> _animation;
  late AnimationController _purchaseAnimationController;
  late Animation<Offset> _purchaseAnimation;

  // 添加显示的购买记录数量
  final int displayCount = 1;
  // 当前显示的购买记录起始索引
  int currentStartIndex = 0;
  // 添加记录长度缓存变量
  int cachedRecordLength = 0;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _animation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.1)
            .chain(CurveTween(curve: Curves.easeOut)),
        weight: 50,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.1, end: 1.0)
            .chain(CurveTween(curve: Curves.easeIn)),
        weight: 50,
      ),
    ]).animate(_animationController);

    _animationController.repeat();

    _purchaseAnimationController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    _purchaseAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: const Offset(-1.0, 0.0),
    ).animate(CurvedAnimation(
      parent: _purchaseAnimationController,
      curve: Curves.easeInOut,
    ));

    _timer = Timer.periodic(const Duration(seconds: 7), (timer) {
      if (mounted) {
        setState(() {
          if (cachedRecordLength > 0) {
            currentStartIndex =
                (currentStartIndex + displayCount) % cachedRecordLength;
          }
        });
        // print("currentStartIndex===: $currentStartIndex");

        // 只重置动画控制器，不尝试访问context
        _purchaseAnimationController.reset();
        _purchaseAnimationController.forward();

        // 使用缓存的记录长度更新索引
      }
    });

    // 立即开始第一次动画
    _purchaseAnimationController.forward();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _animationController.dispose();
    _purchaseAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RepositoryProvider<VipReponsitory>(
      create: (context) => VipReponsitory(),
      child: MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (context) => OpenVipCubit(
                  vipReponsitory:
                      RepositoryProvider.of<VipReponsitory>(context))
                ..getPromotionConfig()
                ..getVipList()
                ..getUserInfo()
                ..getPurchaseRecord(),
            ),
            BlocProvider(
              create: (context) => PayCubit(
                callBack: (p0) {},
              ),
            ),
          ],
          child: Stack(
            children: [_body(), _buildPurchaseRecord()],
          )),
    );
  }

  BlocProvider<OpenVipCubit> _body() {
    return BlocProvider<OpenVipCubit>(
      create: (context) => OpenVipCubit(
          vipReponsitory: RepositoryProvider.of<VipReponsitory>(context))
        ..getPromotionConfig()
        ..getVipList()
        ..getUserInfo(),
      child: MultiBlocListener(
        listeners: [
          BlocListener<InAppPurchaseCubit, InAppPurchaseState>(
            listener: (context, state) {
              switch (state.status) {
                case null:
                  break;
                case InAppPurchaseStep.pending:
                  EasyLoading.show(status: "购买中");
                  break;
                case InAppPurchaseStep.purchased:
                  EasyLoading.showSuccess("购买成功");
                  break;
                case InAppPurchaseStep.verified:
                  //验签成功
                  context.read<OpenVipCubit>().getUserInfo();

                  break;
                case InAppPurchaseStep.error:
                  EasyLoading.showError("支付失败");
                  break;
                case InAppPurchaseStep.restored:
                  EasyLoading.show(status: "购买中");
                  break;
                case InAppPurchaseStep.canceled:
                  EasyLoading.showError("支付取消");
                  break;
              }
            },
          ),
        ],
        child: BlocBuilder<OpenVipCubit, OpenVipState>(
          builder: (context, state) {
            int days = state.differenceDuration?.inDays ?? 0;
            String hours = state.differenceDuration?.inHours
                    .remainder(24)
                    .toString()
                    .padLeft(2, '0') ??
                '00';
            String minutes =
                (state.differenceDuration?.inMinutes.remainder(60) ?? 0 % 60)
                    .toString()
                    .padLeft(2, '0');
            String seconds =
                (state.differenceDuration?.inSeconds.remainder(60) ?? 0 % 60)
                    .toString()
                    .padLeft(2, '0');

            return Stack(
              children: [
                Container(
                  color: Colors.white,
                  width: double.infinity,
                  height: double.infinity,
                ),
                Image.asset(
                  "images/open_vip_bg.png",
                  width: double.infinity,
                  fit: BoxFit.fill,
                ),
                Scaffold(
                  backgroundColor: Colors.transparent,
                  appBar: AppBar(
                    leadingWidth: 300,
                    backgroundColor: Colors.transparent,
                    leading: CustomAppbar.leftWidget(context, text: "开通口语学习会员"),
                  ),
                  body: ListView(
                    children: [
                      SizedBox(
                        height: 90,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(20, 10, 20, 10),
                          child: Row(
                            children: [
                              Container(
                                  height: 60,
                                  width: 60,
                                  decoration: BoxDecoration(
                                      color: Colors.white,
                                      border: Border.all(
                                          width: 2,
                                          color: state.userInfoModel?.data
                                                      ?.memberType ==
                                                  1
                                              ? const Color(0xFFFFCE46)
                                              : Colors.white),
                                      borderRadius: const BorderRadius.all(
                                          Radius.circular(30))),
                                  child: Image.network(
                                      state.userInfoModel?.data?.avatarUrl ??
                                          kDefaultIconUrl)),
                              const SizedBox(
                                width: 12,
                              ),
                              Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Text(
                                            state.userInfoModel?.data
                                                    ?.nickname ??
                                                "_",
                                            style: const TextStyle(
                                                fontSize: 20,
                                                fontWeight: FontWeight.w800,
                                                color: Color(0xFF061B1F))),
                                        const SizedBox(
                                          width: 6,
                                        ),
                                        Container(
                                          height: 16,
                                          padding: const EdgeInsets.all(2),
                                          decoration: const BoxDecoration(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(6))),
                                          child: Row(
                                            children: [
                                              Image.asset(
                                                "images/vip_small.png",
                                              ),
                                              Text(
                                                  state.userInfoModel?.data
                                                          ?.membershipLevel ??
                                                      "_",
                                                  style: const TextStyle(
                                                      fontSize: 10,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      color:
                                                          Color(0xFF061B1F))),
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 4,
                                    ),
                                    Text(
                                        state.userInfoModel?.data?.memberType ==
                                                1
                                            ? state.userInfoModel?.data
                                                    ?.expirationDate +
                                                "日到期"
                                            : "尚未开通会员",
                                        style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w400,
                                            color: Color(0xFF646E70)))
                                  ])
                            ],
                          ),
                        ),
                      ),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
                        decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(20),
                                topRight: Radius.circular(20))),
                        child: BlocBuilder<OpenVipCubit, OpenVipState>(
                          builder: (context, state) {
                            return Column(
                              children: [
                                // Text("购买记录"),

                                Visibility(
                                  visible:
                                      state.promotionConfig?.data?.logoUrl !=
                                          null,
                                  child: Container(
                                    height: 44,
                                    decoration: BoxDecoration(
                                        gradient: const LinearGradient(
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight,
                                            colors: [
                                              Color(0xffFFE5EA),
                                              Color(0xffFFF4F2),
                                            ]),
                                        borderRadius:
                                            BorderRadius.circular(12)),
                                    margin:
                                        const EdgeInsets.fromLTRB(0, 16, 0, 20),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const SizedBox(
                                          width: 12,
                                        ),
                                        Image.network(
                                          state.promotionConfig?.data
                                                  ?.logoUrl ??
                                              "",
                                          height: 12,
                                          fit: BoxFit.fitHeight,
                                        ),
                                        const SizedBox(
                                          width: 4,
                                        ),
                                        Text(
                                          state.promotionConfig?.data?.slogan ??
                                              "",
                                          style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFFFA5151)),
                                        ),
                                        Container(
                                          height: 14,
                                          padding: const EdgeInsets.only(
                                              left: 4, right: 4),
                                          decoration: const BoxDecoration(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(4)),
                                            color: Color(0x1aD81535),
                                          ),
                                          child: Text(
                                            days.toString(),
                                            style: const TextStyle(
                                                fontSize: 12,
                                                height: 1.2,
                                                fontWeight: FontWeight.w400,
                                                color: Color(0xffFF002A)),
                                          ),
                                        ),
                                        const Text(
                                          "天",
                                          style: TextStyle(
                                              fontSize: 12,
                                              height: 1.2,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFFFA5151)),
                                        ),
                                        Container(
                                          height: 14,
                                          padding: const EdgeInsets.only(
                                              left: 4, right: 4),
                                          decoration: const BoxDecoration(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(4)),
                                            color: Color(0x1aD81535),
                                          ),
                                          child: Text(
                                            hours,
                                            style: const TextStyle(
                                                fontSize: 12,
                                                height: 1.2,
                                                fontWeight: FontWeight.w400,
                                                color: Color(0xFFFA5151)),
                                          ),
                                        ),
                                        const Text(
                                          " : ",
                                          style: TextStyle(
                                              fontSize: 12,
                                              height: 1.2,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFFFA5151)),
                                        ),
                                        Container(
                                          height: 14,
                                          padding: const EdgeInsets.only(
                                              left: 4, right: 4),
                                          decoration: const BoxDecoration(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(4)),
                                            color: Color(0x1aD81535),
                                          ),
                                          child: Text(
                                            minutes,
                                            textAlign: TextAlign.center,
                                            style: const TextStyle(
                                                fontSize: 12,
                                                height: 1.2,
                                                fontWeight: FontWeight.w400,
                                                color: Color(0xFFFA5151)),
                                          ),
                                        ),
                                        const Text(
                                          " : ",
                                          style: TextStyle(
                                              fontSize: 12,
                                              height: 1.2,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFFFA5151)),
                                        ),
                                        Container(
                                          height: 14,
                                          padding: const EdgeInsets.only(
                                              left: 4, right: 4),
                                          decoration: const BoxDecoration(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(4)),
                                            color: Color(0x1aD81535),
                                          ),
                                          child: Text(
                                            seconds,
                                            style: const TextStyle(
                                                fontSize: 12,
                                                height: 1.2,
                                                fontWeight: FontWeight.w400,
                                                color: Color(0xFFFA5151)),
                                          ),
                                        ),
                                        const Text(
                                          " 后结束",
                                          style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFFFA5151)),
                                        ),
                                        const SizedBox(
                                          width: 12,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                for (var i = 0;
                                    i < (state.vipListModel?.data ?? []).length;
                                    i++)
                                  _priceWidget(
                                      data: state.vipListModel!.data![i],
                                      selectData: state.selectData,
                                      promotionConfig: state.promotionConfig,
                                      context: context),
                                const SizedBox(
                                  height: 16,
                                ),
                                const Row(
                                  children: [
                                    Text("会员专属权益",
                                        style: TextStyle(
                                            fontWeight: FontWeight.w800,
                                            fontSize: 18,
                                            color: Color(0xFF061B1F))),
                                    Spacer(),
                                    Text(""),
                                    // Text(
                                    //     "${state.selectData?.purchased ?? "_"}人已开通",
                                    //     style: const TextStyle(
                                    //         fontWeight: FontWeight.w400,
                                    //         fontSize: 14,
                                    //         color: Color(0xFF646E70)))
                                  ],
                                ),
                              ]..addAll([
                                  const SizedBox(
                                    height: 12,
                                  ),
                                  for (var i = 0;
                                      i <
                                          (state.selectData?.iconList ?? [])
                                                      .length /
                                                  3 -
                                              ((state.selectData?.iconList ??
                                                                  [])
                                                              .length %
                                                          3 >
                                                      0
                                                  ? 0
                                                  : 1);
                                      i++)
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceAround,
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: _vipContentWidget(
                                              state.selectData!.getIcon(i * 3)),
                                        ),
                                        Expanded(
                                          child: _vipContentWidget(state
                                              .selectData!
                                              .getIcon(i * 3 + 1)),
                                        ),
                                        Expanded(
                                          child: _vipContentWidget(state
                                              .selectData!
                                              .getIcon(i * 3 + 2)),
                                        ),
                                      ],
                                    ),
                                  const SizedBox(
                                    height: 12,
                                  ),
                                ]),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  bottomNavigationBar: BlocBuilder<OpenVipCubit, OpenVipState>(
                    builder: (context, state) {
                      return Container(
                        color: Colors.white,
                        height: 138,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _protocolWidget(context),
                            buyWidget(
                                state.selectData, state.differenceDuration),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _vipContentWidget(IconList? icon) {
    if (icon == null) {
      return const Text("");
    }
    return Center(
      child: Column(
        children: [
          Image.network(
            icon.iconUrl ?? "_",
            width: 40,
            height: 40,
          ),
          const SizedBox(
            height: 8,
          ),
          Text(icon.name ?? "_",
              style: const TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                  color: Color(0xFF061B1F))),
          Text(icon.remark ?? "_",
              style: const TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                  color: Color(0xFF646E70))),
          const SizedBox(
            height: 12,
          )
        ],
      ),
    );
  }

  Widget buyWidget(vip_data.Data? selectData, Duration? differenceDuration) {
    int days = differenceDuration?.inDays ?? 0;
    String hours =
        differenceDuration?.inHours.remainder(24).toString().padLeft(2, '0') ??
            '00';
    String minutes = (differenceDuration?.inMinutes.remainder(60) ?? 0 % 60)
        .toString()
        .padLeft(2, '0');
    String seconds = (differenceDuration?.inSeconds.remainder(60) ?? 0 % 60)
        .toString()
        .padLeft(2, '0');

    return BlocBuilder<InAppPurchaseCubit, InAppPurchaseState>(
      builder: (context, state) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            SizedBox(
              width: double.infinity,
              height: 62,
              child: Row(
                children: [
                  Center(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.baseline,
                      textBaseline: TextBaseline.alphabetic,
                      children: [
                        const SizedBox(width: 20),
                        Text(
                          '${selectData?.currency}${selectData?.price}',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w800,
                            color: Color(0xFFFA5151),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: () async {
                      if (LoginUtil.currentUserInfoModel()?.data?.isVisitor ==
                          true) {
                        CustomButton value =
                            await FlutterPlatformAlert.showCustomAlert(
                                windowTitle: "提示",
                                text: "当前为游客登录，购买商品可能无法在其他设备和平台使用。建议您切换账号登录",
                                positiveButtonTitle: "切换账号",
                                negativeButtonTitle: "继续购买",
                                options: PlatformAlertOptions());
                        if (value == CustomButton.negativeButton) {
                          _pay(context, selectData!);
                        } else {
                          Navigator.of(context).pushNamedAndRemoveUntil(
                              "/login", (route) => false);
                        }
                      } else {
                        //确认购买
                        _pay(context, selectData!);
                      }
                    },
                    child: Container(
                      width: 160,
                      height: double.infinity,
                      color: const Color(0xFFFFE991),
                      child: const Center(
                        child: Text(
                          "去支付",
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w800,
                            color: Color(0xFF061B1F),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (differenceDuration != null)
              Positioned(
                top: -40,
                right: 15,
                child: ScaleTransition(
                  scale: _animation,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFE5EA),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          days > 0
                              ? "${days}天${hours}时${minutes}分${seconds}秒后结束优惠"
                              : "${hours}时${minutes}分${seconds}秒后结束优惠",
                          style: const TextStyle(
                            color: Color(0xFFFA5151),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            if (differenceDuration != null)
              Positioned(
                top: -12,
                right: 80,
                child: CustomPaint(
                  size: const Size(16, 8),
                  painter: TrianglePainter(),
                ),
              ),
          ],
        );
      },
    );
  }

  void _pay(BuildContext context, vip_data.Data selectData) async {
    if (Platform.isAndroid) {
      EasyLoading.show(status: "支付中");
      context.read<PayCubit>().alipay(selectData.packageType!, (status) {
        if (status.resultStatus == 9000) {
          //支付成功
          EasyLoading.showToast("支付成功");
          context.read<OpenVipCubit>().getUserInfo();
          return;
        }
        if (status.resultStatus == 6001) {
          EasyLoading.showToast("已取消支付");
          return;
        }
        EasyLoading.showToast("支付失败");
      });

      // final result = await showModalBottomSheet(
      //     context: context,
      //     useSafeArea: false,
      //     backgroundColor: Colors.transparent,
      //     constraints: BoxConstraints(
      //         maxHeight: MediaQuery.of(context).size.height -
      //             MediaQuery.of(context).padding.top),
      //     builder: (context) {
      //       return AndroidPayWidget(
      //         price: selectData.price ?? "",
      //         packageType: selectData.packageType!,
      //       );
      //     });
      // if (result == PayResult.success) {
      //   //验签成功
      //   context.read<OpenVipCubit>().getUserInfo();
      // }
    } else {
      //确认购买
      context.read<InAppPurchaseCubit>().buy(selectData.productId!);
    }
  }

  Widget _protocolWidget(BuildContext context) {
    return Container(
      color: const Color(0xFFF3FBFD),
      height: 42,
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(20, 0, 0, 0),
      child: Row(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              RichText(
                  text: TextSpan(text: "", children: [
                WidgetSpan(
                    alignment: PlaceholderAlignment.middle,
                    child: Image.asset(
                      "images/checkbox-circle-fill.png",
                      width: 18,
                      height: 18,
                    )),
                const TextSpan(
                    text: "我已阅读并同意",
                    style: TextStyle(
                        color: Color(0xFF646E70),
                        fontSize: 12,
                        fontWeight: FontWeight.w400)),
                TextSpan(
                    style: const TextStyle(
                        color: Color(0xFFFF8F1F),
                        fontSize: 12,
                        fontWeight: FontWeight.w400),
                    text: '《会员协议》',
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Navigator.push(context,
                            MaterialPageRoute(builder: (context) {
                          return const WebviewWidget(
                            url:
                                "https://cos.xinquai.com/agreements_and_notes/ios_Membership_Agreement.html",
                          );
                        }));
                      }),
              ])),
            ],
          ),
        ],
      ),
    );
  }

  Widget _priceWidget(
      {required vip_data.Data data,
      required vip_data.Data? selectData,
      required PromotionConfig? promotionConfig,
      required BuildContext context}) {
    bool selectd = (selectData == data);
    return GestureDetector(
      onTap: () => context.read<OpenVipCubit>().selected(data),
      child: Container(
        width: double.infinity,
        // height: 80,
        clipBehavior: Clip.antiAlias,
        margin: const EdgeInsets.only(top: 4, bottom: 4),
        decoration: BoxDecoration(
            border: Border.all(
                color:
                    selectd ? const Color(0xFF061B1F) : const Color(0xFFDDEDF0),
                width: 2),
            borderRadius: const BorderRadius.all(Radius.circular(12))),
        child: Stack(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(data.name ?? "",
                              style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w800,
                                  color: Color(0xFF061B1F))),
                          const SizedBox(
                            width: 4,
                          ),
                          Visibility(
                            visible: promotionConfig?.data?.labelUrl != null,
                            child: Image.network(
                              promotionConfig?.data?.labelUrl ?? "",
                              height: 16,
                              fit: BoxFit.fitHeight,
                            ),
                          ),
                          Visibility(
                            visible: promotionConfig?.data?.labelUrl != null,
                            child: const SizedBox(
                              width: 4,
                            ),
                          ),
                          Visibility(
                            visible: data.isRecommend == "1",
                            child: Container(
                              height: 16,
                              padding: const EdgeInsets.fromLTRB(4, 0, 4, 0),
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      color: const Color(0xFFFA5151), width: 1),
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(6))),
                              child: const Center(
                                child: Text("推荐",
                                    style: TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.w400,
                                        color: Color(0xFFFA5151))),
                              ),
                            ),
                          )
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(data.promotionalSlogan ?? "",
                          style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: Color(0xFFFA5151))),
                      const SizedBox(height: 16)
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.baseline,
                      textBaseline: TextBaseline.alphabetic,
                      children: [
                        Text('${data.currency}${data.price}',
                            style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.w800,
                                color: Color(0xFFFA5151))),
                      ],
                    ),
                    Row(
                      // mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text('${data.currency}${data.originalPrice}',
                            style: const TextStyle(
                                fontSize: 12,
                                decoration: TextDecoration.lineThrough,
                                fontWeight: FontWeight.w400,
                                color: Color(0xFF9E9E9E))),
                        const SizedBox(
                          width: 8,
                        ),
                        Container(
                          padding: const EdgeInsets.fromLTRB(4, 2, 4, 2),
                          decoration: const BoxDecoration(
                              color: Color.fromARGB(25, 216, 21, 53),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(6))),
                          child: Center(
                            child: Text(data.discount ?? "_",
                                style: const TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w800,
                                    color: Color(0xFFFF002A))),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 18),
                  ],
                ),
                const SizedBox(width: 12),
              ],
            ),
            if (selectd)
              Positioned(
                  right: 0,
                  top: 0,
                  child: Image.asset(
                    "images/vip_icon_select.png",
                    height: 16,
                  ))
          ],
        ),
      ),
    );
  }

  Widget _buildPurchaseRecord() {
    return BlocBuilder<OpenVipCubit, OpenVipState>(
      builder: (context, state) {
        final records = state.purchaseRecordModel?.data ?? [];
        // 更新缓存的记录长度
        cachedRecordLength = records.length;

        if (records.isEmpty) {
          return const SizedBox();
        }

        return Stack(
          children: List.generate(displayCount, (index) {
            final recordIndex = (currentStartIndex + index) % records.length;
            final verticalOffset = index * 50.0.w; // 垂直间距

            return Positioned(
              top: 360.0.w + verticalOffset,
              left: 0,
              right: 0,
              child: SlideTransition(
                position: _purchaseAnimation,
                child: Center(
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.w),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(20.w),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Image.network(
                          records[recordIndex].imageUrl ?? "",
                          width: 30.w,
                          height: 30.w,
                          fit: BoxFit.cover,
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          records[recordIndex].name ?? "",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 25.w,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }
}

class TrianglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFFFE5EA)
      ..style = PaintingStyle.fill;

    final path = Path()
      ..moveTo(size.width / 2, size.height)
      ..lineTo(0, 0)
      ..lineTo(size.width, 0)
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
