import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/alipay/cubit/alipay_cubit.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

enum PayType { alipay, wxpay }

enum PayResult {
  success,
  fail,
  cancel,
}

class AndroidPayWidget extends StatefulWidget {
  const AndroidPayWidget(
      {super.key, required this.price, required this.packageType});
  final String price;
  final int packageType;

  @override
  State<AndroidPayWidget> createState() => _AndroidPayWidgetState();
}

class _AndroidPayWidgetState extends State<AndroidPayWidget> {
  PayType _payType = PayType.alipay;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<PayCubit>(
      create: (context) => PayCubit(),
      child: BlocBuilder<PayCubit, AlipayState>(
        builder: (context, state) {
          return Container(
            width: double.infinity,
            padding: EdgeInsets.only(
                bottom: 68.w, left: 48.w, right: 48.w, top: 48.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(40.w), color: Colors.white),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Text(
                      "选择支付方式",
                      style: style_1_36,
                    ),
                    const Spacer(),
                    GestureDetector(
                      child: const Icon(Icons.close),
                      onTap: () {
                        Navigator.pop(context);
                      },
                    )
                  ],
                ),
                SizedBox(
                  height: 40.w,
                ),
                Text(
                  "￥${widget.price}",
                  style: TextStyle(
                      fontSize: 64.w,
                      wordSpacing: 1,
                      color: const Color(0xFFFA5151),
                      fontWeight: FontWeight.w700),
                ),
                SizedBox(
                  height: 24.w,
                ),

                ///支付宝按钮
                _payItemWidget(PayType.alipay),
                SizedBox(
                  height: 24.w,
                ),

                ///支付宝按钮
                _payItemWidget(PayType.wxpay),
                SizedBox(
                  height: 74.w,
                ),
                //确认按钮
                GestureDetector(
                  onTap: () {
                    _pay(context, widget.packageType);
                  },
                  child: Container(
                    width: double.infinity,
                    height: 102.w,
                    decoration: BoxDecoration(
                        color: const Color(0xFF00A0E9),
                        borderRadius: BorderRadius.circular(20.w)),
                    child: Center(
                      child: Text(
                        "确认",
                        style: style_1_36.copyWith(color: Colors.white),
                      ),
                    ),
                  ),
                )
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _payItemWidget(PayType type) {
    return GestureDetector(
      onTap: () {
        _payType = type;
        setState(() {});
      },
      child: Container(
        height: 144.w,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.w),
            border: Border.all(color: ColorUtil.separated, width: 1)),
        child: Center(
          child: Row(
            children: [
              SizedBox(
                width: 32.w,
              ),
              Image.asset(
                type == PayType.alipay
                    ? "images/pay_alipay.png"
                    : "images/pay_wechat.png",
                width: 80.w,
                height: 80.w,
              ),
              SizedBox(
                width: 24.w,
              ),
              Text(
                type == PayType.alipay ? "支付宝支付" : "微信支付",
                style: style_1_32_400.copyWith(color: const Color(0xFF272733)),
              ),
              const Spacer(),
              Image.asset(
                _payType == type
                    ? "images/check_circle_blue.png"
                    : "images/un_check_circle.png",
                width: 30.w,
                height: 30.w,
              ),
              SizedBox(
                width: 32.w,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _pay(BuildContext context, int packageType) {
    EasyLoading.show(status: "支付中");
    if (_payType == PayType.alipay) {
      context.read<PayCubit>().alipay(packageType, (status) {
        if (status.resultStatus == 9000) {
          //支付成功
          EasyLoading.showToast("支付成功");
          Navigator.of(context).pop(PayResult.success);
          return;
        }
        if (status.resultStatus == 6001) {
          EasyLoading.showToast("已取消支付");
          Navigator.of(context).pop(PayResult.cancel);

          return;
        }
        Navigator.of(context).pop(PayResult.fail);
        EasyLoading.showToast("支付失败");
      });
      return;
    }

    if (_payType == PayType.wxpay) {
      context.read<PayCubit>().wxpay(packageType, (status) {
        if (status.isSuccessful) {
          //支付成功
          EasyLoading.showToast("支付成功");
          Navigator.of(context).pop(PayResult.success);
          return;
        }
        if (status.isCancelled) {
          EasyLoading.showToast("已取消支付");
          Navigator.of(context).pop(PayResult.cancel);

          return;
        }
        Navigator.of(context).pop(PayResult.fail);
        EasyLoading.showToast("支付失败");
      });
      return;
    }
  }
}
