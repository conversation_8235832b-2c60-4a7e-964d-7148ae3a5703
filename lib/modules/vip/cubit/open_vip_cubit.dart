import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/promotion_config/promotion_config.dart';
import 'package:flutter_app_kouyu/common/http/models/purchase_record_model/purchase_record_model.dart';
import 'package:flutter_app_kouyu/common/http/models/user_info_model/user_info_model.dart';
import 'package:flutter_app_kouyu/common/http/models/vip_list_model/data.dart';
import 'package:flutter_app_kouyu/common/http/models/vip_list_model/vip_list_model.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/repository/vip_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'open_vip_state.dart';

class OpenVipCubit extends Cubit<OpenVipState> {
  final VipReponsitory vipReponsitory;
  Timer? _timer;
  Duration? differenceDuration;


  OpenVipCubit({
    required this.vipReponsitory,
  }) : super(const OpenVipState());
  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }

  void startProgress() {
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      DateTime? time;
      if (state.promotionConfig?.data?.expirationDatetime != null) {
        DateTime expirationDate = DateTime.parse(
            state.promotionConfig?.data?.expirationDatetime as String);
        time = expirationDate.subtract(
            Duration(microseconds: DateTime.now().microsecondsSinceEpoch));
        differenceDuration = expirationDate.difference(DateTime.now());
      }
      emit(state.copyWith(seconds: time, differenceDuration: differenceDuration));
    });
  }

  Future<void> getVipList() async {
    vipReponsitory.getVipList().then((VipListModel model) {
      var selectData = (model.data ?? []).first;
      for (Data data in model.data ?? []) {
        if (data.isRecommend == "1") {
          selectData = data;
          break;
        }
      }
      return emit(state.copyWith(vipListModel: model, selectData: selectData));
    });
  }

  Future<void> getPromotionConfig() async {
    Api.getPromotionConfig().then((PromotionConfig model) {
      //计算时分秒
      DateTime? time;
      if (model.data?.expirationDatetime != null) {
        DateTime expirationDate =
            DateTime.parse(model.data?.expirationDatetime as String);
        time = expirationDate.subtract(
            Duration(microseconds: DateTime.now().microsecondsSinceEpoch));
        differenceDuration = expirationDate.difference(DateTime.now());
        startProgress();
      }
      return emit(state.copyWith(promotionConfig: model, seconds: time, differenceDuration: differenceDuration));
    });
  }

  Future<void> getUserInfo() async {
    LoginUtil.updateUserInfo().then((model) {
      return emit(state.copyWith(userInfoModel: model));
    });
  }

  Future<void> getPurchaseRecord() async {
    Api.getPurchaseRecord().then((PurchaseRecordModel model) {
      if (model.data.isNotEmpty) {
        return emit(state.copyWith(purchaseRecordModel: model));
      }
    });
  }

  void selected(Data data) {
    emit(state.copyWith(selectData: data));
  }

}