// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'open_vip_cubit.dart';

class OpenVipState extends Equatable {
  const OpenVipState(
      {this.vipListModel,
      this.selectData,
      this.userInfoModel,
      this.seconds,
      this.promotionConfig,
      this.differenceDuration,
      this.purchaseRecordModel,
      });
  final VipListModel? vipListModel;
  final Data? selectData;
  final UserInfoModel? userInfoModel;
  final PromotionConfig? promotionConfig;
  final DateTime? seconds;
  final Duration? differenceDuration;
  final PurchaseRecordModel? purchaseRecordModel;
  @override
  List<Object?> get props =>
      [vipListModel, selectData, userInfoModel, promotionConfig, seconds, differenceDuration];

  OpenVipState copyWith(
      {VipListModel? vipListModel,
      Data? selectData,
      UserInfoModel? userInfoModel,
      DateTime? seconds,
      PromotionConfig? promotionConfig,
      Duration? differenceDuration,
      PurchaseRecordModel? purchaseRecordModel,
      }) {
    return OpenVipState(
        vipListModel: vipListModel ?? this.vipListModel,
        selectData: selectData ?? this.selectData,
        userInfoModel: userInfoModel ?? this.userInfoModel,
        promotionConfig: promotionConfig ?? this.promotionConfig,
        seconds: seconds ?? this.seconds,
        differenceDuration: differenceDuration ?? this.differenceDuration,
        purchaseRecordModel: purchaseRecordModel ?? this.purchaseRecordModel);
  }
}
