import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/user_info_model/user_info_model.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'person_info_change_page.dart';

class PersonInfoPage extends StatelessWidget {
  UserInfoModel userInfo;
  PersonInfoPage({super.key, required this.userInfo});
  static Route<void> route(UserInfoModel userInfo) {
    return MaterialPageRoute<void>(builder: (_) => PersonInfoPage(userInfo: userInfo));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
        appBar: AppBar(
          leadingWidth: 300.w,
          elevation: 0,
          backgroundColor: Colors.transparent,
          leading: CustomAppbar.leftWidget(context, text: "个人资料"),
        ),
        backgroundColor: const Color(0xFFEDF6F7),
          body: Stack(
            children: [
              Container(
                width: double.infinity,
                height: 600.w,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage("images/scene_introduce_bg.png"),
                    fit: BoxFit.cover,
                    alignment: Alignment.topCenter,
                    opacity: 1,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 32.w, right: 32.w, top: 250.w),
                child: Column(
                  children: [
                    _tabItem(context, "头像" , Container(
                    width: 80.w,
                    height: 80.w,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    child: Image.network(
                      userInfo.data?.avatarUrl ?? "",
                      fit: BoxFit.cover,
                    ),
                  )),
                  SizedBox(height: 24.w),
                  _tabItem(context, "昵称" , Text(
                    userInfo.data?.nickname ?? "", 
                    style: style_1_28_400,)),
                  ],
                ),
              )
            ],
          ),
    );
  }

  // 资料页的tab
  Widget _tabItem(BuildContext context, String name, Widget rightWidget) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        Navigator.of(context).push(PersonInfoChangePage.route(
          nickname: userInfo.data?.nickname,
          avatarUrl: userInfo.data?.avatarUrl,
          gender: userInfo.data?.sex,
        ));
      },
      child: Container(
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(16.w),
        ),
        child: Row(
          children: [
            Text(name, style: style_1_28_400,),
            const Spacer(),
            Row(
              children: [
                rightWidget,
                SizedBox(width: 16.w),
                Image.asset("images/right_arrow_gray.png", width: 40.w, height: 40.w,),
              ],
            )
          ],
        ),
      ),
    );
  }
}