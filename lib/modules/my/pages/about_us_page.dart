import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/utils/package_util.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutUsPage extends StatelessWidget {
  const AboutUsPage({super.key});
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const AboutUsPage());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          leadingWidth: 300,
          backgroundColor: const Color(0xFFF3FBFD),
          leading: CustomAppbar.leftWidget(context, text: "关于我们"),
        ),
        backgroundColor: const Color(0xFFF3FBFD),
        body: Center(
          child: Column(
            children: [
              const SizedBox(
                height: 40,
              ),
              ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Image.asset(
                  fit: BoxFit.fill,
                  "images/icon_logo.png",
                  width: 100,
                  height: 100,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                PackageUtil.packageInfo.appName,
                style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w800,
                    color: Color(0xFF061B1F)),
              ),
              const SizedBox(height: 4),
              Text(
                "v${PackageUtil.packageInfo.version}",
                style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF8BAFB6)),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  launchUrl(Uri.parse(
                      "https://beian.miit.gov.cn/#/Integrated/index"));
                },
                child: const Text(
                  "ICP备案号：浙ICP备2023019762号-3A",
                  style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF8BAFB6)),
                ),
              ),
              const Text(
                "Copyright©2024 新趋科技有限公司All Right Reserved",
                style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF8BAFB6)),
              ),
              const SizedBox(
                height: 40,
              )
            ],
          ),
        ));
  }
}
