import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/collection_vocabulary_detail/collection_vocabulary_detail.dart';
import 'package:flutter_app_kouyu/common/http/models/collection_vocabulary_detail/data.dart';

import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';
import 'package:flutter_app_kouyu/modules/my/bloc/cubit/vocabulary_alert_provider.dart';
import 'package:flutter_app_kouyu/widgets/common-input-widget/left-ai-middle-btn-right-score.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gif_view/gif_view.dart';
import 'package:provider/provider.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/data.dart'
    as SoeData;

class VocabularyAlertWidget extends StatelessWidget {
  final String vocabulary;
  const VocabularyAlertWidget({super.key, required this.vocabulary});
  static Route<void> route(String vocabulary) {
    return MaterialPageRoute<void>(
        builder: (_) => VocabularyAlertWidget(
              vocabulary: vocabulary,
            ));
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<VocabularyAlertProvider>(
      create: (_) =>
          VocabularyAlertProvider(vocabulary: vocabulary)..refreshData(),
      child: Consumer<VocabularyAlertProvider>(
        builder: (context, model, child) {
          return Container(
            color: Colors.white,
            child: _vocabularyCardItem(model.vocabularyDetail, context),
          );
        },
      ),
    );
  }

  Widget _vocabularyCardItem(
      CollectionVocabularyDetail? detail, BuildContext context) {
    VocabularyAlertProvider model = context.read<VocabularyAlertProvider>();

    Data? vocabulary = detail?.data;
    String? definition = vocabulary?.definition;
    List<String> definitionList = [];
    if (definition != null) {
      definitionList = definition.split("\n");
    }
    return Padding(
      padding:
          EdgeInsets.only(left: 40.w, right: 40.w, bottom: 100.w, top: 40.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(gradient: ColorUtil.blueToGreen),
          ),
          Row(
            children: [
              Text(
                vocabulary?.vocabulary ?? "_",
                style: TextStyle(
                    fontSize: 64.sp,
                    fontWeight: FontWeight.w700,
                    color: color_1),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  if (vocabulary!.collected == true) {
                    model.cancelCollectVocabulary(detail!);
                  } else {
                    model.collectVocabulary(detail!);
                  }
                },
                child: Container(
                  width: 104.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16.w),
                      border: Border.all(width: 1.w, color: separated)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        detail?.data?.collected == true
                            ? "images/start_yellow.png"
                            : "images/start_blue.png",
                        width: 24.w,
                        height: 24.w,
                      ),
                      SizedBox(
                        width: 8.w,
                      ),
                      Text(
                        "收藏",
                        style: style_2_24,
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(
                width: 32.w,
              ),
              GestureDetector(
                child: const Icon(Icons.close),
                onTap: () {
                  Navigator.pop(context);
                },
              )
            ],
          ),
          SizedBox(
            height: 32.w,
          ),
          Row(
            children: [
              _voiceWidget("英", vocabulary?.ukPhonetic ?? "_",
                  vocabulary?.ukPronunciationUrl ?? "_"),
              SizedBox(
                width: 16.w,
              ),
              _voiceWidget("美", vocabulary?.usPhonetic ?? "_",
                  vocabulary?.usPronunciationUrl ?? "_"),
            ],
          ),
          SizedBox(
            height: 32.w,
          ),
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              "释义",
              style: style_2_24,
            ),
          ),
          SizedBox(
            height: 16.w,
          ),
          for (int i = 0; i < definitionList.length; i++)
            _definitionWidget(definitionList[i]),
          // const Spacer(),
          // if (model.soeModel?.data != null) ..._soeList(model.soeModel!.data!),
          // model.voiceState == VoiceStateEnum.init
          //     ? _followWidget(context, vocabulary?.defaultPronunciationUrl,
          //         vocabulary?.score, vocabulary?.userPronunciationUrl)
          //     : _speakingWidget(context),
        ],
      ),
    );
  }

  Row _definitionWidget(String definition) {
    List<String> list = definition.split(". ");
    return Row(
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: [
        SizedBox(
          width: 68.w,
          child: Align(
            alignment: Alignment.topLeft,
            child: Text(
              "${list.first}.",
              style: style_3_28,
            ),
          ),
        ),
        Expanded(
          child: Text(
            list.last,
            style: style_1_28,
          ),
        ),
      ],
    );
  }

  Container _voiceWidget(String type, String content, String url) {
    return Container(
      height: 56.w,
      decoration: BoxDecoration(
          color: btnColor, borderRadius: BorderRadius.circular(40.w)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 24.w,
          ),
          Text(
            type,
            style: style_2_24,
          ),
          Text(
            "/",
            style: style_3_24,
          ),
          Text(
            content,
            style: style_2_24,
          ),
          Text(
            "/",
            style: style_3_24,
          ),
          PlayButtonWidget(
            style: PlayButtonStyle.blue,
            audioUrl: url,
          ),
        ],
      ),
    );
  }

  Row _speakingWidget(BuildContext context) {
    VocabularyAlertProvider model = context.read<VocabularyAlertProvider>();

    return Row(
      children: [
        GestureDetector(
          onTap: () {
            model.stop();
          },
          child: Image(
            image: const AssetImage("images/chat_close.png"),
            width: 80.w,
            height: 80.w,
          ),
        ),
        SizedBox(
          width: 40.w,
        ),
        Expanded(
          child: GifView.asset(
            "images/chat_loading.gif",
            fit: BoxFit.cover,
            width: double.infinity,
            height: 48.w,
            repeat: ImageRepeat.repeat,
          ),
        ),
        SizedBox(
          width: 40.w,
        ),
        GestureDetector(
          onTap: () {
            model.sent();
          },
          child: Image(
            image: const AssetImage("images/chat_send.png"),
            width: 80.w,
            height: 80.w,
          ),
        ),
      ],
    );
  }

  Widget _followWidget(
      BuildContext context, String? url, String? score, String? userUrl) {
    VocabularyAlertProvider model = context.read<VocabularyAlertProvider>();

    return LeftAiMiddleBtnRightScore(
      aiUrl: url ?? "",
      btnOnTap: () {
        model.speakEn();
      },
      score: score ?? "",
      userUrl: userUrl ?? "",
    );
  }

  List<Widget> _soeList(SoeData.Data soeModel) {
    return [
      SizedBox(
        width: double.infinity,
        height: 585.w,
        child: Stack(alignment: Alignment.topCenter, children: [
          Positioned(
              top: 192.w,
              child: Container(
                width: 585.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(40.w),
                  color: const Color(0xFFF0F8FA),
                ),
                child: Column(
                  children: [
                    SizedBox(
                      height: 100.w,
                    ),
                    Text(
                      soeModel.speedComment ?? "_",
                      style: style_1_28_400,
                    ),
                    SizedBox(
                      height: 24.w,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        scoreWidget(soeModel.pronFluency, "流利度"),
                        scoreWidget(soeModel.pronAccuracy, "准确度"),
                        scoreWidget(soeModel.pronCompletion, "完整度"),
                        // scoreWidget("_/分钟", "语速"),
                      ],
                    ),
                    SizedBox(
                      height: 24.w,
                    ),
                    _lineWidget(),
                    SizedBox(
                      height: 24.w,
                    ),
                    Row(
                      children: [
                        SizedBox(
                          width: 40.w,
                        ),
                        Text("${soeModel.speed}", style: style_1_40),
                        SizedBox(
                          width: 8.w,
                        ),
                        Text("单词/分钟", style: style_1_24),
                        const Spacer(),
                        Text(
                          "语速较快，水平不错",
                          style: style_1_28_400,
                        ),
                        SizedBox(
                          width: 40.w,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 40.w,
                    ),
                  ],
                ),
              )),
          Container(
            width: 392.w,
            height: 392.w,
            decoration: BoxDecoration(
              image: const DecorationImage(
                  alignment: Alignment.topCenter,
                  image: AssetImage("images/flollow_read_score_bg.png"),
                  fit: BoxFit.fill),
              borderRadius: BorderRadius.circular(40.w),
            ),
            child: Center(
                child: Text("${soeModel.suggestedScore ?? "_"}",
                    style: style_1_48.copyWith(fontSize: 50.sp))),
          )
        ]),
      ),
      SizedBox(
        height: 48.w,
      ),
    ];
  }

  Widget scoreWidget(int? score, String subTitle) {
    return Column(
      children: [
        Text("${score ?? "_"}",
            style: TextStyle(
                fontSize: 40.sp,
                fontWeight: FontWeight.w700,
                color: getColor(score ?? 0))),
        Text(subTitle, style: style_2_24),
      ],
    );
  }

  Color getColor(int Score) {
    if (Score <= 60) {
      return const Color.fromARGB(255, 249, 50, 0);
    } else if (Score <= 80) {
      return const Color(0xFF061B1F);
    } else if (Score <= 100) {
      return const Color.fromARGB(255, 42, 201, 92);
    }

    return const Color(0xFF061B1F);
  }

  Container _lineWidget() {
    return Container(
      margin: EdgeInsets.only(left: 40.w, right: 40.w),
      height: 1.w,
      width: double.infinity,
      color: ColorUtil.separated,
    );
  }
}
