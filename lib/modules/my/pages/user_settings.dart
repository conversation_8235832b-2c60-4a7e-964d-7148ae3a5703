import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/weview_widget.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_platform_alert/flutter_platform_alert.dart';

class UserSettingsPage extends StatelessWidget {
  const UserSettingsPage({super.key});
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const UserSettingsPage());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leadingWidth: 300,
        backgroundColor: const Color(0xFFF3FBFD),
        leading: CustomAppbar.leftWidget(context, text: "更多设置"),
      ),
      backgroundColor: const Color(0xFFF3FBFD),
      body: Container(
        // padding: const EdgeInsets.fromLTRB(20, 12, 20, 0),
        margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
        decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(12)),
            color: Colors.white),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text("隐私政策"),
              trailing: const Icon(
                Icons.arrow_forward_ios,
                size: 18,
              ),
              onTap: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) {
                  return const WebviewWidget(
                    url:
                        "https://cos.xinquai.com/agreements_and_notes/ios_privacy_agreement.html",
                  );
                }));
              },
            ),
            ListTile(
              title: const Text("服务条款"),
              trailing: const Icon(
                Icons.arrow_forward_ios,
                size: 18,
              ),
              onTap: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) {
                  return const WebviewWidget(
                    url:
                        "https://cos.xinquai.com/agreements_and_notes/ios_Membership_Agreement.html",
                  );
                }));
              },
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(left: 20.0, right: 20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Container(
              height: 52,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12), color: Colors.white),
              child: TextButton(
                  onPressed: () {
                    LoginUtil.logout().then((value) {});
                    Navigator.of(context)
                        .pushNamedAndRemoveUntil("/login", (route) => false);
                  },
                  style: const ButtonStyle(
                    textStyle: MaterialStatePropertyAll(
                        TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
                  ),
                  child: const Center(
                    child: Text("退出登录",
                        style: TextStyle(
                            color: Color(0xFF061B1F),
                            fontSize: 18,
                            fontWeight: FontWeight.w800)),
                  )),
            ),
            const SizedBox(
              height: 20,
            ),
            GestureDetector(
              onTap: () async {
                CustomButton value = await FlutterPlatformAlert.showCustomAlert(
                    windowTitle: "提示",
                    text: "确定要注销吗？",
                    positiveButtonTitle: "取消",
                    negativeButtonTitle: "确定",
                    options: PlatformAlertOptions());
                if (value == CustomButton.negativeButton) {
                  //确认注销
                  Api.unRegister();
                  EasyLoading.showSuccess("注销成功");
                  LoginUtil.logout().then((value) {});

                  Navigator.of(context)
                      .pushNamedAndRemoveUntil("/login", (route) => false);
                }
              },
              child: const Text(
                '注销账号',
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF333333)),
              ),
            )
          ],
        ),
      ),
    );
  }
}
