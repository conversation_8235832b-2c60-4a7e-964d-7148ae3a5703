import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/purpuse_model/data.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class SetupSettingOne extends StatefulWidget {
  const SetupSettingOne({super.key});
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const SetupSettingOne());
  }

  @override
  State<SetupSettingOne> createState() => _SetupSettingOneState();
}

class _SetupSettingOneState extends State<SetupSettingOne> {
  List<String> type = [];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leadingWidth: 300,
        backgroundColor: const Color(0xFFF3FBFD),
        leading: CustomAppbar.leftWidgetWithNoIcon(context, text: "学习偏好设置"),
        // actions: [
        //   GestureDetector(
        //     onTap: () {
        //       Navigator.of(context)
        //           .pushNamedAndRemoveUntil("/home", (route) => false);
        //     },
        //     child: const Text(
        //       "跳过",
        //       style: TextStyle(
        //           fontSize: 14,
        //           fontWeight: FontWeight.w400,
        //           color: Color(0xFF646E70)),
        //     ),
        //   ),
        //   const SizedBox(
        //     width: 12,
        //   )
        // ],
      ),
      backgroundColor: const Color(0xFFF3FBFD),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(20, 20, 20, 130),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 8,
                    // width: 80,
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        color: Color(0xFF41C0FF)),
                  ),
                ),
                const SizedBox(
                  width: 6,
                ),
                Expanded(
                  child: Container(
                    height: 8,
                    // width: 80,
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        color: Color(0x1F41C0FF)),
                  ),
                ),
                const SizedBox(
                  width: 6,
                ),
                Expanded(
                  child: Container(
                    height: 8,
                    // width: 80,
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        color: Color(0x1F41C0FF)),
                  ),
                ),
                const SizedBox(
                  width: 6,
                ),
                Expanded(
                  child: Container(
                    height: 8,
                    // widxth: 80,
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        color: Color(0x1F41C0FF)),
                  ),
                ),
              ],
            ),
            Expanded(
              child: ListView(
                children: [
                  const SizedBox(
                    height: 20,
                  ),
                  const Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        "你学习英语口语的目的",
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w800,
                            color: Color(0xFF061B1F)),
                      ),
                      SizedBox(
                        width: 8,
                      ),
                      Text(
                        "(多选)",
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF646E70)),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  const Text(
                    "老师会根据你的英语水平选择合适的词汇和你对话",
                    style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF646E70)),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  FutureBuilder(
                    future: Api.purpose(),
                    builder: (context, snapshot) {
                      return ListView.separated(
                        separatorBuilder: (context, index) => const SizedBox(
                          height: 10,
                        ),
                        itemBuilder: (context, index) {
                          Data data = snapshot.data!.data![index];
                          return ClipRRect(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(12)),
                            child: Container(
                              color: Colors.white,
                              padding: const EdgeInsets.all(16),
                              child: GestureDetector(
                                behavior: HitTestBehavior.opaque,
                                onTap: () {
                                  setState(() {
                                    if (type.contains(data.code)) {
                                      type.remove(data.code);
                                    } else {
                                      type.add(data.code!);
                                    }
                                  });
                                },
                                child: Row(
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          data.name ?? "",
                                          style: const TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w800,
                                              color: Color(0xFF061B1F)),
                                        ),
                                        Text(
                                          data.description ?? "",
                                          style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFF646E70)),
                                        ),
                                      ],
                                    ),
                                    const Spacer(),
                                    Image.asset(
                                      type.contains(data.code)
                                          ? "images/check_circle.png"
                                          : "images/un_check_circle.png",
                                      width: 18,
                                      height: 18,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                        itemCount: snapshot.data?.data?.length ?? 0,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(left: 20.0, right: 20, bottom: 46),
        child: Container(
          height: 52,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF98ECEC),
                  Color(0xFF84E9FF),
                  Color(0xFF9BE1FF)
                ]),
          ),
          child: TextButton(
              onPressed: () async {
                if (type.isEmpty) {
                  EasyLoading.showToast("至少选择一项");
                  return;
                }
                EasyLoading.show();
                await Api.setLearningPurpose({
                  "purpose_code": type,
                }).then((value) {
                  EasyLoading.dismiss();
                  Navigator.of(context).pushNamed("/setup_setting_two");
                });
              },
              style: const ButtonStyle(
                textStyle: MaterialStatePropertyAll(
                    TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
              ),
              child: const Center(
                child: Text("下一步",
                    style: TextStyle(
                        color: Color(0xFF061B1F),
                        fontSize: 18,
                        fontWeight: FontWeight.w800)),
              )),
        ),
      ),
    );
  }
}
