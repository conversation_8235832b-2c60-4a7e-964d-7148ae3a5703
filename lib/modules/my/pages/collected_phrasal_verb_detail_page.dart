import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/collection_phrasal_verb_detail/collection_phrasal_verb_detail.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_phrasal_verb/phrasal_verb_list_model.dart';
import 'package:flutter_app_kouyu/common/provider/voice_model.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';
import 'package:flutter_app_kouyu/modules/my/bloc/cubit/collected_phrasal_verb_detail_provider.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class CollectedPhrasalVerbDetailPage extends StatelessWidget {
  final PhrasalVerbList phrasalVerb;

  const CollectedPhrasalVerbDetailPage({Key? key, required this.phrasalVerb})
      : super(key: key);

  static Route<void> route(PhrasalVerbList phrasalVerb) {
    return MaterialPageRoute<void>(
        builder: (_) => CollectedPhrasalVerbDetailPage(phrasalVerb: phrasalVerb));
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<CollectedPhrasalVerbDetailProvider>(
      create: (_) => CollectedPhrasalVerbDetailProvider(phrasalVerbId: phrasalVerb.id ?? 0)
        ..refreshData(),
      child: Consumer<CollectedPhrasalVerbDetailProvider>(
        builder: (_, model, __) {
          final detailData = model.phrasalVerbDetail?.data;
          
          return Scaffold(
            appBar: AppBar(
              leadingWidth: 200.w,
              elevation: 0,
              backgroundColor: Colors.transparent,
              leading: CustomAppbar.leftWidget(context, text: "短语动词详情"),
            ),
            body: SingleChildScrollView(
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 40.w),
                padding: EdgeInsets.all(32.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(24.w),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                detailData?.phrasalVerb ?? phrasalVerb.phrasalVerb ?? "_",
                                style: TextStyle(fontSize: 72.w, fontWeight: FontWeight.w700, color: color_1),
                              ),
                              SizedBox(height: 8.w),
                              Text(
                                phrasalVerb.cefrLevel ?? "",
                                style: TextStyle(
                                  fontSize: 38.w,
                                  color: const Color(0xFF41C0FF),
                                ),
                              ),
                            ],
                          ),
                        ),
                        // 录音功能暂缓实现，依赖VoiceModel的实现
                      ],
                    ),
                    SizedBox(height: 24.w),
                    _buildPronunciationSection(context, detailData),
                    SizedBox(height: 24.w),
                    _buildDetailSection("释义", detailData?.commonDefinition ?? phrasalVerb.commonDefinition),
                    _buildDetailSection("英文例句", detailData?.englishExample ?? phrasalVerb.englishExample),
                    _buildDetailSection("中文例句", detailData?.chineseExample ?? phrasalVerb.chineseExample),
                    _buildDetailSection("记忆技巧", detailData?.cognitiveTip ?? phrasalVerb.cognitiveTip),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPronunciationSection(BuildContext context, dynamic detailData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "发音",
          style: TextStyle(
            fontSize: 28.sp,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        SizedBox(height: 16.w),
        Row(
          children: [
            _buildPronunciationButton("美式发音", detailData?.usPronUrl ?? phrasalVerb.usPronunciationUrl),
            SizedBox(width: 40.w),
            _buildPronunciationButton("英式发音", detailData?.ukPronUrl ?? phrasalVerb.ukPronunciationUrl),
          ],
        ),
        SizedBox(height: 24.w),
        Container(
          height: 1.w,
          color: ColorUtil.separated,
        ),
      ],
    );
  }

  Widget _buildPronunciationButton(String title, String? url) {
    return Row(
      children: [
        Text(
          title,
          style: style_2_24,
        ),
        SizedBox(width: 8.w),
        PlayButtonWidget(
          style: PlayButtonStyle.blue,
          audioUrl: url,
        ),
      ],
    );
  }

  Widget _buildDetailSection(String title, String? content) {
    if (content == null || content.isEmpty) {
      return SizedBox();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 28.sp,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        SizedBox(height: 8.w),
        Text(
          content,
          style: style_2_28,
        ),
        SizedBox(height: 24.w),
        Container(
          height: 1.w,
          color: ColorUtil.separated,
        ),
        SizedBox(height: 24.w),
      ],
    );
  }
} 