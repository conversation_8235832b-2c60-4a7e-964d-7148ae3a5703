import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/http/models/core_vocabulary_model/core_vocabulary_model.dart';
import 'package:flutter_app_kouyu/common/http/models/core_vocabulary_model/datum.dart'
    as coreData;

import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/modules/my/bloc/cubit/highFrequency_vocabulary_provider.dart';
import 'package:flutter_app_kouyu/modules/my/pages/collected_vocabulary_detail_page.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gif_view/gif_view.dart';
import 'package:provider/provider.dart';

class HighFrequencyVocabularyPage extends StatelessWidget {
  final String conversationId;
  const HighFrequencyVocabularyPage({super.key, required this.conversationId});
  static Route<void> route(String conversationId) {
    return MaterialPageRoute<void>(
        builder: (_) => HighFrequencyVocabularyPage(
              conversationId: conversationId,
            ));
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<HighFrequencyVocabularyProvider>(
      create: (_) => HighFrequencyVocabularyProvider(conversationId),
      child: Consumer<HighFrequencyVocabularyProvider>(
        builder: (context, model, child) {
          return Scaffold(
            backgroundColor: bgColor,
            appBar: AppBar(
              leadingWidth: 100,
              elevation: 0,
              leading: CustomAppbar.leftWidget(context, text: ""),
              title: Text(
                "高频词汇",
                style: tsBar,
              ),
            ),
            body: ListView(
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    left: 40.w,
                    right: 40.w,
                    bottom: 24.w,
                  ),
                  child: Text(
                    "共${model.vocabulary?.data?.length ?? 0}个",
                    style: style_3_24,
                  ),
                ),
                _wordRecordsList(context)
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _wordRecordsList(BuildContext context) {
    HighFrequencyVocabularyProvider model =
        context.read<HighFrequencyVocabularyProvider>();
    List<coreData.Datum> list = model.vocabulary?.data ?? [];
    return Container(
      margin: EdgeInsets.only(
        left: 40.w,
        right: 40.w,
        bottom: 24.w,
      ),
      padding: EdgeInsets.only(
        left: 32.w,
        right: 32.w,
        top: 24.w,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24.w),
      ),
      child: ListView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          coreData.Datum item = list[index];
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {},
            child: Column(
              children: [
                _wordItemWidget(context, item),
                _lineWidget(),
              ],
            ),
          );
        },
        itemCount: list.length,
      ),
    );
  }

  Container _lineWidget() {
    return Container(
      height: 1.w,
      width: double.infinity,
      color: ColorUtil.separated,
    );
  }

  Widget _wordItemWidget(BuildContext context, coreData.Datum vocabulary) {
    HighFrequencyVocabularyProvider model =
        context.read<HighFrequencyVocabularyProvider>();

    return GestureDetector(
      onTap: () {
        Navigator.of(context)
            .push(CollectedVocabularyDetailPage.route(vocabulary.vocabulary!));
      },
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.only(top: 24.w, bottom: 24.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            PlayButtonWidget(
                audioUrl: vocabulary.defaultPronunciationUrl!,
                style: PlayButtonStyle.blue),
            SizedBox(width: 20.w),
            Expanded(
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(
                        vocabulary.vocabulary ?? "_",
                        style: style_1_28,
                      ),
                      const Spacer(),
                      GestureDetector(
                          onTap: () {
                            vocabulary.collected == true
                                ? model.cancelCollection(vocabulary)
                                : model.collection(vocabulary);
                          },
                          child: Image.asset(
                            vocabulary.collected == true
                                ? "images/chat_collected.png"
                                : "images/chat_collect_blue.png",
                            width: 48.w,
                            height: 48.w,
                          )),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          vocabulary.definition ?? "_",
                          style: style_2_24,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
