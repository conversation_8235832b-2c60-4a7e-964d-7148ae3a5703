import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/purpuse_model/data.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/modules/my/pages/setup_settings_person.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SetupSettingPurpose extends StatefulWidget {
  const SetupSettingPurpose({super.key});
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const SetupSettingPurpose());
  }

  @override
  State<SetupSettingPurpose> createState() => _SetupSettingPurposeState();
}

class _SetupSettingPurposeState extends State<SetupSettingPurpose> {
  List<String> type = [];
  List<SettingStage> stageList = [
    SettingStage("1", "小学"),
    SettingStage("2", "初中"),
    SettingStage("3", "高中"),
    SettingStage("4", "大学"),
    SettingStage("5", "工作"),
    SettingStage("6", "其他"),
  ];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leadingWidth: 300,
        backgroundColor: const Color(0xFFFFFFFF),
        scrolledUnderElevation: 0,
        // leading: CustomAppbar.leftCloseWidget(
        //   context,
        //   ontap: () {
        //     Navigator.of(context).pushNamedAndRemoveUntil("/home", (route) => false);
        //   },
        // ),
        leading: CustomAppbar.leftWidgetWithNoIcon(context, text: ""),
        actions: [
          //  GestureDetector(
          //   onTap: () {
          //     Navigator.of(context)
          //         .pushNamedAndRemoveUntil("/setup_setting_four", (route) => false);
          //   },
          //   child:  Text(
          //     "跳过",
          //     style: style_4_28_400,
          //   ),
          // ),
          //  SizedBox(
          //   width: 40.w,
          // )
        ],
      ),
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(48.w, 40.w, 48.w, 100.w),
            child: Column(
              children: [
                Row(
                  children: [],
                ),
                Expanded(
                  child: ListView(
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "你的学习目的",
                            style: style_1_40_800,
                          ),
                          const SizedBox(
                            width: 8,
                          )
                        ],
                      ),
                      const SizedBox(
                        height: 4,
                      ),
                      Text(
                        "老师会根据你的学习目的来匹配你的学习内容",
                        style: style_4_24_400,
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      FutureBuilder(
                        future: Api.purpose(),
                        builder: (context, snapshot) {
                          return ListView.separated(
                            separatorBuilder: (context, index) => SizedBox(
                              height: 20.w,
                            ),
                            itemBuilder: (context, index) {
                              Data apiData = snapshot.data!.data![index];
                              return Align(
                                alignment: Alignment.center,
                                child: ClipRRect(
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(12)),
                                  child: Container(
                                    color: Colors.white,
                                    // padding: const EdgeInsets.all(16),
                                    child: GestureDetector(
                                      behavior: HitTestBehavior.opaque,
                                      onTap: () {
                                        setState(() {
                                          if (type.contains(apiData.code)) {
                                            type.remove(apiData.code);
                                          } else {
                                            type.add(apiData.code!);
                                          }
                                        });
                                      },
                                      child: Row(
                                        children: [
                                          const Spacer(),
                                          Container(
                                            padding: EdgeInsets.all(32.w),
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                border: Border.all(
                                                    color: Color(0xFFCEDDE0))),
                                            height: 112.w,
                                            width: 654.w,
                                            child: Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(apiData.name ?? "",
                                                    style: style_1_32_600),
                                                const Spacer(),
                                                Image.asset(
                                                  type.contains(apiData.code)
                                                      ? "images/check_circle_blue.png"
                                                      : "images/un_check_circle.png",
                                                  width: 18,
                                                  height: 18,
                                                )
                                              ],
                                            ),
                                          ),
                                          const Spacer(),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                            itemCount: snapshot.data?.data?.length ?? 0,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                          );
                        },
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 固定在底部的按钮
          Positioned(
            left: 20,
            right: 20,
            bottom: 46,
            child: Container(
              height: 52,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: const Color(0xFF2693FF),
              ),
              child: TextButton(
                  onPressed: () async {
                    if (type.isEmpty) {
                      EasyLoading.showToast("至少选择一项");
                      return;
                    }
                    EasyLoading.show();
                    await Api.setLearningPurpose({
                      "purpose_code": type,
                    }).then((value) {
                      EasyLoading.dismiss();
                      Navigator.of(context).pushNamed("/setup_setting_four");
                    });
                    EasyLoading.dismiss();
                  },
                  style: const ButtonStyle(
                    textStyle: MaterialStatePropertyAll(
                        TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
                  ),
                  child: const Center(
                    child: Text("下一步",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w800)),
                  )),
            ),
          ),
        ],
      ),
    );
  }
}

class GradientText extends StatelessWidget {
  const GradientText(
    this.text, {
    super.key,
    required this.gradient,
    this.style,
  });

  final String text;
  final TextStyle? style;
  final Gradient gradient;

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (bounds) => gradient.createShader(
        Rect.fromLTWH(0, 0, bounds.width, bounds.height),
      ),
      // 让文字的颜色变为透明，这样ShaderMask的着色器才能生效
      blendMode: BlendMode.srcIn,
      child: Text(
        text,
        style: style?.copyWith(color: Colors.white),
      ),
    );
  }
}

class SettingStage {
  final String code;
  final String name;

  SettingStage(this.code, this.name);
}
