import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/english_level_model/datum.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SetupSettingLevel extends StatefulWidget {
  const SetupSettingLevel({super.key});
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const SetupSettingLevel());
  }

  @override
  State<SetupSettingLevel> createState() => _SetupSettingLevelState();
}

class _SetupSettingLevelState extends State<SetupSettingLevel> {
  String? type;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leadingWidth: 300,
        backgroundColor: const Color(0xFFFFFFFF),
        scrolledUnderElevation: 0,
        // leading: CustomAppbar.leftCloseWidget(
        //   context,
        //   ontap: () {
        //     Navigator.of(context).pushNamedAndRemoveUntil("/home", (route) => false);
        //   },
        // ),
        leading: CustomAppbar.leftWidgetWithNoIcon(context, text: ""),
        actions: [
          // GestureDetector(
          //   onTap: () {
          //     Navigator.of(context)
          //         .pushNamedAndRemoveUntil("/setup_setting_three", (route) => false);
          //   },
          //   child:  Text(
          //     "跳过",
          //     style: style_4_28_400,
          //   ),
          // ),
          //  SizedBox(
          //   width: 40.w,
          // )
        ],
      ),
      backgroundColor: const Color(0xFFFFFFFF),
      body: Stack(
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(48.w, 40.w, 48.w, 100.w),
            child: Column(
              children: [
                Row(
                  children: [],
                ),
                Expanded(
                  child: ListView(
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "你当前的英语水平",
                            style: style_1_40_800,
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 4,
                      ),
                      const Text(
                        "老师会根据你的英语水平选择合适的词汇和你对话",
                        style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF646E70)),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      FutureBuilder(
                        future: Api.getAllEnglishLevels(),
                        builder: (context, snapshot) {
                          return ListView.separated(
                            separatorBuilder: (context, index) => SizedBox(
                              height: 20.w,
                            ),
                            itemBuilder: (context, index) {
                              Datum data = snapshot.data!.data![index];
                              return GestureDetector(
                                // behavior: HitTestBehavior.opaque,
                                onTap: () {
                                  setState(() {
                                    type = data.levelEn;
                                  });
                                },
                                child: Container(
                                  padding: EdgeInsets.all(32.w),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                        color: const Color(0xFFCEDDE0)),
                                  ),
                                  child: Row(
                                    children: [
                                      Image.network(
                                        data.icon ?? "",
                                        width: 40,
                                        height: 40,
                                        fit: BoxFit.fill,
                                      ),
                                      const SizedBox(
                                        width: 8,
                                      ),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              data.levelCn ?? "",
                                              style: style_1_32_600,
                                              // style:     TextStyle(color: #272733, fontSize: 32.sp, fontWeight: FontWeight.w500);,
                                            ),
                                            Text(data.description ?? "",
                                                style: style_4_24_400),
                                          ],
                                        ),
                                      ),
                                      Image.asset(
                                        type == data.levelEn
                                            ? "images/check_circle_blue.png"
                                            : "images/un_check_circle.png",
                                        width: 18,
                                        height: 18,
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                            itemCount: snapshot.data?.data?.length ?? 0,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 固定在底部的按钮
          Positioned(
            left: 20,
            right: 20,
            bottom: 46,
            child: Container(
              height: 52,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: const Color(0xFF2693FF),
              ),
              child: TextButton(
                  onPressed: () async {
                    if (type == null) {
                      EasyLoading.showToast("请选择");
                      return;
                    }
                    EasyLoading.show();
                    await Api.setEnglishLevel({
                      "english_level": type,
                    }).then((value) {
                      EasyLoading.dismiss();
                      Navigator.of(context).pushNamed("/setup_setting_three");
                    });
                  },
                  style: const ButtonStyle(
                    textStyle: MaterialStatePropertyAll(
                        TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
                  ),
                  child: const Center(
                    child: Text("下一步",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w800)),
                  )),
            ),
          ),
        ],
      ),
    );
  }
}
