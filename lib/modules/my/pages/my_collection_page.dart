import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_sentence/sentence.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_sentence/sentence_list.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_vocabulary/vocabulary_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_vocabulary/vocabulary_model.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_phrasal_verb/phrasal_verb_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_phrasal_verb/phrasal_verb_model.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_collocation/collocation_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_collocation/collocation_model.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_report_model.dart';
import 'package:flutter_app_kouyu/modules/chat/view/chat_report_page.dart';
import 'package:flutter_app_kouyu/modules/my/bloc/cubit/my_collection_provider.dart';
import 'package:flutter_app_kouyu/modules/my/pages/collected_sentence_detail_page.dart';
import 'package:flutter_app_kouyu/modules/my/pages/collected_vocabulary_detail_page.dart';
import 'package:flutter_app_kouyu/modules/my/pages/collected_phrasal_verb_detail_page.dart';
import 'package:flutter_app_kouyu/modules/my/pages/collected_collocation_detail_page.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:gif_view/gif_view.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class MyCollectionPage extends StatelessWidget {
  const MyCollectionPage({super.key});
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const MyCollectionPage());
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<MyCollectionProvider>(
      create: (_) => MyCollectionProvider()..refreshData(),
      child: Consumer<MyCollectionProvider>(
        builder: (context, model, child) {
          int wordNum = model.vocabulary?.data?.count ?? 0;
          int senceNum = model.sentence?.data?.count ?? 0;
          int phrasalVerbNum = model.phrasalVerb?.data?.count ?? 0;
          return Scaffold(
            appBar: AppBar(
              leadingWidth: 200,
              elevation: 0,
              backgroundColor: Colors.transparent,
              leading: CustomAppbar.leftWidget(context, text: "我的收藏"),
              actions: [
                Text(
                  model.type == CollectionType.word
                      ? "共$wordNum个单词"
                      : model.type == CollectionType.sentence
                          ? "共$senceNum个句子"
                          : model.type == CollectionType.phrasal_verb
                              ? "共$phrasalVerbNum个短语动词"
                              : "共${model.collocation?.data?.count ?? 0}个固定搭配",
                  style: style_1_24,
                ),
                SizedBox(
                  width: 40.w,
                )
              ],
            ),
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 24.w, left: 40.w, right: 40.w),
                  child: Row(
                    children: [
                      _typeWidget(model, CollectionType.word),
                      SizedBox(
                        width: 40.w,
                      ),
                      _typeWidget(model, CollectionType.phrasal_verb),
                      SizedBox(
                        width: 40.w,
                      ),
                      _typeWidget(model, CollectionType.collocation),
                      SizedBox(
                        width: 40.w,
                      ),
                      _typeWidget(model, CollectionType.sentence),
                      const Spacer(),
                      GestureDetector(
                        onTap: () {
                          if (model.sortEnum == SortEnum.vocabulary) {
                            model.sortEnum = SortEnum.data;
                          } else {
                            model.sortEnum = SortEnum.vocabulary;
                          }
                        },
                        child: Container(
                          width: 175.w,
                          height: 56.w,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16.w),
                              color: Colors.white),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                model.sortEnum.title(),
                                style: style_1_24,
                              ),
                              Image.asset(
                                "images/sort_arrow.png",
                                width: 24.w,
                                height: 24.w,
                              )
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                SizedBox(
                  height: 24.w,
                ),
                Expanded(child: _recordWidget(context))
              ],
            ),
          );
        },
      ),
    );
  }

  GestureDetector _typeWidget(MyCollectionProvider model, CollectionType type) {
    return GestureDetector(
      onTap: () {
        model.type = type;
      },
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Text(
            type.name,
            style: model.type == type ? style_1_40 : style_2_28,
          ),
          if (model.type == type)
            Image.asset(
              "images/text_bg.png",
              width: 63.w,
              height: 16.w,
            )
        ],
      ),
    );
  }

  Widget _recordWidget(BuildContext context) {
    MyCollectionProvider model = context.read<MyCollectionProvider>();
    List<VocabularyCategory>? wordList =
        model.vocabulary?.data?.vocabularys ?? [];
    List<Sentence>? sentenceList = model.sentence?.data?.sentences ?? [];
    List<PhrasalVerbCategory>? phrasalVerbList = 
        model.phrasalVerb?.data?.phrasalVerbList ?? [];
    List<CollocationCategory>? collocationList =
        model.collocation?.data?.collocationList ?? [];

    return SmartRefresher(
      controller: model.refreshController,
      onRefresh: () {
        model.refreshData();
      },
      onLoading: () {
        model.loadMore();
      },
      enablePullUp: true,
      child: CustomScrollView(
        slivers: (model.type == CollectionType.word)
            ? wordList.map((e) => _wordRecordsList(context, e)).toList()
            : (model.type == CollectionType.sentence)
                ? sentenceList
                    .map((e) => _sentenceRecordsList(context, e))
                    .toList()
                : (model.type == CollectionType.phrasal_verb)
                    ? phrasalVerbList
                        .map((e) => _phrasalVerbRecordsList(context, e))
                        .toList()
                    : collocationList
                        .map((e) => _collocationRecordsList(context, e))
                        .toList(),
      ),
    );
  }

  Widget _wordRecordsList(BuildContext context, VocabularyCategory list) {
    MyCollectionProvider model = context.read<MyCollectionProvider>();
    return SliverToBoxAdapter(
      child: Slidable(
        child: Container(
          margin: EdgeInsets.only(
            left: 40.w,
            right: 40.w,
            bottom: 24.w,
          ),
          padding: EdgeInsets.only(
            left: 32.w,
            right: 32.w,
            top: 24.w,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24.w),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                list.groupBy ?? "_",
                style: style_2_24,
              ),
              SizedBox(
                height: 24.w,
              ),
              ListView.builder(
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  VocabularyList item = list.vocabularyList![index];
                  return Slidable(
                    key: ValueKey(item.vocabulary),
                    endActionPane: ActionPane(
                      extentRatio: 0.2,
                      motion: const ScrollMotion(),
                      children: [
                        GestureDetector(
                          child: Container(
                            padding: EdgeInsets.only(left: 16.w, right: 16.w),
                            margin: EdgeInsets.only(top: 24.w, bottom: 24.w),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16.w),
                                color: const Color(0xFFFA5151)),
                            child: Center(
                                child: Text(
                              "删除",
                              style: TextStyle(
                                  color: Colors.white, fontSize: 24.sp),
                            )),
                          ),
                          onTap: () {
                            list.vocabularyList!.remove(item);
                            if (list.vocabularyList!.isEmpty) {
                              model.vocabulary!.data!.vocabularys!.remove(list);
                            }
                            model.vocabulary!.data!.count =
                                model.vocabulary!.data!.count! - 1;
                            model.cancelCollectVocabulary(item);
                          },
                        )
                      ],
                    ),
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        Navigator.of(context).push(
                            CollectedVocabularyDetailPage.route(
                                item.vocabulary!));
                      },
                      child: Column(
                        children: [
                          _lineWidget(),
                          _wordItemWidget(item),
                        ],
                      ),
                    ),
                  );
                },
                itemCount: (list.vocabularyList ?? []).length,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _sentenceRecordsList(BuildContext context, Sentence list) {
    MyCollectionProvider model = context.read<MyCollectionProvider>();
    return SliverToBoxAdapter(
      child: Container(
        margin: EdgeInsets.only(
          left: 40.w,
          right: 40.w,
          bottom: 24.w,
        ),
        padding: EdgeInsets.only(
          left: 32.w,
          right: 32.w,
          top: 24.w,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.w),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              list.groupBy ?? "_",
              style: style_2_24,
            ),
            SizedBox(
              height: 24.w,
            ),
            ListView.builder(
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                SentenceList item = list.sentenceList![index];
                return Slidable(
                  key: ValueKey(item.sentence),
                  endActionPane: ActionPane(
                    extentRatio: 0.2,
                    motion: const ScrollMotion(),
                    children: [
                      GestureDetector(
                        child: Container(
                          padding: EdgeInsets.only(left: 16.w, right: 16.w),
                          margin: EdgeInsets.only(top: 24.w, bottom: 24.w),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16.w),
                              color: const Color(0xFFFA5151)),
                          child: Center(
                              child: Text(
                            "删除",
                            style:
                                TextStyle(color: Colors.white, fontSize: 24.sp),
                          )),
                        ),
                        onTap: () {
                          list.sentenceList!.remove(item);
                          if (list.sentenceList!.isEmpty) {
                            model.sentence!.data!.sentences!.remove(list);
                          }
                          model.sentence!.data!.count =
                              model.sentence!.data!.count! - 1;

                          model.cancelCollectionSentence(item);
                        },
                      )
                    ],
                  ),
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      Navigator.of(context).push(
                          CollectedSentenceDetailPage.route(
                              item.sentence!, "${item.id}"));
                    },
                    child: Column(
                      children: [
                        _lineWidget(),
                        _sentenceItemWidget(context, item),
                      ],
                    ),
                  ),
                );
              },
              itemCount: (list.sentenceList ?? []).length,
            ),
          ],
        ),
      ),
    );
  }

  Widget _phrasalVerbRecordsList(BuildContext context, PhrasalVerbCategory list) {
    MyCollectionProvider model = context.read<MyCollectionProvider>();
    return SliverToBoxAdapter(
      child: Container(
        margin: EdgeInsets.only(
          left: 40.w,
          right: 40.w,
          bottom: 24.w,
        ),
        padding: EdgeInsets.only(
          left: 32.w,
          right: 32.w,
          top: 24.w,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.w),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              list.groupBy ?? "_",
              style: style_2_24,
            ),
            SizedBox(
              height: 24.w,
            ),
            ListView.builder(
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                PhrasalVerbList item = list.phrasalVerbList![index];
                return Slidable(
                  key: ValueKey(item.phrasalVerb),
                  endActionPane: ActionPane(
                    extentRatio: 0.2,
                    motion: const ScrollMotion(),
                    children: [
                      GestureDetector(
                        child: Container(
                          padding: EdgeInsets.only(left: 16.w, right: 16.w),
                          margin: EdgeInsets.only(top: 24.w, bottom: 24.w),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16.w),
                              color: const Color(0xFFFA5151)),
                          child: Center(
                              child: Text(
                            "删除",
                            style:
                                TextStyle(color: Colors.white, fontSize: 24.sp),
                          )),
                        ),
                        onTap: () {
                          list.phrasalVerbList!.remove(item);
                          if (list.phrasalVerbList!.isEmpty) {
                            model.phrasalVerb!.data!.phrasalVerbList!.remove(list);
                          }
                          model.phrasalVerb!.data!.count =
                              model.phrasalVerb!.data!.count! - 1;

                          model.cancelCollectPhrasalVerb(item);
                        },
                      )
                    ],
                  ),
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      Navigator.of(context).push(
                          CollectedPhrasalVerbDetailPage.route(item));
                    },
                    child: Column(
                      children: [
                        _lineWidget(),
                        _phrasalVerbItemWidget(item),
                      ],
                    ),
                  ),
                );
              },
              itemCount: (list.phrasalVerbList ?? []).length,
            ),
          ],
        ),
      ),
    );
  }

  Widget _collocationRecordsList(BuildContext context, CollocationCategory list) {
    MyCollectionProvider model = context.read<MyCollectionProvider>();
    return SliverToBoxAdapter(
      child: Slidable(
        child: Container(
          margin: EdgeInsets.only(
            left: 40.w,
            right: 40.w,
            bottom: 24.w,
          ),
          padding: EdgeInsets.only(
            left: 32.w,
            right: 32.w,
            top: 24.w,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24.w),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                list.groupBy ?? "_",
                style: style_2_24,
              ),
              SizedBox(
                height: 24.w,
              ),
              ListView.builder(
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  CollocationList item = list.collocationList![index];
                  return Slidable(
                    key: ValueKey(item.collocation),
                    endActionPane: ActionPane(
                      extentRatio: 0.2,
                      motion: const ScrollMotion(),
                      children: [
                        GestureDetector(
                          child: Container(
                            padding: EdgeInsets.only(left: 16.w, right: 16.w),
                            margin: EdgeInsets.only(top: 24.w, bottom: 24.w),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16.w),
                                color: const Color(0xFFFA5151)),
                            child: Center(
                                child: Text(
                              "删除",
                              style: TextStyle(
                                  color: Colors.white, fontSize: 24.sp),
                            )),
                          ),
                          onTap: () {
                            list.collocationList!.remove(item);
                            if (list.collocationList!.isEmpty) {
                              model.collocation!.data!.collocationList!.remove(list);
                            }
                            model.collocation!.data!.count =
                                model.collocation!.data!.count! - 1;
                            model.cancelCollectCollocation(item);
                          },
                        )
                      ],
                    ),
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        Navigator.of(context)
                            .push(CollectedCollocationDetailPage.route(item));
                      },
                      child: Container(
                        padding: EdgeInsets.only(
                          bottom: 24.w,
                        ),
                        decoration: BoxDecoration(
                            border: Border(
                                bottom: BorderSide(
                                    color: ColorUtil.separated, width: 1.w))),
                        child: Column(
                          children: [
                            SizedBox(
                              height: 24.w,
                            ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Text(
                                    item.collocation ?? "_",
                                    style: style_1_28,
                                  ),
                                ),
                                SizedBox(
                                  width: 16.w,
                                ),
                                // Text(
                                //   item.cefrLevel ?? "_",
                                //   style: TextStyle(
                                //       color: const Color(0xFF41C0FF),
                                //       fontSize: 20.sp),
                                // ),
                                PlayButtonWidget(
                                  style: PlayButtonStyle.blue,
                                  audioUrl: item.defaultPronunciationUrl,
                                )
                              ],
                            ),
                            SizedBox(
                              height: 4.w,
                            ),
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                item.commonDefinition ?? "_",
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                                style: style_2_24,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
                itemCount: list.collocationList?.length ?? 0,
              )
            ],
          ),
        ),
      ),
    );
  }

  Container _lineWidget() {
    return Container(
      height: 1.w,
      width: double.infinity,
      color: ColorUtil.separated,
    );
  }

  Container _wordItemWidget(VocabularyList vocabulary) {
    return Container(
      color: Colors.white,
      padding:
          EdgeInsets.only(left: 32.w, right: 32.w, top: 24.w, bottom: 24.w),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                vocabulary.vocabulary ?? "_",
                style: style_1_28,
              ),
              const Spacer(),
              PlayButtonWidget(
                style: PlayButtonStyle.blue,
                audioUrl: vocabulary.defaultPronunciationUrl,
              ),
            ],
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  vocabulary.definition ?? "_",
                  style: style_2_24,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Container _phrasalVerbItemWidget(PhrasalVerbList phrasalVerb) {
    return Container(
      color: Colors.white,
      padding:
          EdgeInsets.only(left: 32.w, right: 32.w, top: 24.w, bottom: 24.w),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                phrasalVerb.phrasalVerb ?? "_",
                style: style_1_28,
              ),
              const Spacer(),
              PlayButtonWidget(
                style: PlayButtonStyle.blue,
                audioUrl: phrasalVerb.defaultPronunciationUrl,
              ),
            ],
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  phrasalVerb.commonDefinition ?? "_",
                  style: style_2_24,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Container _sentenceItemWidget(
      BuildContext context, SentenceList sentenceList) {
    return Container(
      color: Colors.white,
      padding:
          EdgeInsets.only(left: 32.w, right: 32.w, top: 24.w, bottom: 24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  sentenceList.sentence ?? "_",
                  style: style_1_28,
                ),
              ),
              PlayButtonWidget(
                style: PlayButtonStyle.blue,
                audioUrl: sentenceList.defaultPronunciationUrl,
              ),
            ],
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  sentenceList.chineseMeaning ?? "",
                  style: style_2_24,
                ),
              ),
            ],
          ),
          Visibility(
            visible: sentenceList.souceChinese() != null,
            child: GestureDetector(
              onTap: () {
                if (SentenceSourceEnumExtension.fromName(sentenceList.source) ==
                    SentenceSourceEnum.conversationReport) {
                  //对话报告
                  Navigator.push(
                      context,
                      MaterialPageRoute<void>(
                          builder: (_) => ChatReportPage(
                                conversationId: sentenceList.sourceId!,
                                type: ReportDetailType.view,
                              )));
                }
              },
              child: Container(
                padding: EdgeInsets.fromLTRB(12.w, 8.w, 12.w, 8.w),
                margin: EdgeInsets.only(top: 12.w),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.w),
                    color: Colors.white,
                    border:
                        Border.all(width: 1.w, color: const Color(0x1941C0FF))),
                child: Text(
                  sentenceList.souceChinese() ?? "",
                  style: style_bue_20,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}

enum CollectionType { sentence, word, phrasal_verb, collocation }

extension CollectionTypeExtension on CollectionType {
  String get name {
    switch (this) {
      case CollectionType.sentence:
        return "句子";
      case CollectionType.word:
        return "单词";
      case CollectionType.phrasal_verb:
        return "短语动词";
      case CollectionType.collocation:
        return "搭配";
    }
  }

  int get requestType {
    switch (this) {
      case CollectionType.sentence:
        return 2;
      case CollectionType.word:
        return 1;
      case CollectionType.phrasal_verb:
        return 3;
      case CollectionType.collocation:
        return 4;
    }
  }
}
