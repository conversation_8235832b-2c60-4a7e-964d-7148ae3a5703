import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/http/models/en_to_ch_model.dart';

import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/modules/chat/view/free_talk_page.dart';
import 'package:flutter_app_kouyu/modules/my/bloc/cubit/sentence_alert_provider.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class SentenceAlert extends StatelessWidget {
  final String sentence;
  final bool showTranslate;
  const SentenceAlert(
      {super.key, required this.sentence, required this.showTranslate});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<SentenceAlertProvider>(
      create: (_) => SentenceAlertProvider(
        sentence: sentence,
        showTranslate: showTranslate,
      )..refreshData(),
      child: Consumer<SentenceAlertProvider>(
        builder: (context, model, child) {
          return Container(
            color: Colors.white,
            width: double.infinity,
            child: _vocabularyCardItem(model.enToCnModel, context),
          );
        },
      ),
    );
  }

  Widget _vocabularyCardItem(EnToCnModel? detail, BuildContext context) {
    SentenceAlertProvider model = context.read<SentenceAlertProvider>();

    return Padding(
      padding:
          EdgeInsets.only(left: 40.w, right: 40.w, top: 40.w, bottom: 100.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  sentence,
                  style: TextStyle(
                      fontSize: 36.sp,
                      fontWeight: FontWeight.w600,
                      color: color_1),
                ),
              ),
              GestureDetector(
                onTap: () {
                  model.collection();
                },
                child: Container(
                  width: 104.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16.w),
                      border: Border.all(width: 1.w, color: separated)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        model.isCollect == true
                            ? "images/start_yellow.png"
                            : "images/start_blue.png",
                        width: 24.w,
                        height: 24.w,
                      ),
                      SizedBox(
                        width: 8.w,
                      ),
                      Text(
                        "收藏",
                        style: style_2_24,
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(
                width: 32.w,
              ),
              GestureDetector(
                child: const Icon(Icons.close),
                onTap: () {
                  Navigator.pop(context);
                },
              )
            ],
          ),
          SizedBox(
            height: 24.w,
          ),
          _voiceWidget(model.sentence),
          if (model.showTranslate) ..._translateWidgt(detail),
        ],
      ),
    );
  }

  List<Widget> _translateWidgt(EnToCnModel? detail) {
    return [
      SizedBox(
        height: 40.w,
      ),
      Align(
        alignment: Alignment.topLeft,
        child: Text(
          "释义",
          style: style_2_24,
        ),
      ),
      SizedBox(
        height: 16.w,
      ),
      Text(
        detail?.data ?? "_",
        style: TextStyle(
            fontSize: 28.sp, fontWeight: FontWeight.w600, color: color_1),
      ),
    ];
  }

  Container _voiceWidget(String content) {
    return Container(
      height: 56.w,
      decoration: BoxDecoration(
          color: btnColor, borderRadius: BorderRadius.circular(40.w)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 24.w,
          ),
          Text(
            "AI发音",
            style: style_2_24,
          ),
          PlayButtonWidget(
            style: PlayButtonStyle.blue,
            text: content,
          ),
        ],
      ),
    );
  }
}
