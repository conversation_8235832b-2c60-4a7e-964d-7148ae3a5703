import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// 个人资料修改页
class PersonInfoChangePage extends StatefulWidget {
  String? nickname;
  String? avatarUrl;
  int? gender;
  PersonInfoChangePage({super.key, this.nickname, this.avatarUrl, this.gender});
  static Route<void> route({String? nickname, String? avatarUrl, int? gender}) {
    return MaterialPageRoute<void>(builder: (_) => PersonInfoChangePage(nickname: nickname, avatarUrl: avatarUrl, gender: gender));
  }

  @override
  State<PersonInfoChangePage> createState() => _PersonInfoChangePageState();
}

class _PersonInfoChangePageState extends State<PersonInfoChangePage> {
  int commonNametotalPage = 10;
  int commonNamePageIndex = 1;

  int filterNamePageCurrent = 1;
  int filterNamepageSize = 50;

  bool _isLoadingMoreName = false;
  int sex = 2; //1 男 2女
  String? iconUrl;
  final TextEditingController _searchController = TextEditingController();
  List<String> filteredNames = [];
  List<String> maleNames = [];
  List<String> femaleNames = [];
  List<String> allNames = [];
  List<String> allCommonNames = [];
  List<String> randomNames = [];
  List<String> maleIcons = [];
  List<String> femaleIcons = [];
  List<String> currentIcons = [];

  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();
  final ScrollController _scrollController = ScrollController();
  final ScrollController _nameScrollController = ScrollController();
  bool _isScrolledUp = false;
  final FocusNode _searchFocusNode = FocusNode();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    _nameScrollController.addListener(() {
      print("开始滚动value:${_nameScrollController.offset}");
      if (_nameScrollController.position.pixels >=
          _nameScrollController.position.maxScrollExtent * 0.8) {
        // 当滚动到 80% 时就开始加载
        // 防止重复加载
        if (!_isLoadingMoreName) {
          _loadMoreNames();
        }
      }
    });

    _scrollController.addListener(() {
      if (!_isScrolledUp && _scrollController.offset >= 300) {
        setState(() => _isScrolledUp = true);
      } else if (_isScrolledUp && _scrollController.offset < 300) {
        setState(() => _isScrolledUp = false);
      }
    });

    _loadInitialData();

    _searchFocusNode.addListener(() {
      if (!_searchFocusNode.hasFocus && _isScrolledUp) {
        _scrollController.animateTo(
          0.0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
        setState(() => _isScrolledUp = false);
      }
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      KeyboardVisibilityController().onChange.listen((bool visible) {
        if (visible && _searchFocusNode.hasFocus) {
          // 键盘显示且输入框有焦点时，将输入框滚动到视图中
          Future.delayed(const Duration(milliseconds: 100), () {
            if (_scrollController.hasClients) {
              _scrollController.animateTo(
                350.0,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOut,
              );
            }
          });
        } else if (!visible && _isScrolledUp) {
          // 键盘隐藏时，恢复原始滚动位置
          if (_scrollController.hasClients) {
            _scrollController.animateTo(
              0.0,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
          setState(() => _isScrolledUp = false);
        }
      });
    });
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // int pageNum = 1;
      // int pageSize = 10000;
      // 获取男生数据
      // final maleResponse =
      //     await Api.getEnglishNamesPage(pageNum, pageSize, '', 1);
      // final maleList = maleResponse.data?.items ?? [];
      final maleList = ["Alice", "Charlie", "David", "Emma", "Fiona", "George", "Helen", "Ian", "Jane"];

      final maleIconList = await Api.getSystemProfilePictureList({"gender": 1});

      final femaleList = ["Allen", "Bob", "Cathy", "David", "Emma", "Fiona", "George", "Helen", "Ian", "Jane"];

        // 获取女生数据
      final femaleIconList =
          await Api.getSystemProfilePictureList({"gender": 2});

  

      // print("widget.fmaleNames:${widget.fmaleNames}");
      setState(() {
        sex = widget.gender ?? 2;
        iconUrl = widget.avatarUrl;
        _searchController.text = widget.nickname ?? "";

        // 设置名字列表
        maleNames = maleList;
        femaleNames = femaleList;
        // femaleNames = widget.fmaleNames;
        allNames = sex == 1 ? maleNames : femaleNames;

        // 设置头像列表
        maleIcons = maleIconList;
        femaleIcons = femaleIconList;
        // femaleIcons = widget.femaleIconList;
        currentIcons = sex == 1 ? maleIcons : femaleIcons;

        // 初始化随机名字
        _getRandomNames(commonNamePageIndex, sex);
        commonNamePageIndex = 2;
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> filterNames(String value) async {

    final response =
        await Api.getEnglishNamesPage(1, filterNamepageSize, value, sex);
    final filterList = response.data?.items ?? [];
    filteredNames = filterList.toList();

    if (value.isEmpty) {
      _hideSearchList();
    } else if (filteredNames.isNotEmpty) {
      _showSearchList();
    } else {
      _hideSearchList();
    }
  }

  Future<void> _loadMoreNames() async {
    if (_isLoadingMoreName) return;

    setState(() {
      _isLoadingMoreName = true;
    });

    try {
      filterNamePageCurrent++;
      // 加载下一页数据
      final response = await Api.getEnglishNamesPage(filterNamePageCurrent,
          filterNamepageSize, _searchController.text, sex);

      // 将新数据添加到现有列表中

      setState(() {
        // 将新数据添加到现有列表中
        filteredNames.addAll(response.data?.items ?? []);

        // 确保搜索列表是显示的
        if (filteredNames.isNotEmpty) {
          _showSearchList();
        }
      });

      // });
    } catch (e) {
      print('加载更多数据失败: $e');
    } finally {
      setState(() {
        _isLoadingMoreName = false;
      });
    }
    print("filteredNames:${filteredNames.length}");
  }

  void _showSearchList() {
    _overlayEntry?.remove();
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideSearchList() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Positioned(
        width: size.width - 100.w,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0.0, 130.w),
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(12.w),
                bottomRight: Radius.circular(12.w)),
            child: Container(
              constraints: BoxConstraints(
                maxHeight: 400.w,
                maxWidth: size.width - 100.w,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(12.w),
                    bottomRight: Radius.circular(12.w)),
              ),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                controller: _nameScrollController,
                shrinkWrap: true,
                itemCount: filteredNames.length,
                itemBuilder: (context, index) {
                  return ListTile(
                    dense: true,
                    title: Text(
                      filteredNames[index],
                      style: style_1_28_400,
                    ),
                    onTap: () {
                      _hideSearchList();
                      setState(() {
                        _searchController.text = filteredNames[index];
                        filteredNames = [];
                      });
                    },
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleInputTap() {
    if (!_isScrolledUp) {
      _scrollController.animateTo(
        300.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
      setState(() => _isScrolledUp = true);
    }
  }

  @override
  void dispose() {
    _hideSearchList();
    _searchController.dispose();
    _scrollController.dispose();
    _searchFocusNode.dispose();
    _nameScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        leadingWidth: 300,
        scrolledUnderElevation: 0,
        backgroundColor: const Color(0xFFFFFFFF),
        leading: CustomAppbar.leftWidget(context, text: "修改个人资料"),
      ),
      backgroundColor: const Color(0xFFFFFFFF),
      body: Stack(
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(48.w, 40.w, 48.w, 100.w),
            child: NotificationListener<ScrollNotification>(
              onNotification: (scrollNotification) {
                if (_isScrolledUp &&
                    scrollNotification is ScrollUpdateNotification) {
                  if (_scrollController.offset < 300) {
                    _scrollController.jumpTo(300);
                    return true;
                  }
                }
                return false;
              },
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          "性别",
                          style: style_1_32_600,
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        Expanded(child: sexItem(2)),
                        const SizedBox(width: 10),
                        Expanded(child: sexItem(1)),
                      ],
                    ),
                    SizedBox(height: 20.w),
                    Text(
                      '头像',
                      style: style_1_32_600,
                    ),
                    if (_isLoading) _buildLoading(200.w),
                    buildIconsList(),
                    const SizedBox(height: 12),
                    _buildNameInputSection(),
                    if (_isLoading) _buildLoading(200.w),
                    buildNamesList(),
                    _buildNameRefresh(),

                    const SizedBox(height: 100), // 为底部按钮留出空间
                  ],
                ),
              ),
            ),
          ),

          // 固定在底部的按钮
          Positioned(
            left: 20,
            right: 20,
            bottom: 46,
            child: Container(
              height: 52,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: const Color(0xFF2693FF),
              ),
              child: TextButton(
                  onPressed: _isLoading
                      ? null
                      : () async {
                          if (iconUrl == null) {
                            EasyLoading.showToast("请选择头像");
                            return;
                          }
                          if (_searchController.text == '') {
                            EasyLoading.showToast("请输入昵称");
                            return;
                          }

                          EasyLoading.show();

                          await Api.setUserGender({
                            "gender": sex,
                          });
                          await Api.setNickname({
                            "nickname": _searchController.text,
                          });
                          await Api.setProfilePicture({
                            "profile_picture": iconUrl,
                          });

                          EasyLoading.dismiss();
                          // 返回2层页面
                          Navigator.of(context).popUntil((route) => route.isFirst);
                        },
                  style: const ButtonStyle(
                    textStyle: MaterialStatePropertyAll(
                        TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
                  ),
                  child: const Center(
                    child: Text("保存",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w800)),
                  )),
            ),
          ),
        ],
      ),
    );
  }

  Widget iconWidget(String? url) {
    if (url == null) {
      return const AspectRatio(
        aspectRatio: 1,
      );
    }
    return GestureDetector(
      onTap: () {
        setState(() {
          print("url:${url}");
          iconUrl = url;
        });
      },
      child: AspectRatio(
        aspectRatio: 1,
        child: Stack(children: [
          Positioned.fill(
              child: Image.network(
            url,
            fit: BoxFit.fill,
          )),
          if (iconUrl == url)
            Positioned.fill(
                child: Image.asset(
              "images/chose_circle.png",
              fit: BoxFit.fill,
            )),
          if (iconUrl == url)
            Positioned(
              right: 0,
              bottom: 0,
              child: Image.asset(
                "images/check_circle.png",
                width: 18,
                height: 18,
              ),
            )
        ]),
      ),
    );
  }

  Widget nameWidget(String? name) {
    if (name == null) {
      return const SizedBox(
        height: 44,
      );
    }
    double baseWidth = 70.0; // 基础宽度
    double extraWidth = name.length > 5 ? (name.length - 5) * 8.0 : 0.0; // 每多一个字符增加8像素
    double containerWidth = baseWidth + extraWidth;
    return GestureDetector(
      onTap: () {
        setState(() {
          _searchController.text = name;
        });
      },
      child: Container(
        
          decoration: BoxDecoration(
              color: const Color(0xFFEDF6F7),
              border: Border.all(
                  width: 1,
                  color: _searchController.text == name
                      ? const Color(0xFF41C0FF)
                      : Colors.white),
              borderRadius: const BorderRadius.all(Radius.circular(12))),
          height: 44,
          width: containerWidth,
          child: Center(
              child: Text(
            name,
            style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Color(0xFF061B1F)),
          ))),
    );
  }

  Container sexItem(int type) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(12)),
          color: Colors.white,
          border: Border.all(
              color: sex == type
                  ? (sex == 2
                      ? const Color(0xFFEB2F96)
                      : const Color(0xFF2693FF))
                  : const Color(0xFFCEDDE0),
              width: 1)),
      padding: const EdgeInsets.all(16),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          setState(() {
            if (sex != type) {
              allNames = type == 1 ? maleNames : femaleNames;
              commonNamePageIndex = 1;
              _getRandomNames(commonNamePageIndex, type);
              _searchController.text = randomNames[0];
              iconUrl = null;
              sex = type;
              currentIcons = type == 1 ? maleIcons : femaleIcons;
            }
          });
        },
        child: Row(
          children: [
            SizedBox(
              width: 12.w,
            ),
            Text(
              type == 2 ? '女生' : '男生',
              style: style_1_28_500,
            ),
            SizedBox(
              width: 120.w,
            ),
            Image.asset(
              type == 1 ? "images/men.png" : "images/women.png",
              width: 24,
              height: 24,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNameInputSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '英文名',
          style: style_1_32_600,
        ),
        CompositedTransformTarget(
          link: _layerLink,
          child: Container(
            margin: EdgeInsets.fromLTRB(0.w, 24.w, 0.w, 0),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Color(0xFFCEDDE0), width: 1),
              borderRadius: BorderRadius.circular(24.w),
            ),
            child: TextField(
              focusNode: _searchFocusNode,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z]')),
              ],
              controller: _searchController,
              maxLines: 1,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: "请输入英文名",
                hintStyle: style_5_28_400,
                contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
              ),
              textAlign: TextAlign.left,
              style: style_1_32_400,
              // onTap: _handleInputTap,
              onChanged: (value) {
                print("开始输入value:${value}");
                filterNames(value);
            
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget buildNamesList() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: Column(
        children: [
          // 第一行
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                for (int i = 0; i < 4; i++)
                  // Expanded(
                    Padding(
                      padding: const EdgeInsets.all(6),
                      child: i < randomNames.length
                          ? nameWidget(randomNames[i])
                          : nameWidget(null),
                    ),
                  // ),
              ],
            ),
          ),
          SizedBox(height: 12.w),
          // 第二行
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                for (int i = 4; i < 8; i++)
                  // Expanded(
                     Padding(
                      padding: const EdgeInsets.all(6),
                      child: i < randomNames.length
                          ? nameWidget(randomNames[i])
                          : nameWidget(null),
                    ),
                  // ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _getRandomNames(int pageIndex, int gender) async {
    int pageSize = 8;
    // if (allNames.isEmpty) return;
    if (pageIndex >= commonNametotalPage) {
      setState(() {
        commonNamePageIndex = 0;
      });
    }
    final commonResponse =
        await Api.getCommonEnglishNamesPage(pageIndex, pageSize, gender);
    commonNametotalPage = commonResponse.data!.totalPages ?? 0;

    allCommonNames = commonResponse.data!.items ?? [];

    setState(() {
      List<String> tempList = List.from(allCommonNames);
      randomNames = [];

      int count = tempList.length < pageSize ? tempList.length : pageSize;

      for (int i = 0; i < count; i++) {
        int randomIndex = (DateTime.now().millisecondsSinceEpoch + i).hashCode %
            tempList.length;
        randomNames.add(tempList[randomIndex]);
        tempList.removeAt(randomIndex);
      }
    });
  }

  Widget buildIconsList() {
    //   if (currentIcons.isEmpty) {
    //     return  SizedBox(
    //       height: 200.w,
    //       width: 20,
    //     );
    //   }
    //  print("_isLoading===${_isLoading}");
    int size = currentIcons.length;
    return ListView.builder(
      itemBuilder: (context, index) {
        return Row(
          children: [
            for (int i = 0; i < 5; i++)
              if (size > index * 5 + i)
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(
                      left: 5,
                      right: 5.0,
                      top: 12,
                      bottom: 0,
                    ),
                    child: iconWidget(currentIcons[index * 5 + i]),
                    // child: iconWidget(widget.femaleIconList[index * 5 + i]),
                  ),
                )
              else
                Expanded(child: iconWidget(null))
          ],
        );
      },
      itemCount: ((size ~/ 5) + (size % 5 > 0 ? 1 : 0)),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
    );
  }

  Widget _buildLoading(double heightSize) {
    return Container(
      width: double.infinity,
      height: heightSize,
      alignment: Alignment.center,
      child: Image.asset(
        "images/samll_loading.gif",
        width: 50.w,
        height: 50.w,
      ),
    );
  }

  Widget _buildNameRefresh() {
    return Center(
      child: GestureDetector(
        onTap: () {
          _getRandomNames(commonNamePageIndex, sex);
          commonNamePageIndex = commonNamePageIndex + 1;
        },
        child: Container(
          // padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.w),
          decoration: BoxDecoration(
            color: Colors.white,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(width: 4.w),
              Text("换一批", style: style_4_28_400),
              SizedBox(width: 11.w),
              Image.asset(
                "images/refresh.png",
                width: 30.w,
                height: 30.w,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
