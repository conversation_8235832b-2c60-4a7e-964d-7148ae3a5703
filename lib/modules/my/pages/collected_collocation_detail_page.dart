import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/collection_collocation_detail/collection_collocation_detail.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_collocation/collocation_list_model.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/modules/my/bloc/cubit/collected_collocation_detail_provider.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/play_button_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class CollectedCollocationDetailPage extends StatelessWidget {
  final CollocationList collocation;

  const CollectedCollocationDetailPage({Key? key, required this.collocation})
      : super(key: key);

  static Route<void> route(CollocationList collocation) {
    return MaterialPageRoute<void>(
        builder: (_) => CollectedCollocationDetailPage(collocation: collocation));
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<CollectedCollocationDetailProvider>(
      create: (_) => CollectedCollocationDetailProvider(collocationId: collocation.id ?? 0)
        ..refreshData(),
      child: Consumer<CollectedCollocationDetailProvider>(
        builder: (_, model, __) {
          final detailData = model.collocationDetail?.data;
          
          return Scaffold(
            appBar: AppBar(
              leadingWidth: 200.w,
              elevation: 0,
              backgroundColor: Colors.transparent,
              leading: CustomAppbar.leftWidget(context, text: "搭配详情"),
            ),
            body: SingleChildScrollView(
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 40.w),
                padding: EdgeInsets.all(32.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(24.w),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                detailData?.collocation ?? collocation.collocation ?? "_",
                                style: TextStyle(fontSize: 72.w, fontWeight: FontWeight.w700, color: color_1),
                              ),
                              SizedBox(height: 8.w),
                              Text(
                                collocation.cefrLevel ?? "",
                                style: TextStyle(
                                  fontSize: 38.w,
                                  color: const Color(0xFF41C0FF),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 24.w),
                    _buildPronunciationSection(context, detailData),
                    SizedBox(height: 24.w),
                    _buildDetailSection("释义", detailData?.commonDefinition ?? collocation.commonDefinition),
                    _buildDetailSection("英文例句", detailData?.englishExample ?? collocation.englishExample),
                    _buildDetailSection("中文例句", detailData?.chineseExample ?? collocation.chineseExample),
                    _buildDetailSection("记忆技巧", detailData?.cognitiveTip ?? collocation.cognitiveTip),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPronunciationSection(BuildContext context, dynamic detailData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "发音",
          style: TextStyle(
            fontSize: 28.sp,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        SizedBox(height: 16.w),
        Row(
          children: [
            _buildPronunciationButton("美式发音", detailData?.usPronUrl ?? collocation.usPronunciationUrl),
            SizedBox(width: 40.w),
            _buildPronunciationButton("英式发音", detailData?.ukPronUrl ?? collocation.ukPronunciationUrl),
          ],
        ),
        SizedBox(height: 24.w),
        Container(
          height: 1.w,
          color: ColorUtil.separated,
        ),
      ],
    );
  }

  Widget _buildPronunciationButton(String title, String? url) {
    return Row(
      children: [
        Text(
          title,
          style: style_2_24,
        ),
        SizedBox(width: 8.w),
        PlayButtonWidget(
          style: PlayButtonStyle.blue,
          audioUrl: url,
        ),
      ],
    );
  }

  Widget _buildDetailSection(String title, String? content) {
    if (content == null || content.isEmpty) {
      return SizedBox();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 28.sp,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        SizedBox(height: 8.w),
        Text(
          content,
          style: style_2_28,
        ),
        SizedBox(height: 24.w),
        Container(
          height: 1.w,
          color: ColorUtil.separated,
        ),
        SizedBox(height: 24.w),
      ],
    );
  }
} 