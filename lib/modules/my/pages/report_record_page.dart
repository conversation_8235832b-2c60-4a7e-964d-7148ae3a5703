import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/report_list_model/list.dart';
import 'package:flutter_app_kouyu/common/http/models/report_list_model/report_list.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/chat_report_model.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielts_speaking_report_page.dart'
    as ielts_speaking_report_page;
import 'package:flutter_app_kouyu/modules/ielts/provider/ielts_speaking_report_provider.dart'
    as ielts_speaking_report_provider;
import 'package:flutter_app_kouyu/modules/chat/view/chat_report_page.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielts_speaking_report_page.dart';
import 'package:flutter_app_kouyu/modules/ielts/provider/ielts_speaking_report_provider.dart';
import 'package:flutter_app_kouyu/modules/my/bloc/cubit/ielts_speaking_record_provider.dart';

import 'package:flutter_app_kouyu/modules/my/bloc/cubit/report_record_provider.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ReportRecordPage extends StatelessWidget {
  final ReportType? initType;
  const ReportRecordPage({super.key, this.initType});

  static Route<void> route(RouteSettings? routeSettings, {ReportType? type}) {
    return MaterialPageRoute<void>(
        builder: (_) => ReportRecordPage(
              initType: type,
            ),
        settings: routeSettings);
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<ReportRecordProvider>(
      create: (_) => ReportRecordProvider(initType: initType)..refreshData(),
      child: Consumer<ReportRecordProvider>(
        builder: (context, model, child) {
          return Scaffold(
            backgroundColor: bgColor,
            appBar: AppBar(
              leadingWidth: 100,
              elevation: 0,
              backgroundColor: Colors.transparent,
              leading: CustomAppbar.leftWidget(context, text: ""),
              title: Text(
                "对话报告",
                style: tsBar,
              ),
            ),
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 24.w, left: 40.w, right: 40.w),
                  child: Row(
                    children: [
                      _typeWidget(model, ReportType.all),
                      SizedBox(
                        width: 40.w,
                      ),
                      _typeWidget(model, ReportType.freeTalk),
                      SizedBox(
                        width: 40.w,
                      ),
                      _typeWidget(model, ReportType.real),
                      SizedBox(
                        width: 40.w,
                      ),
                      _typeWidget(model, ReportType.ielts),
                    ],
                  ),
                ),
                SizedBox(
                  height: 24.w,
                ),
                Expanded(child: _recordWidget(context))
              ],
            ),
          );
        },
      ),
    );
  }

  GestureDetector _typeWidget(ReportRecordProvider model, ReportType type) {
    return GestureDetector(
      onTap: () {
        model.type = type;
      },
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Text(
            type.name,
            style: model.type == type ? style_1_36 : style_2_36,
          ),
          if (model.type == type)
            Image.asset(
              "images/text_bg.png",
              width: 63.w,
              height: 16.w,
            )
        ],
      ),
    );
  }

  Widget _recordWidget(BuildContext context) {
    ReportRecordProvider model = context.read<ReportRecordProvider>();
    List<ReportList> list = model.reportListModel?.data?.reportList ?? [];
    return SmartRefresher(
      controller: model.refreshController,
      onRefresh: () {
        model.refreshData();
      },
      onLoading: () {
        model.loadMore();
      },
      enablePullUp: true,
      child: CustomScrollView(
        slivers: [
          ...list.map((e) => __recordsList(context, e, model.type)),
        ],
      ),
    );
  }

  Widget __recordsList(
      BuildContext context, ReportList reportList, ReportType reportType) {
    return SliverToBoxAdapter(
      child: Container(
        margin: EdgeInsets.only(
          left: 40.w,
          right: 40.w,
          bottom: 24.w,
        ),
        padding: EdgeInsets.only(
          left: 32.w,
          right: 32.w,
          top: 24.w,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.w),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              reportList.groupBy ?? "_",
              style: style_2_24,
            ),
            ListView.separated(
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                ListItem item = reportList.list![index];
                return GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    //查看报告
                    Navigator.push(
                      context,
                      MaterialPageRoute<void>(
                        builder: (_) => reportType == ReportType.ielts
                            ? ielts_speaking_report_page
                                .IeltsSpeakingReportPage(
                                practiceId: item.practiceId!,
                                type: ielts_speaking_report_provider
                                    .IeltsSpeakingReportDetailType.view,
                              )
                            : ChatReportPage(
                                conversationId: item.conversationId!,
                                type: ReportDetailType.view,
                              ),
                      ),
                    );
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 24.w, bottom: 24.w),
                    child: Column(
                      children: [
                        SizedBox(
                          height: (80.w + (item.topicName?.length)! * 0.7)
                              .clamp(80.w, 150.w),
                          // height: 80.w,
                          child: Row(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(40.w),
                                child: Image.network(
                                  item.topicImgUrl ??
                                      (LoginUtil.currentUserInfoModel()
                                              ?.data
                                              ?.userSettings
                                              ?.characterImageUrl ??
                                          ""),
                                  height: 80.w,
                                  width: 80.w,
                                  fit: BoxFit.fill,
                                ),
                              ),
                              SizedBox(
                                width: 20.w,
                              ),
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          item.topicName ?? "_",
                                          style: style_1_28,
                                          overflow: TextOverflow.visible,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 20.w,
                                      ),
                                      if (reportType == ReportType.ielts)
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 4.w, vertical: 2.w),
                                          decoration: BoxDecoration(
                                            border: Border.all(color: bgColor),
                                            borderRadius:
                                                BorderRadius.circular(10.w),
                                          ),
                                          child: Text(
                                            getIeltsTag(item.practiceType ?? 1),
                                            style: style_1_16,
                                          ),
                                        ),
                                    ],
                                  ),
                                  // Text(
                                  //   item.topicName ?? "_",
                                  //   style: style_1_28,
                                  // ),

                                  const Spacer(),
                                  Text(
                                    "${item.startTime}~${item.endTime}",
                                    style: style_2_24,
                                  )
                                ],
                              )),
                              SizedBox(
                                width: 20.w,
                              ),
                              Image.asset(
                                "images/arrow_right_black.png",
                                width: 12.w,
                                height: 20.w,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 20.w,
                        ),
                        Container(
                          height: 120.w,
                          decoration: BoxDecoration(
                            color: bgColor,
                            borderRadius: BorderRadius.circular(24.w),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              if (reportType != ReportType.ielts)
                                _socreWidget("总评分", item.totalScore),
                              if (reportType != ReportType.ielts)
                                _socreWidget("发音评分", item.pronunciationScore),
                              if (reportType != ReportType.ielts)
                                _socreWidget("语法评分", item.grammarScore),
                              if (reportType == ReportType.ielts)
                                _socreIeltstWidget("总评分", item.totalScore),
                              if (reportType == ReportType.ielts)
                                _socreIeltstWidget(
                                    "发音评分", item.pronunciationScore),
                              if (reportType == ReportType.ielts)
                                _socreIeltstWidget("语法评分", item.grammarScore),
                              if (reportType == ReportType.ielts)
                                _socreIeltstWidget(
                                    "词汇评分", item.vocabularyScore),
                              if (reportType == ReportType.ielts)
                                _socreIeltstWidget("流利度评分", item.fluencyScore),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) {
                return Container(
                  color: separated,
                  height: 1.w,
                  width: double.infinity,
                );
              },
              itemCount: (reportList.list ?? []).length,
            ),
          ],
        ),
      ),
    );
  }

  Column _socreWidget(String title, double? score) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "${score ?? "_"}",
          style: style_1_28.copyWith(color: getColor(score ?? 0)),
        ),
        Text(
          title,
          style: style_3_24,
        )
      ],
    );
  }

  Column _socreIeltstWidget(String title, double? score) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "${score ?? "_"}",
          style: style_1_28.copyWith(color: getIeltsColor(score ?? 0)),
        ),
        Text(
          title,
          style: style_3_24,
        )
      ],
    );
  }

  Color getColor(double Score) {
    if (Score <= 60) {
      return const Color.fromARGB(255, 249, 50, 0);
    } else if (Score <= 80) {
      return const Color(0xFF061B1F);
    } else if (Score <= 100) {
      return const Color.fromARGB(255, 42, 201, 92);
    }

    return const Color(0xFF061B1F);
  }

  Color getIeltsColor(double Score) {
    if (Score < 6) {
      return const Color.fromARGB(255, 249, 50, 0);
    } else if (Score < 7) {
      return const Color(0xFF061B1F);
    }

    return const Color.fromARGB(255, 42, 201, 92);
  }

  String getIeltsTag(int practiceType) {
    switch (practiceType) {
      case 1:
        return "练习";
      case 2:
        return "模考";
      case 3:
        return "测试";
      default:
        return "模考";
    }
  }
}

enum ReportType { all, freeTalk, real, ielts }

extension ReportTypeExtension on ReportType {
  String get name {
    switch (this) {
      case ReportType.all:
        return "全部";
      case ReportType.freeTalk:
        return "自由对话";
      case ReportType.real:
        return "实战演练";
      case ReportType.ielts:
        return "雅思口语";
    }
  }

  int get requestType {
    switch (this) {
      case ReportType.all:
        return 1;
      case ReportType.freeTalk:
        return 2;
      case ReportType.real:
        return 3;
      case ReportType.ielts:
        return 104;
    }
  }
}
