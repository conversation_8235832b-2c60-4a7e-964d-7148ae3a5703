import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SetupSettingStage extends StatefulWidget {
  const SetupSettingStage({super.key});
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const SetupSettingStage());
  }

  @override
  State<SetupSettingStage> createState() => _SetupSettingStageState();
}

class _SetupSettingStageState extends State<SetupSettingStage> {
  @override
  void initState() {
    super.initState();
    _initStageList(); // 添加初始化方法调用
  }

  Future<void> _initStageList() async {
    try {
      final response = await Api.getLearningPhaseList();
      if (response.data != null) {
        setState(() {
          stageList = response.data!.map((item) {
            // 假设 API 返回的数据包含 code 和 name 字段
            return SettingStage(item.code ?? '', item.name ?? '');
          }).toList();
        });
      }
    } catch (e) {
      print('获取学习阶段列表失败: $e');
      EasyLoading.showToast("获取学习阶段列表失败");
    }
  }

  List<String> type = [];
  List<SettingStage> stageList = [];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leadingWidth: 300,
        backgroundColor: const Color(0xFFFFFFFF),
        scrolledUnderElevation: 0,
        // leading: CustomAppbar.leftCloseWidget(
        //   context,
        //   ontap: () {
        //     Navigator.of(context)
        //         .pushNamedAndRemoveUntil("/home", (route) => false);
        //   },
        // ),
        leading: CustomAppbar.leftWidgetWithNoIcon(context, text: ""),
        actions: [
          // GestureDetector(
          //   onTap: () {
          //     Navigator.of(context).pushNamedAndRemoveUntil(
          //         "/setup_setting_two", (route) => false);
          //   },
          //   child: Text(
          //     "跳过",
          //     style: style_4_28_400,
          //   ),
          // ),
          // SizedBox(
          //   width: 40.w,
          // )
        ],
      ),
      backgroundColor: const Color(0xFFFFFFFF),
      body: Stack(
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(48.w, 40.w, 48.w, 100.w),
            child: Column(
              children: [
                Row(
                  children: [],
                ),
                Expanded(
                  child: ListView(
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "你的学习阶段",
                            style: style_1_40_800,
                          ),
                          const SizedBox(
                            width: 8,
                          )
                        ],
                      ),
                      const SizedBox(
                        height: 4,
                      ),
                      Text(
                        "老师会根据你的学习阶段来匹配你的学习内容",
                        style: style_4_24_400,
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      ListView.separated(
                        separatorBuilder: (context, index) => SizedBox(
                          height: 20.w,
                        ),
                        itemBuilder: (context, index) {
                          SettingStage stage = stageList[index];
                          return Align(
                            alignment: Alignment.center,
                            child: ClipRRect(
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(12)),
                              child: Container(
                                color: Colors.white,
                                // padding: const EdgeInsets.all(16),
                                child: GestureDetector(
                                  behavior: HitTestBehavior.opaque,
                                  onTap: () {
                                    setState(() {
                                      type = [stage.code];
                                    });
                                  },
                                  child: Row(
                                    children: [
                                      const Spacer(),
                                      Container(
                                        padding: EdgeInsets.all(32.w),
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            border: Border.all(
                                                color: Color(0xFFCEDDE0))),
                                        height: 112.w,
                                        width: 654.w,
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(stage.name,
                                                style: style_1_32_600),
                                            // Text(stage.name, style: TextStyle(color: const Color(0xFF272733), fontSize: 32.w, fontWeight: FontWeight.w600)),
                                            const Spacer(),
                                            Image.asset(
                                              type.contains(stage.code)
                                                  ? "images/check_circle_blue.png"
                                                  : "images/un_check_circle.png",
                                              width: 18,
                                              height: 18,
                                            )
                                          ],
                                        ),
                                      ),
                                      const Spacer(),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                        itemCount: stageList.length,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 固定在底部的按钮
          Positioned(
            left: 20,
            right: 20,
            bottom: 46,
            child: Container(
              height: 52,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: const Color(0xFF2693FF),
              ),
              child: TextButton(
                  onPressed: () async {
                    if (type.isEmpty) {
                      EasyLoading.showToast("至少选择一项");
                      return;
                    }
                    EasyLoading.show();
                    await Api.setLearningPhase(type[0]).then((value) {
                      EasyLoading.dismiss();
                      Navigator.of(context).pushNamed("/setup_setting_two");
                    });
                    EasyLoading.dismiss();
                  },
                  style: const ButtonStyle(
                    textStyle: MaterialStatePropertyAll(
                        TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
                  ),
                  child: const Center(
                    child: Text("下一步",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w800)),
                  )),
            ),
          ),
        ],
      ),
    );
  }
}

class GradientText extends StatelessWidget {
  const GradientText(
    this.text, {
    super.key,
    required this.gradient,
    this.style,
  });

  final String text;
  final TextStyle? style;
  final Gradient gradient;

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (bounds) => gradient.createShader(
        Rect.fromLTWH(0, 0, bounds.width, bounds.height),
      ),
      // 让文字的颜色变为透明，这样ShaderMask的着色器才能生效
      blendMode: BlendMode.srcIn,
      child: Text(
        text,
        style: style?.copyWith(color: Colors.white),
      ),
    );
  }
}

class SettingStage {
  final String code;
  final String name;

  SettingStage(this.code, this.name);
}
