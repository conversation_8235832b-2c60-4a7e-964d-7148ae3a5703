import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/collection_sentence_detail/collection_sentence_detail.dart';
import 'package:flutter_app_kouyu/common/http/models/collection_sentence_detail/data.dart';

import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';
import 'package:flutter_app_kouyu/modules/chat/view/follow_read_widget.dart';
import 'package:flutter_app_kouyu/modules/my/bloc/cubit/collected_sentence_detail_provider.dart';
import 'package:flutter_app_kouyu/widgets/common-input-widget/left-ai-middle-btn-right-score.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gif_view/gif_view.dart';
import 'package:provider/provider.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/data.dart'
    as SoeData;

class CollectedSentenceDetailPage extends StatelessWidget {
  final String sentence;
  final String collectId;
  const CollectedSentenceDetailPage(
      {super.key, required this.sentence, required this.collectId});
  static Route<void> route(String sentence, String collectId) {
    return MaterialPageRoute<void>(
        builder: (_) => CollectedSentenceDetailPage(
              sentence: sentence,
              collectId: collectId,
            ));
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<CollectedSentenceDetailProvider>(
      create: (_) => CollectedSentenceDetailProvider(
          sentence: sentence, collectId: collectId)
        ..refreshData(),
      child: Consumer<CollectedSentenceDetailProvider>(
        builder: (context, model, child) {
          return Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              leadingWidth: 100,
              elevation: 0,
              leading: CustomAppbar.leftWidget(context, text: ""),
            ),
            body: _vocabularyCardItem(model.sentenceDetail, context),
          );
        },
      ),
    );
  }

  Widget _vocabularyCardItem(
      CollectionSentenceDetail? detail, BuildContext context) {
    CollectedSentenceDetailProvider model =
        context.read<CollectedSentenceDetailProvider>();

    Data? sentence = detail?.data;
    return Padding(
      padding: EdgeInsets.only(left: 40.w, right: 40.w, bottom: 100.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(gradient: ColorUtil.blueToGreen),
          ),
          Text(
            sentence?.sentence ?? "_",
            style: TextStyle(
                fontSize: 40.sp, fontWeight: FontWeight.w700, color: color_1),
          ),
          SizedBox(height: 16.w),
          Text(
            sentence?.chineseMeaning ?? "",
            style: TextStyle(
                fontSize: 32.sp, fontWeight: FontWeight.w400, color: color_2),
          ),
          const Spacer(),
          if (model.soeModel?.data != null) ..._soeList(model.soeModel!.data!),
          model.voiceState == VoiceStateEnum.init
              ? _followWidget(context, sentence?.defaultPronunciationUrl,
                  sentence?.score, sentence?.userPronunciationUrl)
              : _speakingWidget(context),
        ],
      ),
    );
  }

  Row _speakingWidget(BuildContext context) {
    CollectedSentenceDetailProvider model =
        context.read<CollectedSentenceDetailProvider>();

    return Row(
      children: [
        GestureDetector(
          onTap: () {
            model.stop();
          },
          child: Image(
            image: const AssetImage("images/chat_close.png"),
            width: 80.w,
            height: 80.w,
          ),
        ),
        SizedBox(
          width: 40.w,
        ),
        Expanded(
          child: GifView.asset(
            "images/chat_loading.gif",
            fit: BoxFit.cover,
            width: double.infinity,
            height: 48.w,
            repeat: ImageRepeat.repeat,
          ),
        ),
        SizedBox(
          width: 40.w,
        ),
        GestureDetector(
          onTap: () {
            model.sent();
          },
          child: Image(
            image: const AssetImage("images/chat_send.png"),
            width: 80.w,
            height: 80.w,
          ),
        ),
      ],
    );
  }

  Container _lineWidget() {
    return Container(
      margin: EdgeInsets.only(left: 40.w, right: 40.w),
      height: 1.w,
      width: double.infinity,
      color: ColorUtil.separated,
    );
  }

  List<Widget> _soeList(SoeData.Data soeModel) {
    return [
      SizedBox(
        width: double.infinity,
        height: 585.w,
        child: Stack(alignment: Alignment.topCenter, children: [
          Positioned(
              top: 192.w,
              child: Container(
                width: 585.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(40.w),
                  color: const Color(0xFFF0F8FA),
                ),
                child: Column(
                  children: [
                    SizedBox(
                      height: 100.w,
                    ),
                    Text(
                      soeModel.speedComment ?? "_",
                      style: style_1_28_400,
                    ),
                    SizedBox(
                      height: 24.w,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        scoreWidget(soeModel.pronFluency, "流利度"),
                        scoreWidget(soeModel.pronAccuracy, "准确度"),
                        scoreWidget(soeModel.pronCompletion, "完整度"),
                        // scoreWidget("_/分钟", "语速"),
                      ],
                    ),
                    SizedBox(
                      height: 24.w,
                    ),
                    _lineWidget(),
                    SizedBox(
                      height: 24.w,
                    ),
                    Row(
                      children: [
                        SizedBox(
                          width: 40.w,
                        ),
                        Text("${soeModel.speed}", style: style_1_40),
                        SizedBox(
                          width: 8.w,
                        ),
                        Text("单词/分钟", style: style_1_24),
                        const Spacer(),
                        Text(
                          "语速较快，水平不错",
                          style: style_1_28_400,
                        ),
                        SizedBox(
                          width: 40.w,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 40.w,
                    ),
                  ],
                ),
              )),
          Container(
            width: 392.w,
            height: 392.w,
            decoration: BoxDecoration(
              image: const DecorationImage(
                  alignment: Alignment.topCenter,
                  image: AssetImage("images/flollow_read_score_bg.png"),
                  fit: BoxFit.fill),
              borderRadius: BorderRadius.circular(40.w),
            ),
            child: Center(
                child: Text("${soeModel.suggestedScore ?? "_"}",
                    style: style_1_48.copyWith(fontSize: 50.sp))),
          )
        ]),
      ),
      SizedBox(
        height: 48.w,
      ),
    ];
  }

  Widget scoreWidget(int? score, String subTitle) {
    return Column(
      children: [
        Text("${score ?? "_"}",
            style: TextStyle(
                fontSize: 40.sp,
                fontWeight: FontWeight.w700,
                color: getColor(score ?? 0))),
        Text(subTitle, style: style_2_24),
      ],
    );
  }

  Color getColor(int Score) {
    if (Score <= 60) {
      return const Color.fromARGB(255, 249, 50, 0);
    } else if (Score <= 80) {
      return const Color(0xFF061B1F);
    } else if (Score <= 100) {
      return const Color.fromARGB(255, 42, 201, 92);
    }

    return const Color(0xFF061B1F);
  }

  Widget _followWidget(
      BuildContext context, String? url, String? score, String? userUrl) {
    CollectedSentenceDetailProvider model =
        context.read<CollectedSentenceDetailProvider>();

    return LeftAiMiddleBtnRightScore(
      aiUrl: url ?? "",
      btnOnTap: () {
        model.speakEn();
      },
      score: score ?? "",
      userUrl: userUrl ?? "",
    );
  }
}
