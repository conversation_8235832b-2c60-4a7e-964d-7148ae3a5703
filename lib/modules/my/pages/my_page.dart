import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/config/constant.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/login_util.dart';
import 'package:flutter_app_kouyu/modules/my/bloc/cubit/my_page_cubit.dart';
import 'package:flutter_app_kouyu/modules/my/pages/my_collection_page.dart';
import 'package:flutter_app_kouyu/modules/my/pages/person_info_page.dart';
import 'package:flutter_app_kouyu/modules/my/pages/report_record_page.dart';
import 'package:flutter_app_kouyu/modules/scene_study/pages/my_collect_page.dart';
import 'package:flutter_app_kouyu/modules/scene_study/pages/my_create_page.dart';
import 'package:flutter_app_kouyu/my_app.dart';
import 'package:flutter_app_kouyu/repository/user_info_reponsitory.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:wechat_kit/wechat_kit.dart';

class MyPage extends StatelessWidget {
  const MyPage({super.key});

  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const MyPage());
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          height: double.infinity,
          color: const Color(0xFFFFFFFF),
        ),
        Image.asset(
          "images/my_bg.png",
          width: double.infinity,
        ),
        Scaffold(
            backgroundColor: Colors.transparent,
            appBar: PreferredSize(
              preferredSize: Size.fromHeight(50.w),
              child: AppBar(
                automaticallyImplyLeading: false, // 这行代码会禁用自动添加的返回按钮
                backgroundColor: Colors.transparent,
                actions: [
                  GestureDetector(
                      onTap: () {
                        Navigator.of(context).pushNamed("/user_settings");
                      },
                      child: Image.asset(
                        "images/icon_setting.png",
                        width: 24,
                        height: 24,
                      )),
                  const SizedBox(
                    width: 20,
                  )
                ],
              ),
            ),
            body: SafeArea(
              child: RepositoryProvider<UserInfoReponsitory>(
                create: (context) {
                  return UserInfoReponsitory();
                },
                child: BlocProvider(
                  create: (context) => MyPageCubit(
                      userInfoResponsitory:
                          RepositoryProvider.of<UserInfoReponsitory>(context))
                    ..initUserInfo(),
                  child: BlocBuilder<MyPageCubit, MyPageState>(
                    builder: (context, state) {
                      return ListView(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 20, right: 20),
                            child: _titleWidget(),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          state.userInfoModel?.data?.memberType == 1
                              ? Stack(children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 20, right: 20),
                                    child: _vipTimeWidget(context),
                                  ),
                                  Positioned(
                                    top: 0,
                                    right: 16,
                                    child: FutureBuilder(
                                        future: Api.getPromotionConfig(),
                                        builder: (context, snapshot) {
                                          return CachedNetworkImage(
                                              height: 16,
                                              fit: BoxFit.fitHeight,
                                              imageUrl: snapshot.data?.data
                                                      ?.lowerLeftLabelUrl ??
                                                  "");
                                        }),
                                  )
                                ])
                              : Padding(
                                  padding: const EdgeInsets.only(
                                      left: 20, right: 20),
                                  child: _timeWidget(),
                                ),
                          Padding(
                            padding: const EdgeInsets.only(left: 20, right: 20),
                            child: Visibility(
                                visible:
                                    state.userInfoModel?.data?.memberType != 1,
                                child: _vipAdWidget(context)),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 20, right: 20),
                            child: _sceneDataWidget(context),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 20, right: 20),
                            child: _studyRecordWidget(context),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 20, right: 20),
                            child: _functionListWidget(),
                          )
                        ],
                      );
                    },
                  ),
                ),
              ),
            )),
      ],
    );
  }

  /// 场景对话
  Widget _sceneDataWidget(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 0),
      padding: const EdgeInsets.only(left: 0, right: 18, top: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "社区场景",
            style: TextStyle(
                fontSize: 32.sp,
                fontWeight: FontWeight.w800,
                color: const Color(0xFF061B1F)),
          ),
          const SizedBox(
            height: 16,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: _sceneDataItemWidget("我的收藏", () {
                  Navigator.of(context).push(MyCollectPage.route());
                }),
              ),
              Container(
                margin: EdgeInsets.only(left: 24.w, right: 24.w),
                width: 1.w,
                height: 50.w,
                color: ColorUtil.separated,
              ),
              Expanded(
                child: _sceneDataItemWidget("我创建的", () {
                  Navigator.of(context).push(MyCreatePage.route());
                }),
              ),
            ],
          ),
          const SizedBox(
            height: 20,
          ),
        ],
      ),
    );
  }

  Widget _sceneDataItemWidget(String title, GestureTapCallback onTap) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: style_2_28),
          Row(
            children: [
              // Text(count, style: style_1_36),
              // 一个向右的箭头
              Image.asset(
                "images/right_arrow_gray.png",
                width: 40.w,
                height: 40.w,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _studyRecordWidget(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 0),
      padding: const EdgeInsets.only(left: 0, right: 18, top: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "学习记录",
            style: TextStyle(
                fontSize: 32.sp,
                fontWeight: FontWeight.w800,
                color: const Color(0xFF061B1F)),
          ),
          const SizedBox(
            height: 16,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _reportWidget(context),
              _myCollectWidget(context),
              // _stepWidget(context),
              Expanded(child: Container()),
            ],
          ),
          const SizedBox(
            height: 20,
          ),
        ],
      ),
    );
  }

  Widget _functionListWidget() {
    return BlocBuilder<MyPageCubit, MyPageState>(
      builder: (context, state) {
        return FutureBuilder(
            future: WechatKitPlatform.instance.isInstalled(),
            builder: (context, shot) {
              List<Widget> list1 = [];
              List<Widget> list2 = [];

              if (shot.data == true) {
                //安装微信
                list1 = [
                  _studyGroupWidget(),
                  // _englishLevelWidget(state, context),
                  _changeTeacherWidget(context),
                  _wechatWidget(shot),
                ];
                list2 = [
                  _feekbackWidget(context),
                  _customerWidget(),
                  _aboutUsWidget(context),
                  // Expanded(child: Container()),
                ];
              } else {
                list1 = [
                  // _englishLevelWidget(state, context),
                  _changeTeacherWidget(context),
                  _feekbackWidget(context),
                  _aboutUsWidget(context),
                ];
                list2 = [];
              }

              return Container(
                margin: const EdgeInsets.only(top: 0),
                padding: const EdgeInsets.only(left: 0, right: 18, top: 10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "更多服务",
                      style: TextStyle(
                          fontSize: 32.sp,
                          fontWeight: FontWeight.w800,
                          color: const Color(0xFF061B1F)),
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: list1,
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Row(
                      children: list2,
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                  ],
                ),
              );
            });
      },
    );
  }

  Expanded _shareWidget() {
    return Expanded(
      child: _functionItemWidget(
        image: const Image(
          image: AssetImage("images/my_func_share.png"),
          width: 24,
          height: 24,
        ),
        text: "分享app",
        onTap: () {},
      ),
    );
  }

  Expanded _aboutUsWidget(BuildContext context) {
    return Expanded(
      child: _functionItemWidget(
        image: const Image(
          image: AssetImage("images/my_func_aboutus.png"),
          width: 24,
          height: 24,
        ),
        text: "关于我们",
        onTap: () {
          Navigator.of(context).pushNamed("/about_us");
        },
      ),
    );
  }

  /// 联系客服
  Expanded _customerWidget() {
    return Expanded(
      child: _functionItemWidget(
        image: const Image(
          image: AssetImage("images/my_func_customer.png"),
          width: 24,
          height: 24,
        ),
        text: "联系客服",
        onTap: () async {
          // https: //work.weixin.qq.com/u/vc9fa245f532c50fa6?v=4.1.20.26620
          WechatKitPlatform.instance.launchMiniProgram(
              userName: wechatOriginId,
              path: "/pages/customerService/index?type=customer_manager");
        },
      ),
    );
  }

  Expanded _feekbackWidget(BuildContext context) {
    return Expanded(
        child: _functionItemWidget(
      image: const Image(
        image: AssetImage("images/my_func_feedback.png"),
        width: 24,
        height: 24,
      ),
      text: "用户反馈",
      onTap: () {
        Navigator.of(context).pushNamed("/feedback");
      },
    ));
  }

    Expanded _stepWidget(BuildContext context) {
    return Expanded(
        child: _functionItemWidget(
      image: const Image(
        image: AssetImage("images/my_func_feedback.png"),
        width: 24,
        height: 24,
      ),
      text: "引导页测试",
      onTap: () {
        Navigator.of(context).pushNamed("/setup_setting_one");
      },
    ));
  }

  Visibility _wechatWidget(AsyncSnapshot<bool> shot) {
    return Visibility(
      visible: shot.data == true,
      child: Expanded(
        child: _functionItemWidget(
          image: const Image(
            image: AssetImage("images/my_func_wechat.png"),
            width: 24,
            height: 24,
          ),
          text: "微信小程序",
          onTap: () async {
            WechatKitPlatform.instance.launchMiniProgram(
                userName: wechatOriginId, path: "pages/newIndex/newIndex");
          },
        ),
      ),
    );
  }

  Expanded _changeTeacherWidget(BuildContext context) {
    return Expanded(
      child: _functionItemWidget(
        image: const Image(
          image: AssetImage("images/my_func_exchange.png"),
          width: 24,
          height: 24,
        ),
        text: "变更语伴",
        onTap: () {
          Navigator.of(context).pushNamed("/change_teacher");
        },
      ),
    );
  }

  Expanded _reportWidget(BuildContext context) {
    return Expanded(
      child: _functionItemWidget(
        image: const Image(
          image: AssetImage("images/my_report_icon.png"),
          width: 24,
          height: 24,
          color: Color(0xFF2693FF),
        ),
        text: "对话报告",
        onTap: () async {
          final userInfo = await LoginUtil.userInfoModel();
          bool isVip = userInfo?.data?.memberType == 1;
          if (!isVip) {
            globalNavigatorKey.currentState?.pushNamed("/open_vip");
          } else {
            Navigator.of(context).push(ReportRecordPage.route(null));
          }
        },
      ),
    );
  }

  Expanded _myCollectWidget(BuildContext context) {
    return Expanded(
      child: _functionItemWidget(
        image: const Image(
          image: AssetImage("images/mine_colleced_icon.png"),
          width: 24,
          height: 24,
          color: Color(0xFF2693FF),
        ),
        text: "我的收藏",
        onTap: () async {
          Navigator.of(context).push(MyCollectionPage.route());
        },
      ),
    );
  }

  Expanded _studyGroupWidget() {
    return Expanded(
      child: _functionItemWidget(
        image: const Image(
          image: AssetImage("images/my_func_book.png"),
          width: 24,
          height: 24,
        ),
        text: "加入学习群",
        onTap: () async {
          // https: //work.weixin.qq.com/u/vc9fa245f532c50fa6?v=4.1.20.26620
          WechatKitPlatform.instance.launchMiniProgram(
              userName: wechatOriginId,
              path: "/pages/customerService/index?type=wechat_group");
        },
      ),
    );
  }

  Expanded _englishLevelWidget(MyPageState state, BuildContext context) {
    return Expanded(
      child: _functionItemWidget(
        image: const Image(
          image: AssetImage("images/my_func_kouyu.png"),
          width: 24,
          height: 24,
        ),
        text: "英语水平选择",
        secondText:
            state.userInfoModel?.data?.englishLevelChineseAbbreviation ?? "",
        onTap: () {
          Navigator.of(context).pushNamed("/enlish_level_choose");
        },
      ),
    );
  }

  GestureDetector _stuyGroupWidget() {
    return _functionItemWidget(
      image: const Image(
        image: AssetImage("images/my_func_book.png"),
        width: 24,
        height: 24,
      ),
      text: "加入学习群",
      onTap: () async {
        // https: //work.weixin.qq.com/u/vc9fa245f532c50fa6?v=4.1.20.26620
        WechatKitPlatform.instance.launchMiniProgram(
            userName: wechatOriginId,
            path: "/pages/customerService/index?type=wechat_group");
      },
    );
  }

  GestureDetector _functionItemWidget(
      {required Image image,
      required String text,
      String secondText = '',
      GestureTapCallback? onTap}) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(24.w),
            decoration: BoxDecoration(
              color: Colors.transparent,
              // 圆形, border 颜色CEDDE0
              border: Border.all(
                color: const Color(0xFFCEDDE0),
                width: 1.w,
              ),
              shape: BoxShape.circle,
            ),
            child: image,
          ),
          SizedBox(
            height: 10.w,
          ),
          Text(
            text,
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF646E70)),
          ),
        ],
      ),
    );
  }

  Widget _vipAdWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context)
            .pushNamed('/open_vip')
            .then((value) => context.read<MyPageCubit>().initUserInfo());
      },
      child: Padding(
        padding: const EdgeInsets.only(top: 12.0),
        child: Stack(
          children: [
            const Image(
              image: AssetImage("images/my_open_vip.png"),
            ),
            Positioned.fill(
                top: 0,
                right: 0,
                child: Center(
                  child: FractionallySizedBox(
                    alignment: Alignment.topRight,
                    heightFactor: 250 / 294.0,
                    widthFactor: 618 / 668.0,
                    child: FractionallySizedBox(
                      alignment: Alignment.topRight,
                      heightFactor: 32 / 250.0,
                      child: Row(
                        children: [
                          const Spacer(),
                          FutureBuilder(
                              future: Api.getPromotionConfig(),
                              builder: (context, snapshot) {
                                return CachedNetworkImage(
                                    fit: BoxFit.fitHeight,
                                    imageUrl: snapshot
                                            .data?.data?.lowerLeftLabelUrl ??
                                        "");
                              })
                        ],
                      ),
                    ),
                  ),
                ))
          ],
        ),
      ),
    );
  }

  Widget _timeWidget() {
    return BlocBuilder<MyPageCubit, MyPageState>(
      builder: (context, state) {
        return Row(
          children: [
            const Spacer(),
            _timeItemWidget(
                days: '${state.userInfoModel?.data?.availableMinutes ?? "_"}',
                desc: "可用时长",
                unit: "分钟"),
            const Spacer(),
            _timeItemWidget(
                days: '${state.userInfoModel?.data?.usedMinutes ?? "_"}',
                desc: "已学习",
                unit: "分钟"),
            const Spacer(),
            _timeItemWidget(
                days:
                    '${state.userInfoModel?.data?.complimentaryMinutes?.toInt() ?? "_"}',
                desc: "已获赠",
                unit: "分钟"),
            const Spacer(),
          ],
        );
      },
    );
  }

  Widget _vipTimeWidget(BuildContext context) {
    return BlocBuilder<MyPageCubit, MyPageState>(
      builder: (context, state) {
        return Container(
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage('images/my_vip_bg2.png'),
              fit: BoxFit.fill, // 完全填充
            ),
          ),
          child: Column(
            children: [
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pushNamed('/open_vip').then(
                      (value) => context.read<MyPageCubit>().initUserInfo());
                  ;
                },
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(12, 12, 12, 6),
                  child: Image.asset(
                    "images/my_vip_button.png",
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.all(4),
                padding: const EdgeInsets.all(12),
                decoration: const BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(12)),
                    color: Colors.white),
                child: Row(
                  children: [
                    const Spacer(),
                    _timeItemWidget(
                        days:
                            '${state.userInfoModel?.data?.availableDays ?? "_"}',
                        desc: "会员有效期",
                        unit: "天"),
                    const Spacer(),
                    _timeItemWidget(
                        days:
                            '${state.userInfoModel?.data?.usedMinutes ?? "_"}',
                        desc: "已学习",
                        unit: "分钟"),
                    const Spacer(),
                    _timeItemWidget(
                        days:
                            '${state.userInfoModel?.data?.complimentaryMinutes?.toInt() ?? "_"}',
                        desc: "已获赠",
                        unit: "分钟"),
                    const Spacer(),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _timeItemWidget(
      {required String days, required String unit, required String desc}) {
    return Center(
      child: Column(
        children: [
          Row(
            children: [
              Text(
                days,
                style: const TextStyle(
                    color: Color(0xFF061B1F),
                    fontSize: 20,
                    fontWeight: FontWeight.w700),
              ),
              Text(
                unit,
                style: const TextStyle(
                    color: Color(0xFF061B1F),
                    fontSize: 12,
                    fontWeight: FontWeight.w400),
              )
            ],
          ),
          Text(desc,
              style: const TextStyle(
                  color: Color(0xFF646E70),
                  fontSize: 12,
                  fontWeight: FontWeight.w400)),
        ],
      ),
    );
  }

  Widget _titleWidget() {
    return BlocBuilder<MyPageCubit, MyPageState>(
      builder: (context, state) {
        return GestureDetector(
          onTap: () {
            Navigator.of(context).push(
              PersonInfoPage.route(state.userInfoModel!)
            ).then((value) => context.read<MyPageCubit>().initUserInfo());
          },
          child: Row(
            children: [
              Container(
                height: 60,
                width: 60,
                decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(
                        width: 2,
                        color: state.userInfoModel?.data?.memberType == 1
                            ? const Color(0xFFFFCE46)
                            : Colors.white),
                    borderRadius: const BorderRadius.all(Radius.circular(30))),
                child: state.userInfoModel?.data?.avatarUrl != null
                    ? Image.network(state.userInfoModel!.data!.avatarUrl!)
                    : null,
              ),
              const SizedBox(
                width: 12,
              ),
              Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(state.userInfoModel?.data?.nickname ?? "_",
                            style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                                color: Color(0xFF061B1F))),
                        const SizedBox(
                          width: 6,
                        ),
                        Container(
                          height: 16,
                          padding: const EdgeInsets.all(2),
                          decoration: const BoxDecoration(
                              borderRadius: BorderRadius.all(Radius.circular(6))),
                          child: Row(
                            children: [
                              Image.asset(
                                "images/vip_small.png",
                              ),
                              Text(
                                  state.userInfoModel?.data?.membershipLevel ??
                                      "_",
                                  style: const TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w400,
                                      color: Color(0xFF061B1F))),
                            ],
                          ),
                        )
                      ],
                    ),
                    const SizedBox(
                      height: 4,
                    ),
                    Text("一起学习的第${state.userInfoModel?.data?.daysOfStudy}天",
                        style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF646E70)))
                  ])
            ],
          ),
        );
      },
    );
  }
}
