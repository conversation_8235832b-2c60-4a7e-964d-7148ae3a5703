import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/purpuse_model/data.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class SetupSettingThree extends StatefulWidget {
  const SetupSettingThree({super.key});
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const SetupSettingThree());
  }

  @override
  State<SetupSettingThree> createState() => _SetupSettingThreeState();
}

class _SetupSettingThreeState extends State<SetupSettingThree> {
  List<String> type = [];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leadingWidth: 300,
        backgroundColor: const Color(0xFFF3FBFD),
        leading: CustomAppbar.leftWidget(context, text: "学习偏好设置"),
        // actions: [
        // GestureDetector(
        //   onTap: () {
        //     Navigator.of(context)
        //         .pushNamedAndRemoveUntil("/home", (route) => false);
        //   },
        //   child: const Text(
        //     "跳过",
        //     style: TextStyle(
        //         fontSize: 14,
        //         fontWeight: FontWeight.w400,
        //         color: Color(0xFF646E70)),
        //   ),
        // ),
        //   const SizedBox(
        //     width: 12,
        //   )
        // ],
      ),
      backgroundColor: const Color(0xFFF3FBFD),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(20, 20, 20, 130),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 8,
                    // width: 80,
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        color: Color(0xFF41C0FF)),
                  ),
                ),
                const SizedBox(
                  width: 6,
                ),
                Expanded(
                  child: Container(
                    height: 8,
                    // width: 80,
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        color: Color(0xFF41C0FF)),
                  ),
                ),
                const SizedBox(
                  width: 6,
                ),
                Expanded(
                  child: Container(
                    height: 8,
                    // width: 80,
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        color: Color(0xFF41C0FF)),
                  ),
                ),
                const SizedBox(
                  width: 6,
                ),
                Expanded(
                  child: Container(
                    height: 8,
                    // widxth: 80,
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        color: Color(0x1F41C0FF)),
                  ),
                ),
              ],
            ),
            Expanded(
              child: ListView(
                children: [
                  const SizedBox(
                    height: 20,
                  ),
                  const Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        "选择你感兴趣的话题",
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w800,
                            color: Color(0xFF061B1F)),
                      ),
                      SizedBox(
                        width: 8,
                      ),
                      Text(
                        "(多选)",
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF646E70)),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  const Text(
                    "我们会根据你的选择推荐学习内容",
                    style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF646E70)),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  FutureBuilder(
                    future: Api.getSystemSceneTopicsClassForUserChoose(),
                    builder: (context, snapshot) {
                      int total = snapshot.data?.data?.length ?? 0;

                      return ListView.separated(
                        separatorBuilder: (context, index) => const SizedBox(
                          height: 10,
                        ),
                        itemBuilder: (context, index) {
                          Data data = snapshot.data!.data![index * 2];
                          Data? data2;
                          if (total > index * 2 + 1) {
                            data2 = snapshot.data!.data![index * 2 + 1];
                          }

                          return IntrinsicHeight(
                            child: Row(
                              children: [
                                Expanded(child: itemWidget(data)),
                                const SizedBox(
                                  width: 10,
                                ),
                                Expanded(child: itemWidget(data2)),
                              ],
                            ),
                          );
                        },
                        itemCount: (total / 2 + total % 2).toInt(),
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(left: 20.0, right: 20, bottom: 46),
        child: Container(
          height: 52,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF98ECEC),
                  Color(0xFF84E9FF),
                  Color(0xFF9BE1FF)
                ]),
          ),
          child: TextButton(
              onPressed: () async {
                if (type.isEmpty) {
                  EasyLoading.showToast("至少选择一项");
                  return;
                }
                EasyLoading.show();
                await Api.setUserInterestTopics({
                  "topic_codes": type,
                }).then((value) {
                  EasyLoading.dismiss();
                  Navigator.of(context).pushNamed("/setup_setting_four");
                });
              },
              style: const ButtonStyle(
                textStyle: MaterialStatePropertyAll(
                    TextStyle(fontSize: 20, fontWeight: FontWeight.w800)),
              ),
              child: const Center(
                child: Text("下一步",
                    style: TextStyle(
                        color: Color(0xFF061B1F),
                        fontSize: 18,
                        fontWeight: FontWeight.w800)),
              )),
        ),
      ),
    );
  }

  Widget itemWidget(Data? data) {
    if (data == null) {
      return const Spacer();
    }

    return Container(
      decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(12)),
          color: Colors.white,
          border: Border.all(color: const Color(0xff41C0FF), width: 1)),
      padding: const EdgeInsets.all(16),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          setState(() {
            if (type.contains(data!.code)) {
              type.remove(data.code);
            } else {
              type.add(data.code!);
            }
          });
        },
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    data.name ?? "",
                    style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w800,
                        color: Color(0xFF061B1F)),
                  ),
                  Text(
                    data.description ?? "",
                    style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF646E70)),
                  ),
                ],
              ),
            ),
            Image.asset(
              type.contains(data.code)
                  ? "images/check_circle.png"
                  : "images/un_check_circle.png",
              width: 18,
              height: 18,
            )
          ],
        ),
      ),
    );
  }
}
