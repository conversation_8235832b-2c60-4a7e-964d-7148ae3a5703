import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/purpuse_model/data.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class SetupSettingFour extends StatefulWidget {
  const SetupSettingFour({super.key});
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const SetupSettingFour());
  }

  @override
  State<SetupSettingFour> createState() => _SetupSettingFourState();
}

class _SetupSettingFourState extends State<SetupSettingFour> {
  int sex = 2; //1 男 2女
  String? iconUrl;
  String? selectName;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leadingWidth: 300,
        backgroundColor: const Color(0xFFF3FBFD),
        leading: CustomAppbar.leftWidget(context, text: "学习偏好设置"),
        // actions: [
        //   GestureDetector(
        //     onTap: () {
        //       Navigator.of(context)
        //           .pushNamedAndRemoveUntil("/home", (route) => false);
        //     },
        //     child: const Text(
        //       "跳过",
        //       style: TextStyle(
        //           fontSize: 14,
        //           fontWeight: FontWeight.w400,
        //           color: Color(0xFF646E70)),
        //     ),
        //   ),
        //   const SizedBox(
        //     width: 12,
        //   )
        // ],
      ),
      backgroundColor: const Color(0xFFF3FBFD),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(20, 20, 20, 130),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 8,
                    // width: 80,
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        color: Color(0xFF41C0FF)),
                  ),
                ),
                const SizedBox(
                  width: 6,
                ),
                Expanded(
                  child: Container(
                    height: 8,
                    // width: 80,
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        color: Color(0xFF41C0FF)),
                  ),
                ),
                const SizedBox(
                  width: 6,
                ),
                Expanded(
                  child: Container(
                    height: 8,
                    // width: 80,
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        color: Color(0xFF41C0FF)),
                  ),
                ),
                const SizedBox(
                  width: 6,
                ),
                Expanded(
                  child: Container(
                    height: 8,
                    // widxth: 80,
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                        color: Color(0xFF41C0FF)),
                  ),
                ),
              ],
            ),
            Expanded(
              child: ListView(
                children: [
                  const SizedBox(
                    height: 20,
                  ),
                  const Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        "选择你的个人信息",
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w800,
                            color: Color(0xFF061B1F)),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: sexItem(2),
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      Expanded(
                        child: sexItem(1),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  const Text(
                    '选择你喜欢的头像',
                    style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: Color(0xff646E70)),
                  ),
                  FutureBuilder(
                      future: Api.getSystemProfilePictureList({"gender": sex}),
                      builder: (context, snapshot) {
                        List<String> icons = snapshot.data ?? [];
                        if (icons.isEmpty) {
                          return const SizedBox(
                            height: 20,
                            width: 20,
                          );
                        }
                        // iconUrl ??= snapshot.data?.first;
                        int size = icons.length;
                        return ListView.builder(
                          itemBuilder: (context, index) {
                            return Row(
                              children: [
                                for (int i = 0; i < 5; i++)
                                  if (size > index * 5 + i)
                                    Expanded(
                                        child: Padding(
                                      padding: const EdgeInsets.only(
                                          left: 5,
                                          right: 5.0,
                                          top: 12,
                                          bottom: 0),
                                      child: iconWidget(icons[(index * 5 + i)]),
                                    ))
                                  else
                                    Expanded(child: iconWidget(null))
                              ],
                            );
                          },
                          itemCount: ((size ~/ 5) + (size % 5 > 0 ? 1 : 0)),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                        );
                      }),
                  const SizedBox(
                    height: 12,
                  ),
                  const Text(
                    '选择你喜欢的昵称',
                    style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: Color(0xff646E70)),
                  ),
                  FutureBuilder(
                      future: Api.getNickNameList({"gender": sex}),
                      builder: (context, snapshot) {
                        List<String> names = snapshot.data ?? [];
                        // selectName ??= snapshot.data?.first;
                        if (names.isEmpty) {
                          return const SizedBox(
                            height: 20,
                            width: 20,
                          );
                        }
                        int size = names.length;
                        return ListView.builder(
                          itemBuilder: (context, index) {
                            return Row(
                              children: [
                                for (int i = 0; i < 3; i++)
                                  if (size > index * 3 + i)
                                    Expanded(
                                        child: Padding(
                                      padding: const EdgeInsets.all(6),
                                      child: nameWidget(names[(index * 3 + i)]),
                                    ))
                                  else
                                    Expanded(child: nameWidget(null))
                              ],
                            );
                          },
                          itemCount: ((size ~/ 3) + (size % 3 > 0 ? 1 : 0)),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                        );
                      }),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(left: 20.0, right: 20, bottom: 46),
        child: Container(
          height: 52,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF98ECEC),
                  Color(0xFF84E9FF),
                  Color(0xFF9BE1FF)
                ]),
          ),
          child: TextButton(
              onPressed: () async {
                if (iconUrl == null) {
                  EasyLoading.showToast("请选择头像");
                  return;
                }
                if (selectName == null) {
                  EasyLoading.showToast("请选择昵称");
                  return;
                }
                EasyLoading.show();

                await Api.setUserGender({
                  "gender": sex,
                });
                await Api.setNickname({
                  "nickname": selectName,
                });
                await Api.setProfilePicture({
                  "profile_picture": iconUrl,
                });
                EasyLoading.dismiss();
                if (context.mounted) {
                  Navigator.of(context)
                      .pushNamedAndRemoveUntil("/home", (route) => false);
                }
              },
              style: const ButtonStyle(
                textStyle: MaterialStatePropertyAll(
                    TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
              ),
              child: const Center(
                child: Text("完成",
                    style: TextStyle(
                        color: Color(0xFF061B1F),
                        fontSize: 18,
                        fontWeight: FontWeight.w800)),
              )),
        ),
      ),
    );
  }

  Widget iconWidget(String? url) {
    if (url == null) {
      return const AspectRatio(
        aspectRatio: 1,
      );
    }
    return GestureDetector(
      onTap: () {
        setState(() {
          iconUrl = url;
        });
      },
      child: AspectRatio(
        aspectRatio: 1,
        child: Stack(children: [
          Positioned.fill(
              child: Image.network(
            url,
            fit: BoxFit.fill,
          )),
          if (iconUrl == url)
            Positioned.fill(
                child: Image.asset(
              "images/chose_circle.png",
              fit: BoxFit.fill,
            )),
          if (iconUrl == url)
            Positioned(
              right: 0,
              bottom: 0,
              child: Image.asset(
                "images/check_circle.png",
                width: 18,
                height: 18,
              ),
            )
        ]),
      ),
    );
  }

  Widget nameWidget(String? name) {
    if (name == null) {
      return const SizedBox(
        height: 44,
      );
    }
    return GestureDetector(
      onTap: () {
        setState(() {
          selectName = name;
        });
      },
      child: Container(
          decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(
                  width: 1,
                  color: selectName == name
                      ? const Color(0xFF41C0FF)
                      : Colors.white),
              borderRadius: const BorderRadius.all(Radius.circular(12))),
          height: 44,
          child: Center(
              child: Text(
            name,
            style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Color(0xFF061B1F)),
          ))),
    );
  }

  Container sexItem(int type) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(12)),
          color: Colors.white,
          border: Border.all(
              color: sex == type ? const Color(0xff41C0FF) : Colors.white,
              width: 1)),
      padding: const EdgeInsets.all(16),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          setState(() {
            if (sex != type) {
              //切换性别，清空名字和头像选择
              selectName = null;
              iconUrl = null;
            }

            sex = type;
          });
        },
        child: Row(
          children: [
            const SizedBox(
              width: 12,
            ),
            Image.asset(
              type == 1 ? "images/men.png" : "images/women.png",
              width: 24,
              height: 24,
            ),
            const SizedBox(
              width: 10,
            ),
            Text(
              type == 2 ? '我是女生' : '我是男生',
              style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: Color(0xff061B1F)),
            )
          ],
        ),
      ),
    );
  }
}
