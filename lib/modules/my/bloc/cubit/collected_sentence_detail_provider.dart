import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/audio_upload_model/audio_upload_model.dart';
import 'package:flutter_app_kouyu/common/http/models/collection_sentence_detail/collection_sentence_detail.dart';
import 'package:flutter_app_kouyu/common/http/models/collection_vocabulary_detail/collection_vocabulary_detail.dart';
import 'package:flutter_app_kouyu/common/http/models/scene_topics_vocabulary_model/list.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';
import 'package:flutter_app_kouyu/common/http/models/vocabulary_soe_model/vocabulary_soe_model.dart';
import 'package:flutter_app_kouyu/common/provider/voice_model.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';

class CollectedSentenceDetailProvider extends VoiceModel {
  VoiceStateEnum voiceState = VoiceStateEnum.init;

  final String sentence;
  final String collectId;
  CollectionSentenceDetail? sentenceDetail;
  SoeModel? soeModel;
  CollectedSentenceDetailProvider(
      {required this.sentence, required this.collectId});

  refreshData() async {
    sentenceDetail = await Api.getSentenceDetail(sentence, collectId);
    notifyListeners();
  }

  Future<bool> sent() async {
    await super.stop();
    notifyListeners();

    //上传文件
    AudioUploadModel audioUploadModel =
        await Api.uploadFile(filePath!, topicCode: "", scene: "word_learning");
    //soe
    soeModel = await Api.soe({
      "text": sentence,
      "audio_url": audioUploadModel.data?.fileUrl ?? "",
      "client_sentence_id": 0,
      "conversation_id": 0,
      "scene": "",
    });
    if (soeModel?.data?.suggestedScore != null) {
      sentenceDetail?.data?.score = "${soeModel?.data?.suggestedScore}";
      sentenceDetail?.data?.userPronunciationUrl =
          audioUploadModel.data?.fileUrl;
    }

    notifyListeners();

    return true;
  }
}
