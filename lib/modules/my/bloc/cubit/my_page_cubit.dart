import 'package:equatable/equatable.dart';
import 'package:flutter_app_kouyu/common/http/models/user_info_model/user_info_model.dart';
import 'package:flutter_app_kouyu/repository/user_info_reponsitory.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'my_page_state.dart';

class MyPageCubit extends Cubit<MyPageState> {
  final UserInfoReponsitory userInfoResponsitory;
  MyPageCubit({required this.userInfoResponsitory})
      : super(const MyPageState());

  Future<void> initUserInfo() async {
    UserInfoModel? userInfoModel = await userInfoResponsitory.getUserInfo();
    return emit(state.copywith(userInfoModel));
  }
}
