import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/audio_upload_model/audio_upload_model.dart';
import 'package:flutter_app_kouyu/common/http/models/collection_vocabulary_detail/collection_vocabulary_detail.dart';
import 'package:flutter_app_kouyu/common/http/models/scene_topics_vocabulary_model/list.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';
import 'package:flutter_app_kouyu/common/http/models/vocabulary_soe_model/vocabulary_soe_model.dart';
import 'package:flutter_app_kouyu/common/provider/voice_model.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';

class CollectedVocabularyDetailProvider extends VoiceModel {
  VoiceStateEnum voiceState = VoiceStateEnum.init;

  final String vocabulary;
  CollectionVocabularyDetail? vocabularyDetail;
  CollectedVocabularyDetailProvider({required this.vocabulary});
  SoeModel? soeModel;

  refreshData() async {
    vocabularyDetail = await Api.getVocabularyDetail(vocabulary);
    notifyListeners();
  }

  Future<bool> sent() async {
    await super.stop();
    notifyListeners();

    //上传文件
    AudioUploadModel audioUploadModel =
        await Api.uploadFile(filePath!, topicCode: "", scene: "word_learning");
    //soe
    //soe
    soeModel = await Api.soe({
      "text": vocabulary,
      "audio_url": audioUploadModel.data?.fileUrl ?? "",
      "client_sentence_id": 0,
      "conversation_id": 0,
      "scene": "",
    });
    if (soeModel?.data?.suggestedScore != null) {
      vocabularyDetail?.data?.score = "${soeModel?.data?.suggestedScore}";
      vocabularyDetail?.data?.userPronunciationUrl =
          audioUploadModel.data?.fileUrl;
    }

    notifyListeners();

    return true;
  }
}
