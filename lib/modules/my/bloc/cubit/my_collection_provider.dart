// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_sentence/my_collection_sentence.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_sentence/sentence_list.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_vocabulary/my_collection_vocabulary_model.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_vocabulary/vocabulary_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_phrasal_verb/my_collection_phrasal_verb.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_phrasal_verb/phrasal_verb_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_collocation/collocation_response_model.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_collocation/collocation_list_model.dart';
import 'package:flutter_app_kouyu/modules/my/pages/my_collection_page.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

enum SortEnum {
  vocabulary,
  data;
}

extension SortEnumExtension on SortEnum {
  title() {
    switch (this) {
      case SortEnum.vocabulary:
        return "字母排序";
      case SortEnum.data:
        return "时间排序";
    }
  }

  type() {
    switch (this) {
      case SortEnum.vocabulary:
        return "vocabulary";
      case SortEnum.data:
        return "date";
    }
  }
}

class MyCollectionProvider extends ChangeNotifier {
  MyCollectionVocabulary? vocabulary;
  MyCollectionSentence? sentence;
  MyCollectionPhrasalVerb? phrasalVerb;
  MyCollectionCollocation? collocation;
  SortEnum _sortEnum = SortEnum.vocabulary;
  RefreshController refreshController = RefreshController();
  final int initPageNum = 1;
  final int pageSize = 20;

  CollectionType _type = CollectionType.word;
  set type(CollectionType type) {
    _type = type;
    refreshData();
    notifyListeners();
  }

  SortEnum get sortEnum => _sortEnum;

  set sortEnum(SortEnum sortEnum) {
    _sortEnum = sortEnum;
    refreshData();
    notifyListeners();
  }

  CollectionType get type => _type;

  refreshData() async {
    if (_type == CollectionType.word) {
      try {
        vocabulary = await Api.getMyCollectVocabularyList(
            initPageNum, pageSize, sortEnum.type());
      } catch (e) {
        refreshController.refreshFailed();
      } finally {
        refreshController.refreshCompleted();
        notifyListeners();
      }
      return;
    }
    if (_type == CollectionType.sentence) {
      try {
        sentence = await Api.getMyCollectSentenceList(
            initPageNum, pageSize, sortEnum.type());
      } catch (e) {
        refreshController.refreshFailed();
      } finally {
        refreshController.refreshCompleted();
        notifyListeners();
      }
    }
    if (_type == CollectionType.phrasal_verb) {
      try {
        phrasalVerb = await Api.getMyCollectPhrasalVerbList(
            initPageNum, pageSize, sortEnum.type());
      } catch (e) {
        refreshController.refreshFailed();
      } finally {
        refreshController.refreshCompleted();
        notifyListeners();
      }
    }
    if (_type == CollectionType.collocation) {
      try {
        collocation = await Api.getMyCollectCollocationList(
            initPageNum, pageSize, sortEnum.type());
      } catch (e) {
        refreshController.refreshFailed();
      } finally {
        refreshController.refreshCompleted();
        notifyListeners();
      }
    }
  }

  loadMore() async {
    if (_type == CollectionType.word) {
      loadMoreWords();
      return;
    }
    if (_type == CollectionType.sentence) {
      loadMoreSentence();
    }
    if (_type == CollectionType.phrasal_verb) {
      loadMorePhrasalVerb();
    }
    if (_type == CollectionType.collocation) {
      loadMoreCollocation();
    }
  }

  loadMoreWords() async {
    try {
      MyCollectionVocabulary value = await Api.getMyCollectVocabularyList(
          vocabulary!.data!.pageNum! + 1, pageSize, sortEnum.type());
      _appendWord(value);
    } catch (e) {
      refreshController.loadComplete();
    } finally {
      refreshController.loadComplete();
      notifyListeners();
    }
  }

  loadMoreSentence() async {
    try {
      MyCollectionSentence value = await Api.getMyCollectSentenceList(
          vocabulary!.data!.pageNum! + 1, pageSize, sortEnum.type());
      _appendSentence(value);
    } catch (e) {
      refreshController.loadComplete();
    } finally {
      refreshController.loadComplete();
      notifyListeners();
    }
  }

  loadMorePhrasalVerb() async {
    try {
      MyCollectionPhrasalVerb value = await Api.getMyCollectPhrasalVerbList(
          phrasalVerb!.data!.pageNum! + 1, pageSize, sortEnum.type());
      _appendPhrasalVerb(value);
    } catch (e) {
      refreshController.loadComplete();
    } finally {
      refreshController.loadComplete();
      notifyListeners();
    }
  }

  loadMoreCollocation() async {
    try {
      MyCollectionCollocation value = await Api.getMyCollectCollocationList(
          collocation!.data!.pageNum! + 1, pageSize, sortEnum.type());
      _appendCollocation(value);
    } catch (e) {
      refreshController.loadComplete();
    } finally {
      refreshController.loadComplete();
      notifyListeners();
    }
  }

  void _appendWord(MyCollectionVocabulary model) {
    if (vocabulary == null) {
      vocabulary = model;
      return;
    }
    if (model.data?.vocabularys?.isEmpty == true) {
      return;
    }
    vocabulary!.data!.pageNum = model.data!.pageNum;
    vocabulary!.data!.pageSize = model.data!.pageSize;
    if (vocabulary!.data!.vocabularys!.last.groupBy ==
        model.data!.vocabularys!.first.groupBy) {
      vocabulary!.data!.vocabularys!.last.vocabularyList!
          .addAll(model.data?.vocabularys?.first.vocabularyList ?? []);
      model.data?.vocabularys?.removeAt(0);
    }
    vocabulary!.data!.vocabularys!.addAll(model.data?.vocabularys ?? []);
  }

  void _appendSentence(MyCollectionSentence model) {
    if (sentence == null) {
      sentence = model;
      return;
    }
    if (model.data?.sentences?.isEmpty == true) {
      return;
    }
    vocabulary!.data!.pageNum = model.data!.pageNum;
    vocabulary!.data!.pageSize = model.data!.pageSize;
    if (vocabulary!.data!.vocabularys!.last.groupBy ==
        model.data!.sentences!.first.groupBy) {
      sentence!.data!.sentences!.last.sentenceList!
          .addAll(model.data?.sentences?.first.sentenceList ?? []);
      model.data?.sentences?.removeAt(0);
    }
    sentence!.data!.sentences!.addAll(model.data?.sentences ?? []);
  }

  void _appendPhrasalVerb(MyCollectionPhrasalVerb model) {
    if (phrasalVerb == null) {
      phrasalVerb = model;
      return;
    }
    if (model.data?.phrasalVerbList?.isEmpty == true) {
      return;
    }
    phrasalVerb!.data!.pageNum = model.data!.pageNum;
    phrasalVerb!.data!.pageSize = model.data!.pageSize;
    if (phrasalVerb!.data!.phrasalVerbList!.last.groupBy ==
        model.data!.phrasalVerbList!.first.groupBy) {
      phrasalVerb!.data!.phrasalVerbList!.last.phrasalVerbList!
          .addAll(model.data?.phrasalVerbList?.first.phrasalVerbList ?? []);
      model.data?.phrasalVerbList?.removeAt(0);
    }
    phrasalVerb!.data!.phrasalVerbList!.addAll(model.data?.phrasalVerbList ?? []);
  }

  void _appendCollocation(MyCollectionCollocation model) {
    if (collocation == null) {
      collocation = model;
      return;
    }
    if (model.data?.collocationList?.isEmpty == true) {
      return;
    }
    collocation!.data!.pageNum = model.data!.pageNum;
    collocation!.data!.pageSize = model.data!.pageSize;
    if (collocation!.data!.collocationList!.last.groupBy ==
        model.data!.collocationList!.first.groupBy) {
      collocation!.data!.collocationList!.last.collocationList!
          .addAll(model.data?.collocationList?.first.collocationList ?? []);
      model.data?.collocationList?.removeAt(0);
    }
    collocation!.data!.collocationList!.addAll(model.data?.collocationList ?? []);
  }

  cancelCollectionSentence(SentenceList sentence) async {
    await Api.cancelCollectionSentence(sentence.sentence);
    notifyListeners();
  }

  cancelCollectVocabulary(VocabularyList vocabulary) async {
    await Api.cancelCollectVocabulary(vocabulary.vocabulary);
    notifyListeners();
  }

  cancelCollectPhrasalVerb(PhrasalVerbList phrasalVerb) async {
    await Api.cancelCollectPhrasalVerb(phrasalVerb.id);
    notifyListeners();
  }

  cancelCollectCollocation(CollocationList collocation) async {
    await Api.cancelCollectCollocation(collocation.id);
    notifyListeners();
  }

  collectPhrasalVerb(PhrasalVerbList phrasalVerb) async {
    await Api.collectPhrasalVerb(phrasalVerb.id);
    notifyListeners();
  }
  
  collectCollocation(CollocationList collocation) async {
    await Api.collectCollocation(collocation.id);
    notifyListeners();
  }
}
