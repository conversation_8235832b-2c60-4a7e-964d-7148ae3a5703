import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/core_vocabulary_model/core_vocabulary_model.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';
import 'package:flutter_app_kouyu/common/http/models/core_vocabulary_model/datum.dart'
    as coreData;

class HighFrequencyVocabularyProvider extends ChangeNotifier {
  final FreeTalkRepository freeTalkRepository = FreeTalkRepository();

  CoreVocabularyModel? vocabulary;
  final String conversationId;
  HighFrequencyVocabularyProvider(this.conversationId) {
    _requestData();
  }

  _requestData() {
    Api.getUserChatCoreVocabularies(conversationId).then((value) {
      vocabulary = value;
      notifyListeners();
    });
  }

  collection(coreData.Datum data) async {
    await freeTalkRepository.collectVocabulary(data.vocabulary);
    data.collected = true;
    notifyListeners();
  }

  cancelCollection(coreData.Datum data) async {
    await freeTalkRepository.cancelCollectVocabulary(data.vocabulary);
    data.collected = false;
    notifyListeners();
  }
}
