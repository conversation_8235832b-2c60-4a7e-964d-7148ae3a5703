import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/en_to_ch_model.dart';
import 'package:flutter_app_kouyu/common/provider/voice_model.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';

class SentenceAlertProvider extends VoiceModel {
  final FreeTalkRepository freeTalkRepository = FreeTalkRepository();
  late final bool showTranslate;
  final String sentence;
  bool isCollect = false;
  EnToCnModel? enToCnModel;
  SentenceAlertProvider({required this.sentence, required this.showTranslate});

  refreshData() async {
    if (showTranslate) {
      enToCnModel = await translate(sentence);
      notifyListeners();
    }
    CommonUtils.playTts(sentence, key: sentence);
  }

  Future<EnToCnModel> translate(String? text) async {
    return Api.translateToCH(text);
  }

  void collection() async {
    if (isCollect == true) {
      await freeTalkRepository.cancelCollectionSentence(sentence);
      isCollect = false;
      notifyListeners();
      return;
    } else {
      await freeTalkRepository.collectionSentence(
          sentence, "conversation_history", "0");
      isCollect = true;
      notifyListeners();
      return;
    }
  }
}
