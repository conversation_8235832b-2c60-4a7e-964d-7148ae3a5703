// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/report_list_model/report_list_model.dart';
import 'package:flutter_app_kouyu/modules/my/pages/report_record_page.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class IeltsSpeakingRecordProvider extends ChangeNotifier {
  ReportListModel? reportListModel;
  RefreshController refreshController = RefreshController();
  final int initPageNum = 1;
  final int pageSize = 20;

  ReportType _type = ReportType.all;
  set type(ReportType type) {
    _type = type;
    refreshData();
    notifyListeners();
  }

  ReportType get type => _type;

  refreshData() async {
    try {
      reportListModel = await Api.queryIeltsSpeakingPage(initPageNum, pageSize);
    } catch (e) {
      refreshController.refreshFailed();
    } finally {
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }

  loadMore() async {
    try {
      ReportListModel value = await Api.queryIeltsSpeakingPage(
          reportListModel!.data!.pageNum! + 1, pageSize);

      _append(value);
    } catch (e) {
      refreshController.loadComplete();
    } finally {
      refreshController.loadComplete();
      notifyListeners();
    }
  }

  void _append(ReportListModel model) {
    if (reportListModel == null) {
      reportListModel = model;
      return;
    }
    if (model.data?.reportList?.isEmpty == true) {
      return;
    }
    reportListModel!.data!.pageNum = model.data!.pageNum;
    reportListModel!.data!.pageSize = model.data!.pageSize;
    if (reportListModel!.data!.reportList!.last.groupBy ==
        model.data!.reportList!.first.groupBy) {
      reportListModel!.data!.reportList!.last.list!
          .addAll(model.data?.reportList?.first.list ?? []);
      model.data?.reportList?.removeAt(0);
    }
    reportListModel!.data!.reportList!.addAll(model.data?.reportList ?? []);
  }
}
