import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';

class AppNavigator extends NavigatorObserver {
  //是否跳转到过首页
  static bool containHome = false;
  @override
  void didPop(Route route, Route? previousRoute) {
    super.didPop(route, previousRoute);
    //停止播放
    if (route.settings.arguments is Map) {
      Map map = route.settings.arguments as Map;
      if (map["stopPlay"] == false) {
        //特殊情况不停止播放
        return;
      }
    }
    CommonUtils.stopPlay();
  }
}
