import 'dart:async';

import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

enum InAppPurchaseStep {
  // 等待中
  pending,
  // 购买成功
  purchased,
  // 验单成功
  verified,
  // 错误
  error,
  // 恢复购买
  restored,
  // 已取消
  canceled;
}

abstract class InAppPurchaseServiceInterface {
  /*
  * 购买状态监听回调
  * */
  void listenToPurchaseUpdated(InAppPurchaseStep status);
  /*
  * 验单接口
  * */
  Future<bool> verifyPurchase(PurchaseDetails details);
}

class InAppPurchaseService {
  InAppPurchaseService(this.iapServiceImpl);

  InAppPurchaseServiceInterface iapServiceImpl;
  StreamSubscription<List<PurchaseDetails>>? _subscription;
  final List<ProductDetails> _products = [];
  Completer<bool>? _buySignal;
  InAppPurchaseStep? _lastStatus;

  /*
  * 2.登录后调用监听
  * */
  void listen() {
    LogUtil.v("listem", tag: "purchaselist");

    _subscription ??= InAppPurchase.instance.purchaseStream.listen(
      _listenToPurchaseUpdated,
    );
  }

  /*
  * 需要取消监听,退出登录时调用
  * */
  void cancel() {
    _subscription?.cancel();
    _subscription = null;
  }

  /*
  * 确定苹果购买服务可不可用
  * */
  Future<bool> checkAvailable() async {
    final bool available = await InAppPurchase.instance.isAvailable();
    return available;
  }

  /*
  * 开启购买
  * */
  Future<bool> buy(String priceId) async {
    try {
      handlerLoadingCallBack(InAppPurchaseStep.pending);
      final isAvailable = await checkAvailable();
      if (!isAvailable) {
        throw Exception('服务不可用');
      }
      _iapLog('内购服务可用');
      // 1.商品详情
      final detail = await _queryProductDetails(priceId);
      if (detail == null) {
        throw Exception('商品不存在');
      }
      _iapLog('获取到商品详情信息');
      // 2.购买参
      final purchaseParam = PurchaseParam(productDetails: detail);

      // 3.购买
      final res = await InAppPurchase.instance
          .buyConsumable(purchaseParam: purchaseParam);
      _iapLog('购买结果: $res');

      final fu = Completer<bool>();
      _buySignal = fu;
      return fu.future;
    } catch (e) {
      _iapLog(e);
      handlerLoadingCallBack(InAppPurchaseStep.error);
      return false;
    }
  }

  /*
  * 查询可用商品
  * */
  Future<ProductDetails?> _queryProductDetails(String priceId) async {
    // query local
    for (final pro in _products) {
      if (pro.id == priceId) {
        return pro;
      }
    }
    // query remote
    ProductDetailsResponse res = await InAppPurchase.instance
        .queryProductDetails({priceId}).timeout(const Duration(
      seconds: 15,
    ));
    for (final pro in res.productDetails) {
      if (pro.id == priceId) {
        _products.add(pro);
        return pro;
      }
    }
    // find null
    return null;
  }

  void _listenToPurchaseUpdated(List<PurchaseDetails> purchaseDetailsList) {
    LogUtil.v(purchaseDetailsList.length, tag: "purchaselist");

    purchaseDetailsList.forEach((PurchaseDetails purchaseDetails) async {
      LogUtil.v(purchaseDetails.productID, tag: "purchase");
      switch (purchaseDetails.status) {
        case PurchaseStatus.pending:
          handlerLoadingCallBack(InAppPurchaseStep.pending);
          break;
        case PurchaseStatus.purchased:
          try {
            handlerLoadingCallBack(InAppPurchaseStep.purchased);
            // 验单结束
            final verifyResult =
                await iapServiceImpl.verifyPurchase(purchaseDetails);
            if (verifyResult) {
              _buySignal?.complete(true);

              handlerLoadingCallBack(InAppPurchaseStep.verified);
              // 完成订单
              await _finishDetails(purchaseDetails, true);
            } else {
              LogUtil.v("验签失败", tag: "purchase");

              // 发送失败
              _buySignal?.complete(false);
              // 状态更新
              handlerLoadingCallBack(InAppPurchaseStep.error);
              // 完成订单
              await _finishDetails(purchaseDetails, true);
            }
          } catch (e) {
            LogUtil.v("验签异常", tag: "purchase");

            _buySignal?.complete(false);

            handlerLoadingCallBack(InAppPurchaseStep.error);
            // 完成订单
            await _finishDetails(purchaseDetails, false);
          }
          break;
        case PurchaseStatus.error:
          await _finishDetails(purchaseDetails, true);
          handlerLoadingCallBack(InAppPurchaseStep.error);
          break;
        case PurchaseStatus.restored:
          await _finishDetails(purchaseDetails, true);
          handlerLoadingCallBack(InAppPurchaseStep.restored);
          break;
        case PurchaseStatus.canceled:
          await _finishDetails(purchaseDetails, true);
          handlerLoadingCallBack(InAppPurchaseStep.canceled);
          break;
      }
      _iapLog(
        '订单状态: ${purchaseDetails.status.name} 是否需要完成订单: ${purchaseDetails.pendingCompletePurchase}',
      );
    });
  }

  /*
  * 购买各状态回调
  * */
  void handlerLoadingCallBack(InAppPurchaseStep status) {
    if (status != _lastStatus) {
      iapServiceImpl.listenToPurchaseUpdated(status);
      _lastStatus = status;
    }
  }

  Future<void> _finishDetails(PurchaseDetails details,
      [bool force = false]) async {
    // 完成订单
    if (details.pendingCompletePurchase || force) {
      await InAppPurchase.instance.completePurchase(details);
    }
  }
}

void _iapLog(dynamic m) {
  debugPrint('内购🥚🥚🥚 $m');
}
