import 'package:common_utils/common_utils.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:flutter_app_kouyu/my_app.dart';

class InAppPurchaseServiceImpl extends InAppPurchaseServiceInterface {
  @override
  void listenToPurchaseUpdated(InAppPurchaseStep status) {
    inAppPurchaseCubit.inAppPurchaseStepUpdate(status);
    LogUtil.v(status, tag: "内购状态更新");
  }

  @override
  Future<bool> verifyPurchase(PurchaseDetails details) async {
    LogUtil.v("details:${details.productID}", tag: "内购开始验签");

    return Api.verifyIosReceipt({"receipt_data": details.toMap()});
  }
}

extension on PurchaseDetails {
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'purchaseID': purchaseID,
      'productID': productID,
      'verificationData': verificationData.toMap(),
      'transactionDate': transactionDate,
    };
  }
}

extension on PurchaseVerificationData {
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'localVerificationData': localVerificationData,
      'serverVerificationData': serverVerificationData,
      'source': source,
    };
  }
}
