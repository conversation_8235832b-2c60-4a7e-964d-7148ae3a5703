// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'in_app_purchase_cubit.dart';

class InAppPurchaseState extends Equatable {
  const InAppPurchaseState({this.status});
  final InAppPurchaseStep? status;

  @override
  List<Object?> get props => [status];

  InAppPurchaseState copyWith({
    InAppPurchaseStep? status,
  }) {
    return InAppPurchaseState(
      status: status ?? this.status,
    );
  }
}
