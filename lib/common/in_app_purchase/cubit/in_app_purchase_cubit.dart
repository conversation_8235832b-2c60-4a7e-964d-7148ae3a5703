import 'package:common_utils/common_utils.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_app_kouyu/common/in_app_purchase/In_app_purchase_service_impl.dart';
import 'package:flutter_app_kouyu/common/in_app_purchase/in_app_purchase.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'in_app_purchase_state.dart';

class InAppPurchaseCubit extends Cubit<InAppPurchaseState> {
  InAppPurchaseCubit() : super(const InAppPurchaseState());

  final _inAppPurchaseService =
      InAppPurchaseService(InAppPurchaseServiceImpl());

  Future<void> inAppPurchaseStepUpdate(InAppPurchaseStep step) async {
    return emit(state.copyWith(status: step));
  }

  Future<bool> buy(String priceId) async {
    return _inAppPurchaseService.buy(priceId);
  }

  void listen() {
    LogUtil.v("listen...", tag: "purchase");
    _inAppPurchaseService.listen();
  }
}
