import 'package:asr_plugin/asr_plugin.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/system_secret_model/system_secret_model.dart';

class AsrUtil {
  static ASRControllerConfig? _config;

  static Future<ASRControllerConfig> config() async {
    if (_config != null) {
      return Future.value(_config);
    }
    SystemSecretModel model = await Api.getASRSystemSecret();
    ASRControllerConfig config = ASRControllerConfig()
      ..is_save_audio_file = true
      ..engine_model_type = "16k_zh-PY"
      ..appID = model.data?.appId ?? 0
      ..secretID = model.data?.secretId ?? ""
      ..secretKey = model.data?.secretKey ?? "";
    _config = config;
    return Future.value(_config);
  }
}
