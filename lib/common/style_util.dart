import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

Color color_1 = const Color(0xFF061B1F);
Color color_2 = const Color(0xFF646E70);
Color color_3 = const Color(0xFFA7D3DB);
Color separated = const Color(0xFFDDEDF0);
Color bgColor = const Color(0xFFF0F8FA);
Color greenColor = const Color(0xFF00B578);
Color orangeColor = const Color(0xFFFF8F1F);
Color redColor = const Color(0xFFFA5151);
Color btnColor = const Color(0xFFF0F8FA);
Color blue = const Color(0xFF41C0FF);
Color color_4 = const Color(0xFF4E5766);
Color color_5 = const Color(0xFFB8CDDB);

TextStyle tsBar =
    TextStyle(color: color_1, fontSize: 36.sp, fontWeight: FontWeight.w700);

TextStyle style_1_36 =
    TextStyle(color: color_1, fontSize: 36.sp, fontWeight: FontWeight.w700);

TextStyle style_1_40 =
    TextStyle(color: color_1, fontSize: 36.sp, fontWeight: FontWeight.w700);

TextStyle style_1_48 =
    TextStyle(color: color_1, fontSize: 48.sp, fontWeight: FontWeight.w700);

TextStyle style_1_32 =
    TextStyle(color: color_1, fontSize: 32.sp, fontWeight: FontWeight.w700);

TextStyle style_1_32_400 =
    TextStyle(color: color_1, fontSize: 32.sp, fontWeight: FontWeight.w400);

TextStyle style_1_28 =
    TextStyle(color: color_1, fontSize: 28.sp, fontWeight: FontWeight.w700);

TextStyle style_1_28_500 =
    TextStyle(color: color_1, fontSize: 28.sp, fontWeight: FontWeight.w500);

TextStyle style_1_32_500 =
    TextStyle(color: color_1, fontSize: 32.sp, fontWeight: FontWeight.w500);

TextStyle style_1_28_400 =
    TextStyle(color: color_1, fontSize: 28.sp, fontWeight: FontWeight.w400);

TextStyle style_1_24 =
    TextStyle(color: color_1, fontSize: 24.sp, fontWeight: FontWeight.w400);

TextStyle style_bue_24 =
    TextStyle(color: blue, fontSize: 24.sp, fontWeight: FontWeight.w400);
TextStyle style_bue_20 =
    TextStyle(color: blue, fontSize: 20.sp, fontWeight: FontWeight.w400);

TextStyle style_1_16 =
    TextStyle(color: color_1, fontSize: 20.sp, fontWeight: FontWeight.w400);

TextStyle style_1_20 =
    TextStyle(color: color_1, fontSize: 20.sp, fontWeight: FontWeight.w400);

TextStyle style_2_36 =
    TextStyle(color: color_2, fontSize: 36.sp, fontWeight: FontWeight.w700);

TextStyle style_2_34 =
    TextStyle(color: color_2, fontSize: 34.sp, fontWeight: FontWeight.w700);

TextStyle style_2_32 =
    TextStyle(color: color_2, fontSize: 32.sp, fontWeight: FontWeight.w400);

TextStyle style_2_32_500 =
    TextStyle(color: color_2, fontSize: 32.sp, fontWeight: FontWeight.w500);

TextStyle style_2_28 =
    TextStyle(color: color_2, fontSize: 28.sp, fontWeight: FontWeight.w400);

TextStyle style_2_24 =
    TextStyle(color: color_2, fontSize: 20.sp, fontWeight: FontWeight.w400);
TextStyle style_2_20 =
    TextStyle(color: color_2, fontSize: 24.sp, fontWeight: FontWeight.w400);

TextStyle style_green_24 =
    TextStyle(color: greenColor, fontSize: 24.sp, fontWeight: FontWeight.w400);

TextStyle style_3_32 =
    TextStyle(color: color_3, fontSize: 32.sp, fontWeight: FontWeight.w700);

TextStyle style_3_28 =
    TextStyle(color: color_3, fontSize: 28.sp, fontWeight: FontWeight.w700);

TextStyle style_green_40 =
    TextStyle(color: greenColor, fontSize: 40.sp, fontWeight: FontWeight.w700);
TextStyle style_green_64 =
    TextStyle(color: greenColor, fontSize: 64.sp, fontWeight: FontWeight.w700);

TextStyle style_orange_40 =
    TextStyle(color: orangeColor, fontSize: 40.sp, fontWeight: FontWeight.w700);

TextStyle style_orangeColor_28 =
    TextStyle(color: orangeColor, fontSize: 28.sp, fontWeight: FontWeight.w700);

TextStyle style_3_24 =
    TextStyle(color: color_3, fontSize: 24.sp, fontWeight: FontWeight.w400);

TextStyle style_3_20 =
    TextStyle(color: color_3, fontSize: 20.sp, fontWeight: FontWeight.w400);

TextStyle style_1_36_500 =
    TextStyle(color: color_1, fontSize: 36.sp, fontWeight: FontWeight.w500);

TextStyle style_1_40_700 =
    TextStyle(color: color_1, fontSize: 40.sp, fontWeight: FontWeight.w700);

TextStyle style_4_24_400 =
    TextStyle(color: color_4, fontSize: 24.sp, fontWeight: FontWeight.w400);

TextStyle style_4_20_400 =
    TextStyle(color: color_4, fontSize: 20.sp, fontWeight: FontWeight.w400);

TextStyle style_4_28_400 =
    TextStyle(color: color_4, fontSize: 28.sp, fontWeight: FontWeight.w400);

TextStyle style_5_28_400 =
    TextStyle(color: color_5, fontSize: 28.sp, fontWeight: FontWeight.w400);

TextStyle style_1_40_800 =
    TextStyle(color: color_1, fontSize: 40.sp, fontWeight: FontWeight.w800);

TextStyle style_1_32_600 =
    TextStyle(color: color_1, fontSize: 32.sp, fontWeight: FontWeight.w600);
