import 'package:common_utils/common_utils.dart';

enum Env { develop, product }

class EnvConfig {
  static Env env = Env.product;
  static init(Env e) {
    env = e;
    switch (e) {
      case Env.develop:
        //配置log
        LogUtil.init(isDebug: true);
        break;
      default:
    }
  }

  static String baseUrl() {
    switch (env) {
      case Env.develop:
        return "http://chat-test.xinquai.com";
      default:
        return 'https://chat.xinquai.com';
    }
  }

  static String dbName() {
    switch (env) {
      case Env.develop:
        return "talk_database_dev";
      case Env.product:
        return "talk_database_prod";
      default:
        return "talk_database_dev";
    }
  }
}
