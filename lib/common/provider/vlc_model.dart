import 'package:audio_session/audio_session.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/provider/base_model.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/common/utils/log.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';

mixin VLCMixin on BaseModel {
  final VlcPlayerController _videoPlayerController =
      VlcPlayerController.network(
    "",
    hwAcc: HwAcc.full,
    autoPlay: true,
    allowBackgroundPlayback: true,
    options: VlcPlayerOptions(),
  );

  @override
  void dispose() {
    videoPlayerController.dispose();
    super.dispose();
  }

  @override
  init() {
    _initListener();
  }

  VlcPlayerController get videoPlayerController {
    return _videoPlayerController;
  }

  void playStatusChanged(VlcPlayerController controller, PlayState playState) {}

  Future<void> stopPlayVideo() async {
    try {
      if ((await videoPlayerController.isPlaying()) == true) {
        return await videoPlayerController.stop();
      }
    } catch (e) {
      Log.e("Error stopping video: $e");
    }
  }

  Future<void> playVideo() async {
    if ((await videoPlayerController.isPlaying()) == true) {
      return;
    }
    final audioSession = await AudioSession.instance;
    await audioSession.configure(const AudioSessionConfiguration.music());
    await audioSession.setActive(true);
    await videoPlayerController.play();
  }

  Future<void> addPlayVideo(
    String url,
  ) async {
    Log.d("playingState: 添加url$url");
    await videoPlayerController.addAudioFromNetwork(url);
    await videoPlayerController.play();
    // CommonUtils.playVideo(url);
  }

  _initListener() {
    videoPlayerController.addListener(() {
      videoPlayerController.addOnInitListener(() {
        Log.d("videoPlayerController init");
      });
      videoPlayerController.addListener(() {
        Log.d("playingState: ${videoPlayerController.value.playingState}");

        switch (videoPlayerController.value.playingState) {
          case PlayingState.initializing:
            playStatusChanged(videoPlayerController, PlayState.idle);
            return;
          case PlayingState.initialized:
            playStatusChanged(videoPlayerController, PlayState.idle);
            return;
          case PlayingState.buffering:
            playStatusChanged(videoPlayerController, PlayState.buffering);
            return;
          case PlayingState.playing:
            playStatusChanged(videoPlayerController, PlayState.ready);
            return;
          case PlayingState.stopped:
            playStatusChanged(videoPlayerController, PlayState.completed);
            return;
          case PlayingState.ended:
            playStatusChanged(videoPlayerController, PlayState.completed);

            return;
          case PlayingState.error:
            playStatusChanged(videoPlayerController, PlayState.completed);
            return;
          default:
            playStatusChanged(videoPlayerController, PlayState.completed);
        }
      });
    });
  }
}
