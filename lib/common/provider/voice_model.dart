import 'dart:async';
import 'dart:convert';

import 'package:asr_plugin/asr_plugin.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_app_kouyu/common/asr/asr_util.dart';
import 'package:flutter_app_kouyu/common/provider/base_model.dart';
import 'package:flutter_app_kouyu/common/provider/vlc_model.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/modules/chat/bloc/cubit/voice_cubit.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

typedef SuccessCallback = Function({dynamic result, dynamic filepath});

class VoiceModel extends BaseModel {
  VoiceStateEnum voiceState = VoiceStateEnum.init;
  ValueStream<bool> get realFinish => _realFinish.stream;
  String get filePath => _config.audio_file_path;
  ASRController? _controller;
  Timer? _timer;
  final _maxSeconds = 60.0;
  int _seconds = 0;
  BehaviorSubject<bool> _realFinish = BehaviorSubject<bool>.seeded(false);
  String result = "";
  List<String> _sentences = [];
  late ASRControllerConfig _config;

  @override
  void dispose() {
    _timer?.cancel();
    _realFinish.close();
    if (_controller != null) {
      _controller?.release();
    }
    super.dispose();
  }

  Future<void> speak({
    required bool isZh,
    int? silence_detect_duration,
    Function(String result)? cb,
    SuccessCallback? successCallback,
  }) {
    if (isZh == true) {
      return speakZh(
        silence_detect_duration: silence_detect_duration,
        cb: cb,
        successCallback: successCallback,
      );
    } else {
      return speakEn(
        silence_detect_duration: silence_detect_duration,
        cb: cb,
        successCallback: successCallback,
      );
    }
  }

  /// 英文识别
  /// [cb] 识别结果回调, 参数是识别结果
  Future<void> speakEn(
      {int? silence_detect_duration,
      Function(String result)? cb,
      SuccessCallback? successCallback}) async {
    //停止播放声音
    CommonUtils.stopPlay();
    bool access = await _initRecorder();
    if (!access) {
      return;
    }
    startProgress();
    startRecord(
        engineModelType: "16k_en",
        cb: cb,
        successCallback: successCallback,
        silence_detect_duration: silence_detect_duration);
    voiceState = VoiceStateEnum.recording;
    notifyListeners();
  }

  /// 中文识别
  /// [cb] 识别结果回调, 参数是识别结果
  Future<void> speakZh({
    int? silence_detect_duration,
    Function(String result)? cb,
    SuccessCallback? successCallback,
  }) async {
    //停止播放声音
    CommonUtils.stopPlay();
    bool access = await _initRecorder();
    if (!access) {
      return;
    }
    startProgress();
    await startRecord(
        engineModelType: "16k_zh",
        cb: cb,
        successCallback: successCallback,
        silence_detect_duration: silence_detect_duration);
    voiceState = VoiceStateEnum.recording;
    notifyListeners();
  }

  void startProgress() {
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _seconds = _seconds + 1;
      if (_seconds.toDouble() > _maxSeconds) {
        _timer?.cancel();
      }
    });
  }

  @mustCallSuper
  Future<bool> stop() async {
    _timer?.cancel();
    var value = await stopRecording();
    voiceState = VoiceStateEnum.init;
    notifyListeners();
    return value;
  }

  Future<bool> _initRecorder() async {
    bool havePermission = await _checkPermission();
    if (!havePermission) {
      // 授权失败
      EasyLoading.showToast('请开启麦克风权限');
    }
    return havePermission;
  }

  /// 获取/判断权限
  Future<bool> _checkPermission() async {
    final status = await Permission.microphone.status;
    if (!status.isGranted) {
      // 无权限，则请求权限
      PermissionStatus requestStatus = await Permission.microphone.request();
      return requestStatus == PermissionStatus.granted;
    } else {
      return true;
    }
  }

  Future<void> startRecord(
      {String? engineModelType,
      Function(String result)? cb,
      int? silence_detect_duration,
      //录音结束回调
      SuccessCallback? successCallback}) async {
    try {
      LogUtil.d("asr:开始录音", tag: "app");
      if (_controller != null) {
        try {
          await _controller?.release();
        } catch (e) {}
      }
      _config = await AsrUtil.config();
      if (silence_detect_duration != null) {
        _config.silence_detect_duration = silence_detect_duration;
        _config.silence_detect = true;
      } else {
        _config.silence_detect = false;
      }
      _config.audio_file_path =
          "${(await getTemporaryDirectory()).absolute.path}/temp.wav";
      if (engineModelType != null) {
        _config.engine_model_type = engineModelType;
      } else {
        _config.engine_model_type = "16k_zh";
      }
      _sentences = [];
      _realFinish.close;
      _realFinish = BehaviorSubject<bool>.seeded(false);
      LogUtil.d("engine_model_type:${_config.engine_model_type}");
      _controller = await _config.build();
      Stream<ASRData> asrStream = _controller!.recognize();
      await for (final val in asrStream) {
        LogUtil.d("asr:ASRDataType:${val.type}", tag: "app");

        switch (val.type) {
          case ASRDataType.SLICE:
          case ASRDataType.SEGMENT:
            var id = val.id!;
            var res = val.res!;
            if (id >= _sentences.length) {
              for (var i = _sentences.length; i <= id; i++) {
                _sentences.add("");
              }
            }
            _sentences[id] = res;
            result = _sentences.map((e) => e).join("");
            LogUtil.d("asr:SEGMENT:${result}", tag: "app");

            cb?.call(result);

            break;
          case ASRDataType.SUCCESS:
            result = val.result!;

            _sentences = [];
            cb?.call(result);
            successCallback?.call(
                result: result, filepath: _config.audio_file_path);
            break;
          case ASRDataType.NOTIFY:
            if (!_realFinish.isClosed) {
              _realFinish.add(true);
            }
            if (result.isEmpty) {
              successCallback?.call(
                  result: result, filepath: _config.audio_file_path);
            }
        }
      }
    } on ASRError catch (e) {
      LogUtil.d("asr:发生错误:错误码：${e.code} \n错误信息: ${e.message} \n详细信息: ${e.resp}",
          tag: "app");

      result = "错误码：${e.code} \n错误信息: ${e.message} \n详细信息: ${e.resp}";
      _realFinish.add(true);
      voiceState = VoiceStateEnum.init;
    } catch (e) {
      LogUtil.v(e.toString());
      LogUtil.d("asr:捕获异常：${e.toString()}", tag: "app");

      _realFinish.add(true);
      voiceState = VoiceStateEnum.init;
    }
  }

  Future<bool> stopRecording() async {
    _timer?.cancel();
    try {
      await _controller?.stop();
    } catch (e) {}

    await for (final val in realFinish) {
      if (val == true) {
        return Future.value(true);
      }
    }
    return Future.value(true);
    // await _controller?.release();
  }
}
