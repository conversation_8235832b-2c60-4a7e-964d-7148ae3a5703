import 'dart:typed_data';

import 'package:wechat_kit/wechat_kit.dart';

// import 'package:fluwx/fluwx.dart';

/// 微信SDK
class WXSDK {
  // static final fluwx = Fluwx();
  static initSDK() async {
    await WechatKitPlatform.instance.registerApp(
      appId: 'wx8a882167ca93bd37',
      universalLink: 'https://www.xinquai.com/app/',
    );
    print('注册APP成功');
  //   fluwx.registerApi(
  //     appId: "wx8a882167ca93bd37",
  //     universalLink: "https://www.xinquai.com/app/",
  //     doOnAndroid: true,
  //     doOnIOS: true,
  //   );
  //   var result = await fluwx.isWeChatInstalled;
    // print("is installed $result");
    // print("fluwx 初始化完成");
  }

  // 分享图片到微信好友
  static shareImageToWeChatFriend(String path) async {
    print('shareImageToWeChatFriend path: $path');
    await WechatKitPlatform.instance.shareImage(
      scene: WechatScene.kSession,
      imageUri: Uri.file(path),
    );
  }

  // 分享图片到微信朋友圈
  static shareImageToWeChatMoments(String path) async {
    print('shareImageToWeChatMoments path: $path');
    await WechatKitPlatform.instance.shareImage(
      scene: WechatScene.kTimeline,
      imageUri: Uri.file(path),
    );
  }
}
