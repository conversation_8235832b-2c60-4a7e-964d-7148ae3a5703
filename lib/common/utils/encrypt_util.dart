import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';

import 'package:common_utils/common_utils.dart';
import 'package:encrypt/encrypt.dart';

class EncryptUtils {
  //Base64编码
  static String encodeBase64(String data) {
    return base64Encode(utf8.encode(data));
  }

  //Base64解码
  static String decodeBase64(String data) {
    return String.fromCharCodes(base64Decode(data));
  }

  // md5 加密 32位小写
  static String encodeMd5(String plainText) {
    return EncryptUtil.encodeMd5(plainText);
  }

  //AES加密
  static String aesEncrypt(plainText) {
    try {
      final key = Key.fromUtf8(('NJ46WfEmXbqsftpa+4jSaw=='));
      final iv = IV.fromLength(16);

      final encrypter = Encrypter(AES(key, mode: AESMode.cbc));

      final encrypted = encrypter.encrypt(plainText, iv: iv);
      var list = iv.bytes + encrypted.bytes;
      return base64.encode(list);
    } catch (err) {
      print("aes encode error:$err");
      return plainText;
    }
  }
}
