import 'dart:async';

import 'package:audio_session/audio_session.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/utils/package_util.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_vlc_player/src/vlc_player_platform.dart';
import 'package:wechat_kit/wechat_kit.dart';

enum PlayState {
  /// The player has not loaded an [AudioSource].
  idle,

  /// The player is loading an [AudioSource].
  loading,

  /// The player is buffering audio and unable to play.
  buffering,

  /// The player is has enough audio buffered and is able to play.
  ready,

  /// The player has reached the end of the audio.
  completed,
}

class CommonUtils {
  static VlcPlayerController videoPlayerController =
      VlcPlayerController.network(
    "",
    hwAcc: HwAcc.full,
    autoPlay: false,
    allowBackgroundPlayback: true,
    options: VlcPlayerOptions(),
  );

  static final _audioPlayer = AudioPlayer();

  static final _streamcontroller = StreamController<PlayState>.broadcast();
  static String? _url;
  static List<String> urls = [];

  //播放的唯一key，用来UI监听播放结束
  static String? _audio_key;
  static late SharedPreferences sharedPreferences;
  static void Function()? finishBlock;
  static Future<bool> init() async {
    sharedPreferences = await SharedPreferences.getInstance();
    PackageUtil.packageInfo = await PackageInfo.fromPlatform();

    videoPlayerController.addOnInitListener(() {
      LogUtil.d("videoPlayerController init", tag: "app : playingState");
    });
    videoPlayerController.addListener(() {
      LogUtil.d("playingState: ${videoPlayerController.value.playingState}",
          tag: "app : playingState");

      switch (videoPlayerController.value.playingState) {
        case PlayingState.initializing:
          _streamcontroller.add(PlayState.idle);
          return;
        case PlayingState.initialized:
          _streamcontroller.add(PlayState.idle);
          return;
        case PlayingState.buffering:
          _streamcontroller.add(PlayState.buffering);
          return;
        case PlayingState.playing:
          _streamcontroller.add(PlayState.ready);
          return;
        case PlayingState.stopped:
          _streamcontroller.add(PlayState.completed);
          return;
        case PlayingState.ended:
          _streamcontroller.add(PlayState.completed);
          LogUtil.d("playingState: 播放结束:$url,剩余urls:${urls.length}",
              tag: "app : playingState");

          if (urls.isNotEmpty) {
            final nextUrl = urls.removeAt(0);
            _continuePlay(nextUrl);
          } else {
            finishBlock?.call();
          }

          return;
        case PlayingState.error:
          finishBlock?.call();
          _streamcontroller.add(PlayState.completed);
          return;
        default:
          finishBlock?.call();
          _streamcontroller.add(PlayState.completed);
      }
    });

    return true;
  }

  static bool get isPlaying {
    return videoPlayerController.value.playingState == PlayingState.playing;
  }

  static bool get isBuffering {
    return videoPlayerController.value.playingState == PlayingState.buffering;
  }

  static bool get isStopped {
    return videoPlayerController.value.playingState == PlayingState.stopped;
  }

  static Stream<PlayState> get playStatus {
    return _streamcontroller.stream;
  }

  static String? get url {
    return _url;
  }

  static String? get key {
    return _audio_key;
  }

  static addUrl(String url) async {
    if (!isStopped) {
      urls.add(url);
      LogUtil.d("playingState: 正在播放，添加到队列,,剩余url：${urls.length}",
          tag: "app : playingState");
    } else {
      LogUtil.d("playingState: 没有播放，直接播放", tag: "app : playingState");

      playVideo(url);
    }
  }

  static _continuePlay(String url) async {
    try {
      _url = url;
      LogUtil.d("playingState: 继续播放: $url,剩余url：${urls.length}",
          tag: "app : playingState");

      final audioSession = await AudioSession.instance;
      await audioSession.configure(const AudioSessionConfiguration.music());
      await audioSession.setActive(true);
      await videoPlayerController.setMediaFromNetwork(url, autoPlay: false);
      videoPlayerController.play();
    } catch (e) {
      LogUtil.d("playingState: 播放失败: $url", tag: "app : playingState");
      finishBlock?.call();
    }
  }

  static playVideo(String? url,
      {String? nextUrl, String? key, void Function()? finish}) async {
    finishBlock = finish;
    if (url == null) {
      finishBlock?.call();
      return;
    }
    await stopPlay();
    LogUtil.d("playingState: 开始播放: $url", tag: "app : playingState");

    try {
      _url = url;
      urls.clear();
      if (nextUrl != null) {
        urls.add(nextUrl);
      }
      _audio_key = key;
      final audioSession = await AudioSession.instance;
      await audioSession.configure(const AudioSessionConfiguration.music());
      await audioSession.setActive(true);
      await videoPlayerController.setMediaFromNetwork(url, autoPlay: false);
      videoPlayerController.play();
    } catch (e) {
      LogUtil.d("playingState: 播放失败: $url", tag: "app : playingState");
      finishBlock?.call();
    }
  }

  static playLocalVideo(String? url, {void Function()? finish}) async {
    if (url == null || url.isEmpty) {
      finish?.call();
      return;
    }
    await stopPlay();
    LogUtil.d("playingState: 开始播放: $url", tag: "app : playingState");

    try {
      _audioPlayer.onPlayerStateChanged.listen((event) {
        if (event != PlayerState.playing && event != PlayerState.paused) {
          finish?.call();
        }
      });
      await _audioPlayer.play(AssetSource(url));
    } catch (e) {
      LogUtil.d("playingState: 播放失败: $url", tag: "app : playingState");
      finish?.call();
    }
  }

  static stopPlay() async {
    try {
      urls.clear();
      await videoPlayerController.stop();
    } catch (_) {}
  }

  static pause() async {
    try {
      await videoPlayerController.pause();
    } catch (_) {}
  }

  static playTts(String? text, {String? key}) async {
    if (text == null || text.isEmpty) {
      return;
    }
    Api.tts(text).then((value) {
      playVideo(value, key: key);
    });
  }
}
