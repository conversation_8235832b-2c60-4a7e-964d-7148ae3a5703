import 'package:audioplayers/audioplayers.dart';

class LocalAudioPlayer {
  static final _audioPlayer = AudioPlayer();

  /// 播放本地音效
  static playLocalAudio(int? audioId) async {
    final autoPathMap = {
      1: 'audio/mission_complete.mp3', // 对话任务完成
      2: 'audio/success_result.mp3', // 生成报告, 快速练习结果
      3: 'audio/wrong_answer.mp3', // 快速练习: 错误答案
      4: 'audio/correct_answer.wav', // 快速练习: 正确答案
    };

    final path = autoPathMap[audioId];

    if (path == null) {
      return;
    }

    print("playLocalAudio: $path");

    await _audioPlayer.play(AssetSource(path));
  }
}
