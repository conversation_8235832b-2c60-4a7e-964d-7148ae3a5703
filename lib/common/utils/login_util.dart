// ignore: unused_import
import 'package:common_utils/common_utils.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/login_info_model/login_info_model.dart';
import 'package:flutter_app_kouyu/common/http/models/user_info_model/user_info_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LoginUtil {
  static final Future<SharedPreferences> prefs =
      SharedPreferences.getInstance();
  static LoginInfoModel? _model;
  static UserInfoModel? _userInfoModel;
  static bool checkAppVersion = true;

  static Future<bool> isLogin() {
    if (_model != null && _model?.data?.accessToken != null) {
      return Future.value(true);
    }
    Future<bool> isLogin = prefs.then((p) {
      String? info = p.getString("login_info");
      if (info == null) {
        return false;
      }
      _model = LoginInfoModel.fromJson(info);

      info = p.getString("user_info");
      if (info != null) {
        _userInfoModel = UserInfoModel.fromJson(info);
      }

      return _model?.data?.accessToken != null;
    });
    return isLogin;
  }

  static LoginInfoModel? loginInfoModel() {
    return _model;
  }

  static Future logout() async {
    Api.logout().then((value) {
      _model = null;
      return prefs.then((p) {
        p.remove("login_info");
      });
    });
  }

  static saveOrUpdateLoginInfo(LoginInfoModel model) {
    _model = model;
    prefs.then((p) {
      p.setString("login_info", model.toJson());
    });
  }

  //记录app提示弹框的时间
  static resetShowUpdateAlertTime() {
    prefs.then((p) {
      p.setInt("last_update_alert", DateTime.now().millisecondsSinceEpoch);
    });
  }

  static Future<int?> getLastUpdateAlertTime() {
    Future<int?> lastTime = prefs.then((p) {
      int? info = p.getInt("last_update_alert");

      return info;
    });
    return lastTime;
  }

  static UserInfoModel? currentUserInfoModel() {
    return _userInfoModel;
  }

  static Future<UserInfoModel?> userInfoModel() async {
    if (_userInfoModel != null) {
      return Future.value(_userInfoModel);
    }
    return updateUserInfo();
  }

  static Future<UserInfoModel?> updateUserInfo() async {
    return Api.getUserInfo({}).then((value) {
      _userInfoModel = value;
      saveOrUpdateUserInfo(value);
      return Future.value(value);
    });
  }

  static saveOrUpdateUserInfo(UserInfoModel model) {
    _userInfoModel = model;
    prefs.then((p) {
      p.setString("user_info", model.toJson());
    });
  }
}
