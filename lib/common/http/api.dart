import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_app_kouyu/common/http/Request.dart';
import 'package:flutter_app_kouyu/common/http/models/analyze_model/analyze_model.dart';
import 'package:flutter_app_kouyu/common/http/models/api_model.dart';
import 'package:flutter_app_kouyu/common/http/models/carousel_list_model/carousel_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ch_to_en_audio_model/ch_to_en_audio_model.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_hint_model/chat_hint_model.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_model/chat_model.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_report_model/chat_report_model.dart';
import 'package:flutter_app_kouyu/common/http/models/check_udpate_model/check_udpate_model.dart';
import 'package:flutter_app_kouyu/common/http/models/collect_sentence.dart';
import 'package:flutter_app_kouyu/common/http/models/collection_collocation_detail/collection_collocation_detail.dart';
import 'package:flutter_app_kouyu/common/http/models/collection_phrasal_verb_detail/collection_phrasal_verb_detail.dart';
import 'package:flutter_app_kouyu/common/http/models/collection_sentence_detail/collection_sentence_detail.dart';
import 'package:flutter_app_kouyu/common/http/models/collection_vocabulary_detail/collection_vocabulary_detail.dart';
import 'package:flutter_app_kouyu/common/http/models/core_vocabulary_model/core_vocabulary_model.dart';
import 'package:flutter_app_kouyu/common/http/models/custome_scene_model/custom_sence_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/custome_scene_model/get_share_image_model.dart';
import 'package:flutter_app_kouyu/common/http/models/custome_scene_model/my_collect_model.dart';
import 'package:flutter_app_kouyu/common/http/models/custome_scene_model/my_create_status_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/custome_scene_model/only_string.model.dart';
import 'package:flutter_app_kouyu/common/http/models/custome_scene_model/string_array_model.dart';
import 'package:flutter_app_kouyu/common/http/models/en_to_ch_model.dart';
import 'package:flutter_app_kouyu/common/http/models/english_level_model/english_level_model.dart';
import 'package:flutter_app_kouyu/common/http/models/english_name_page_model/english_name_page_model.dart';
import 'package:flutter_app_kouyu/common/http/models/english_tap_model/english_tap_model.dart';
import 'package:flutter_app_kouyu/common/http/models/explore_chat_model/explore_chat_model.dart';
import 'package:flutter_app_kouyu/common/http/models/explore_topic_list_model/explore_topic_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/file_upload_model/file_upload_model.dart';
import 'package:flutter_app_kouyu/common/http/models/greeting_model/greeting_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_prescription_model/ielts_prescription_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_analyze_vocabulary_model/ielts_speaking_analyze_vocabulary_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_chat_model/ielts_speaking_chat_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_grammar_check_model/ielts_speaking_grammar_check_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_practice_model/ielts_speaking_practice_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_question_answer_model/ielts_speaking_question_answer_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_report_model/ielts_speaking_report_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_soe_model/ielts_speaking_soe_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_topic_questions_model/ielts_speaking_topic_questions_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_topics_model/ielts_speaking_topics_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_topic_detail_model/ielts_topic_detail_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ielts_user_info_model/ielts_user_info_model.dart';
import 'package:flutter_app_kouyu/common/http/models/iets_tab_model/iets_tab_model.dart';
import 'package:flutter_app_kouyu/common/http/models/learning_phase_model/learning_phase_model.dart';
import 'package:flutter_app_kouyu/common/http/models/login_info_model/login_info_model.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_collocation/collocation_response_model.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_phrasal_verb/my_collection_phrasal_verb.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_sentence/my_collection_sentence.dart';
import 'package:flutter_app_kouyu/common/http/models/my_collection_vocabulary/my_collection_vocabulary_model.dart';
import 'package:flutter_app_kouyu/common/http/models/promotion_config/promotion_config.dart';
import 'package:flutter_app_kouyu/common/http/models/purchase_record_model/purchase_record_model.dart';
import 'package:flutter_app_kouyu/common/http/models/purpuse_model/purpuse_model.dart';
import 'package:flutter_app_kouyu/common/http/models/quick_study_model/batch_collect_model.dart';
import 'package:flutter_app_kouyu/common/http/models/quick_study_model/quick_study_answer_model.dart';
import 'package:flutter_app_kouyu/common/http/models/quick_study_model/quick_study_question_model.dart';
import 'package:flutter_app_kouyu/common/http/models/quick_study_model/quick_study_result_model.dart';
import 'package:flutter_app_kouyu/common/http/models/quick_study_model/reback_difficulty.model.dart';
import 'package:flutter_app_kouyu/common/http/models/report_list_model/report_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/role_list_model/role_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/scene_topics_vocabulary_model/scene_topics_vocabulary_model.dart';
import 'package:flutter_app_kouyu/common/http/models/sence_topic_sentence/sence_topic_sentence.dart';
import 'package:flutter_app_kouyu/common/http/models/sence_topics_class/sence_topics_class.dart';
import 'package:flutter_app_kouyu/common/http/models/sentenc_polishing_model/sentenc_polishing_model.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';
import 'package:flutter_app_kouyu/common/http/models/study_progress_list_model/study_progress_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/system_secret_model/system_secret_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_translate_model/tool_ocr_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_translate_model/tool_translate_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_english_writing_detail_model/tool_user_english_writing_detail_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_english_writing_recods_model/tool_user_english_writing_recods_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_try_times_model/tool_user_try_times_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_writing_composition_assessment_model/tool_user_writing_composition_assessment_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_writing_detail_model/tool_user_writing_detail_model.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_writing_page_model/tool_user_writing_page_model.dart';
import 'package:flutter_app_kouyu/common/http/models/topic_sences/topic_sences.dart';
import 'package:flutter_app_kouyu/common/http/models/user_info_model/user_info_model.dart';
import 'package:flutter_app_kouyu/common/http/models/vip_list_model/vip_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/vocabulary_soe_model/vocabulary_soe_model.dart';
import 'package:flutter_app_kouyu/common/http/models/weekly_free_topic_list_model/weekly_free_topic_list_model.dart';
import 'package:flutter_app_kouyu/common/http/models/wxpay_model/wxpay_model.dart';
import 'package:flutter_app_kouyu/common/utils/encrypt_util.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';
import 'package:flutter_udid/flutter_udid.dart';

import 'models/audio_upload_model/audio_upload_model.dart';
import 'models/custome_scene_model/custome_scene_type_list_model.dart';
import 'models/custome_scene_model/empty.model.dart';
import 'models/custome_scene_model/my_create_list_model.dart';
import 'models/custome_scene_model/random_generate_scene_model.dart';
import 'models/custome_scene_model/scene_detail_v2_model.dart';
import 'models/topic_sences/reback_scene_model.dart';
import 'models/topic_sences/system_scene_list_model.dart';

class Api {
  static Future<LoginInfoModel> login(data) {
    return Request.post(
      "/login/phone_login",
      data: data,
    ).then((value) => Future.value(LoginInfoModel.fromMap(value)));
  }

  static Future<LoginInfoModel> visitorLogin() async {
    String udid = await FlutterUdid.udid;
    return Request.post("/visitor/login", data: {
      "device_id": udid,
      "os_type": _osType(),
      "s": EncryptUtils.aesEncrypt(udid)
    }).then((value) => Future.value(LoginInfoModel.fromMap(value)));
  }

  static Future<SendSmsModel> smsSend(data) {
    return Request.post(
      "/login/send_sms",
      data: data,
    ).then((value) => Future.value(SendSmsModel.fromJson(value)));
  }

  static Future<UserInfoModel> getUserInfo(data) async {
    String url = "/get_user_info";

    return Request.get(
      url,
    ).then((value) => Future.value(UserInfoModel.fromMap(value)));
  }

  //自由聊天不需要传topic参数，场景话题需要，例如爱好&兴趣话题"topic":"0001"
  static Future<UserInfoModel> getGreetingStream(data) {
    return Request.post(
      "/greeting_stream",
    ).then((value) => Future.value(UserInfoModel.fromJson(value)));
  }

//自由聊天或者场景话题进入口问候语(自由聊天不需要传topic参数，场景话题需要，例如爱好&兴趣话题"topic":"0001")
  static Future<GreetingModel> getTimePeriodGreeting(data) {
    return Request.post("/get_time_period_greeting", data: data)
        .then((value) => Future.value(GreetingModel.fromJson(value)));
  }

  static Future<EnToCnModel> translateToCH(String? text) {
    if (text == null) {
      return Future.value(EnToCnModel());
    }
    return Request.post("/en_to_cn", data: {'text': text})
        .then((value) => Future.value(EnToCnModel.fromJson(value)));
  }

  static Future<CollectionSentence> collectionSentence(
      String? text, String source, String? sourceId) {
    if (text == null) {
      return Future.value(CollectionSentence());
    }
    var data = {'sentence': text, "source": source, "source_id": sourceId};
    if (sourceId != null) {
      data["source_id"] = sourceId;
    }
    return Request.post("/system_scene_topics_course/collect_sentence",
            data: data)
        .then((value) => Future.value(CollectionSentence.fromJson(value)));
  }

  static Future<CollectionSentence> cancelCollectionSentence(String? text) {
    if (text == null) {
      return Future.value(CollectionSentence());
    }
    return Request.post("/system_scene_topics_course/cancel_collect_sentence",
            data: {'sentence': text})
        .then((value) => Future.value(CollectionSentence.fromJson(value)));
  }

  static Future<CollectionSentence> collectVocabulary(String? text) {
    if (text == null) {
      return Future.value(CollectionSentence());
    }
    return Request.post("/system_scene_topics_course/collect_vocabulary",
            data: {'vocabulary': text})
        .then((value) => Future.value(CollectionSentence.fromJson(value)));
  }

  static Future<CollectionSentence> cancelCollectVocabulary(String? text) {
    if (text == null) {
      return Future.value(CollectionSentence());
    }
    return Request.post("/system_scene_topics_course/cancel_collect_vocabulary",
            data: {'vocabulary': text})
        .then((value) => Future.value(CollectionSentence.fromJson(value)));
  }

  static Future<CollectionSentence> collectPhrasalVerb(int? id) {
    if (id == null) {
      return Future.value(CollectionSentence());
    }
    return Request.post("/system_scene_topics_course/collect_phrasal_verb",
            data: {'phrasal_verb_id': id})
        .then((value) => Future.value(CollectionSentence.fromJson(value)));
  }

  ///批量收藏短语动词
  static Future<BatchCollectModel> batchCollectPhrasalVerb(List<int> ids) {
    if (ids.isEmpty) {
      return Future.value(BatchCollectModel());
    }
    return Request.post(
            "/system_scene_topics_course/batch_collect_phrasal_verb",
            data: {'phrasal_verb_id_list': ids})
        .then((value) => Future.value(BatchCollectModel.fromJson(value)));
  }

  static Future<CollectionSentence> collectCollocation(int? id) {
    if (id == null) {
      return Future.value(CollectionSentence());
    }
    return Request.post("/system_scene_topics_course/collect_collocation",
            data: {'collocation_id': id})
        .then((value) => Future.value(CollectionSentence.fromJson(value)));
  }

  static Future<BatchCollectModel> batchCollectCollocation(List<int> ids) {
    if (ids.isEmpty) {
      return Future.value(BatchCollectModel());
    }
    return Request.post("/system_scene_topics_course/batch_collect_collocation",
            data: {'collocation_id_list': ids})
        .then((value) => Future.value(BatchCollectModel.fromJson(value)));
  }

  static Future<CollectionSentence> cancelCollectPhrasalVerb(int? id) {
    if (id == null) {
      return Future.value(CollectionSentence());
    }
    return Request.post(
            "/system_scene_topics_course/cancel_collect_phrasal_verb",
            data: {'phrasal_verb_id': id})
        .then((value) => Future.value(CollectionSentence.fromJson(value)));
  }

  static Future<CollectionSentence> cancelCollectCollocation(int? id) {
    if (id == null) {
      return Future.value(CollectionSentence());
    }
    return Request.post(
            "/system_scene_topics_course/cancel_collect_collocation",
            data: {'collocation_id': id})
        .then((value) => Future.value(CollectionSentence.fromJson(value)));
  }

  static Future<ChatHintModel> getChatHint(String text) {
    return Request.post("/hint", data: {'text': text})
        .then((value) => Future.value(ChatHintModel.fromJson(value)));
  }

  static Future<ChatHintModel> generateNextSentence(
      String? topicName, String chatMoudle, int conversationId) {
    return Request.post("/chat/generate_next_sentence", data: {
      'topic_name': topicName,
      'chat_moudle': chatMoudle,
      'conversation_id': conversationId
    }).then((value) => Future.value(ChatHintModel.fromJson(value)));
  }

  static Future<ChToEnAudioModel> cnToEn(String? text) {
    if (text == null) {
      return Future.value(ChToEnAudioModel());
    }
    return Request.post("/translate", data: {'text': text})
        .then((value) => Future.value(ChToEnAudioModel.fromJson(value)));
  }

  static Future<AnalyzeModel> analyze(data) {
    return Request.post("/analyze", data: data, hideError: true)
        .then((value) => Future.value(AnalyzeModel.fromJson(value)));
  }

  static Future<SentencPolishingModel> sentencePolishing(data) {
    return Request.post("/chat/sentence_polishing", data: data, hideError: true)
        .then((value) => Future.value(SentencPolishingModel.fromJson(value)));
  }

  static Future<SoeModel> soe(data) {
    return Request.post(
      "/soe",
      data: data,
      options: Options(receiveTimeout: const Duration(seconds: 100)),
      hideError: true,
    ).then((value) => Future.value(SoeModel.fromJson(value)));
  }

  static Future<ChatModel> chatSummaryPredict(data) {
    return Request.post("/chat_summary_predict", data: data)
        .then((value) => Future.value(ChatModel.fromJson(value)));
  }

  static Future<Stream<Uint8List>> chatStream(data,
      {CancelToken? cancelToken}) {
    return Request.streamPost(
      "/chat/stream",
      data: data,
      cancelToken: cancelToken,
    );
  }

  static Future<AudioUploadModel> uploadFile(String filePath,
      {required String topicCode, required scene}) async {
    var formData = FormData.fromMap({
      "file": await MultipartFile.fromFile(filePath),
    });
    return Request.post("/user_upload_file?scene=$scene&topic_code=$topicCode",
            data: formData)
        .then((value) => Future.value(AudioUploadModel.fromJson(value)));
  }

  static Future<VipListModel> openVipList(data) {
    return Request.get("/member/get_icon_list")
        .then((value) => Future.value(VipListModel.fromJson(value)));
  }

  static Future<RoleListModel> getRoleList(data) {
    return Request.get("/get_all_characters")
        .then((value) => Future.value(RoleListModel.fromJson(value)));
  }

  static Future<SystemSecretModel> getASRSystemSecret() {
    return Request.get(
            "/system/get_system_secret?code=TENCENT_CLOUD_ASR_API_SECRET_ID")
        .then((value) => Future.value(SystemSecretModel.fromJson(value)));
  }

  static Future<bool> setRole(int? roleId) async {
    return Request.post("/set_character_id", data: {'character_id': roleId})
        .then((value) => Future.value(true));
  }

  static Future<FileUploadModel> userUploadFile(String filePath) async {
    var formData = FormData.fromMap({
      "file": await MultipartFile.fromFile(filePath),
    });

    return Request.post("/feedback/user_upload_file", data: formData)
        .then((value) => Future.value(FileUploadModel.fromJson(value)));
  }

  static Future<dynamic> feedback(data) {
    return Request.post("/feedback/add_user_usage_feedback", data: data);
  }

  static Future<dynamic> logout() {
    return Request.post("/logout", data: {});
  }

  static Future<bool> verifyIosReceipt(data) {
    return Request.post("/pay/verify_ios_receipt", data: data).then((value) {
      return true;
    }, onError: (_) {
      return false;
    });
  }

  static Future<String> alipayRequest(data) {
    return Request.post("/alipay/create_order", data: data).then((value) {
      return value["data"]["order_string"];
    }, onError: (_) {
      return false;
    });
  }

  static Future<WxpayModel> wxpayRequest(data) {
    return Request.post("/alipay/wx_pre_pay", data: data).then((value) {
      return WxpayModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<String> tts(String text) {
    return Request.post("/text_to_speech", data: {"text": text}).then((value) {
      return value["data"]["audio_url"];
    }, onError: (_) {
      return false;
    });
  }

  static Future<PurpuseModel> purpose() {
    return Request.get("/get_all_learning_purposes").then((value) {
      return PurpuseModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<EnglishLevelModel> getAllEnglishLevels() {
    return Request.get("/get_all_english_levels").then((value) {
      return EnglishLevelModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<PurpuseModel> getSystemSceneTopicsClassForUserChoose() {
    return Request.get("/get_system_scene_topics_class_for_user_choose").then(
        (value) {
      return PurpuseModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<dynamic> setLearningPurpose(data) {
    return Request.post("/set_learning_purpose", data: data);
  }

  static Future<dynamic> setEnglishLevel(data) {
    return Request.post("/set_english_level", data: data);
  }

  static Future<dynamic> updateUserRate(data) {
    return Request.post("/set_english_personal_center/update_user_ratelevel",
        data: data);
  }

  static Future<dynamic> setUserInterestTopics(data) {
    return Request.post("/user_interest_topics/add_batch", data: data);
  }

  static Future<dynamic> setProfilePicture(data) {
    return Request.post("/set_profile_picture", data: data);
  }

  static Future<dynamic> setNickname(data) {
    return Request.post("/set_nickname", data: data);
  }

  static Future<dynamic> setUserGender(data) {
    return Request.post("/user/set_user_gender", data: data);
  }

  static Future<List<String>> getSystemProfilePictureList(
      Map<String, dynamic>? param) {
    return Request.get("/user_guide_page/get_system_user_avatar_list",
            params: param)
        .then((value) {
      return List<String>.from(value['data']);
    }, onError: (_) {
      return false;
    });
  }

  static Future<List<String>> getNickNameList(Map<String, dynamic>? param) {
    return Request.get("/user/get_name_list", params: param).then((value) {
      return List<String>.from(value['data']);
    }, onError: (_) {
      return false;
    });
  }

  static Future<EnglishTapModel> getPolishedEnglishLabelList(
      Map<String, dynamic>? param) {
    return Request.get("/chat/get_polished_english_label_list", params: param)
        .then((value) {
      return EnglishTapModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<PromotionConfig> getPromotionConfig() {
    return Request.get("/promotion/get_promotion_config").then((value) {
      return PromotionConfig.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static String _osType() {
    //os_type: 操作系统类型 1：iOS 2: iPadOS 3: macOS 4: watchOS 5: Android 6: Harmony OS
    String osType = "";
    if (!kIsWeb && Platform.isIOS) {
      osType = "1";
    }
    if (!kIsWeb && Platform.isAndroid) {
      osType = "5";
    }
    return osType;
  }

  static Future<CheckUdpateModel> checkUdpate() {
    //os_type: 操作系统类型 1：iOS 2: iPadOS 3: macOS 4: watchOS 5: Android 6: Harmony OS
    String osType = _osType();
    String path = "/appInfo/check_app_version?os_type=$osType";

    return Request.get(path).then((value) {
      return CheckUdpateModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<bool> unRegister() {
    return Request.post("/user/logoff").then((value) {
      return Future.value(true);
    }, onError: (_) {
      return false;
    });
  }

  static Future<SenceTopicsClass> sceneTopicsClassList() {
    return Request.get("/scene_topics_class_list").then((value) {
      return SenceTopicsClass.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<CarouselListModel> carouselList() {
    return Request.get("/home_page/get_carousel_list").then((value) {
      return CarouselListModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<WeeklyFreeTopicListModel> weeklyFreeTopicList() {
    return Request.get("/home_page/weekly_free_topic_list").then((value) {
      return WeeklyFreeTopicListModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<StudyProgressListModel> todayStudyProgressList() {
    return Request.get("/home_page/today_study_progress_list").then((value) {
      return StudyProgressListModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<TopicSences> sceneTopicss(data) {
    return Request.get("/scene_topics", params: data).then((value) {
      return TopicSences.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<SceneTopicsVocabularyModel> sceneTopicsVocabularyModel(data) {
    return Request.get(
            "/system_scene_topics_course/system_scene_topics_vocabulary_list",
            params: data)
        .then((value) {
      return SceneTopicsVocabularyModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<SenceTopicSentence> sceneTopicSsentenceModel(data) {
    return Request.get(
            "/system_scene_topics_course/system_scene_topics_sentence_list",
            params: data)
        .then((value) {
      return SenceTopicSentence.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<bool> saveHaveLearnedVocabulary(data) {
    return Request.post("/user_basic_course/save_have_learned_vocabulary",
            data: data)
        .then((value) {
      return true;
    }, onError: (_) {
      return false;
    });
  }

  static Future<VocabularySoeModel> vocabularySoeModel(data) {
    return Request.post("/user_basic_course/vocabulary_soe", data: data).then(
        (value) {
      return VocabularySoeModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<SoeModel> sentenceSoeModel(data) {
    return Request.post("/user_basic_course/sentence_soe", data: data).then(
        (value) {
      return SoeModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<bool> checkTargetFinish(data) {
    return Request.post("/chat/check_target_finish", data: data).then((value) {
      return value["data"] as bool;
    }, onError: (_) {
      return false;
    });
  }

  static Future<ChatReportModel> generateConversationReport(data) {
    return Request.post("/chat/generate_conversation_report", data: data).then(
        (value) {
      return ChatReportModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<ChatReportModel> getConversationReport(data) {
    return Request.get("/personal_center/get_conversation_report", params: data)
        .then((value) {
      return ChatReportModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<IeltsSpeakingReportModel> generateIeltsSpeakingReport(data) {
    return Request.post("/ielts_speaking/generate_report", data: data).then(
        (value) {
      return IeltsSpeakingReportModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<IeltsSpeakingReportModel> getIeltsSpeakingReport(data) {
    return Request.get("/ielts_speaking/get_report_detail", params: data).then(
        (value) {
      return IeltsSpeakingReportModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //report 1 全部 2 自由对话 3 实战演练
  static Future<ReportListModel> queryConversationReportPage(
      int pageNum, int pageSize, int reportType) {
    return Request.get("/personal_center/query_conversation_report_page",
        params: {
          "page_num": pageNum,
          "page_size": pageSize,
          "report_type": reportType
        }).then((value) {
      return ReportListModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //雅思口语报告
  static Future<ReportListModel> queryIeltsSpeakingPage(
      int pageNum, int pageSize) {
    return Request.get("/ielts_speaking/query_ielts_speaking_practice_list",
        params: {
          "page_num": pageNum,
          "page_size": pageSize,
        }).then((value) {
      return ReportListModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //我的单词收藏
  static Future<MyCollectionVocabulary> getMyCollectVocabularyList(
      int pageNum, int pageSize, String orderByType) {
    return Request.get("/personal_center/my_collect_vocabulary_list", params: {
      "page_num": pageNum,
      "page_size": pageSize,
      "order_by_type": orderByType
    }).then((value) {
      return MyCollectionVocabulary.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //我的短语动词收藏
  static Future<MyCollectionPhrasalVerb> getMyCollectPhrasalVerbList(
      int pageNum, int pageSize, String orderByType) {
    return Request.get("/personal_center/my_collect_phrasal_verb_list",
        params: {
          "page_num": pageNum,
          "page_size": pageSize,
          "order_by_type": orderByType
        }).then((value) {
      return MyCollectionPhrasalVerb.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //我的搭配收藏
  static Future<MyCollectionCollocation> getMyCollectCollocationList(
      int pageNum, int pageSize, String orderByType) {
    return Request.get("/personal_center/my_collect_collocations_list",
        params: {
          "page_num": pageNum,
          "page_size": pageSize,
          "order_by_type": orderByType
        }).then((value) {
      return MyCollectionCollocation.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //收藏的单词详情
  static Future<CollectionVocabularyDetail> getVocabularyDetail(
      String vocabulary) {
    return Request.get("/chat/get_vocabulary_detail", params: {
      "vocabulary": vocabulary,
    }).then((value) {
      return CollectionVocabularyDetail.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //收藏的短语动词详情
  static Future<CollectionPhrasalVerbDetail> getPhrasalVerbDetail(int id) {
    return Request.get("/phrase/get_phrasal_verb_detail", params: {
      "phrasal_verb_id": id,
    }).then((value) {
      return CollectionPhrasalVerbDetail.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //收藏的搭配详情
  static Future<CollectionCollocationDetail> getCollocationDetail(int id) {
    return Request.get("/phrase/get_collocation_detail", params: {
      'collocation_id': id,
    }).then((value) {
      return CollectionCollocationDetail.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //收藏的句子详情
  static Future<CollectionSentenceDetail> getSentenceDetail(
      String sentence, String collectId) {
    return Request.get("/personal_center/my_collect_sentence_detail",
        params: {"sentence": sentence, "collect_id": collectId}).then((value) {
      return CollectionSentenceDetail.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //高频词汇
  static Future<CoreVocabularyModel> getUserChatCoreVocabularies(
      String conversationId) {
    return Request.get("/chat/get_user_chat_core_vocabularies", params: {
      "conversation_id": conversationId,
    }).then((value) {
      return CoreVocabularyModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //高频词汇
  static Future<CoreVocabularyModel> getIeltsSpeakingCoreVocabularies(
      String practiceId) {
    return Request.get(
        "/ielts_speaking/get_ielts_speaking_practices_core_vocabularies",
        params: {
          "practice_id": practiceId,
        }).then((value) {
      return CoreVocabularyModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //我的句子收藏
  static Future<MyCollectionSentence> getMyCollectSentenceList(
      int pageNum, int pageSize, String orderByType) {
    return Request.get("/personal_center/my_collect_sentence_list", params: {
      "page_num": pageNum,
      "page_size": pageSize,
      "order_by_type": orderByType
    }).then((value) {
      return MyCollectionSentence.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<void> setPlaybackSpeed(num data) {
    return Request.post("/set_playback_speed", data: {"playback_speed": data})
        .then((value) {
      return;
    }, onError: (_) {
      return false;
    });
  }

  /// 获取我的雅思信息
  static Future<IeltsUserInfoModel> getUserIeltsInfo() {
    return Request.get("/ielts_speaking/get_user_ielts_info").then((value) {
      return IeltsUserInfoModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 获取 topic 详情
  static Future<IeltsTopicDetailModel> getIeltsTopicDetail(int topicId) {
    return Request.get("/ielts_speaking/get_ielts_speaking_questions",
        params: {"topic_id": topicId}).then((value) {
      return IeltsTopicDetailModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 获取 tab list
  static Future<IetsTabModel> getIeltsTabs() {
    return Request.get("/ielts_speaking/get_ielts_speaking_tab", params: {})
        .then((value) {
      return IetsTabModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 获取 topic list
  static Future<IeltsSpeakingTopicsModel> getIeltsSpeakingTopics(
      String partCategoryCode, int topicType) {
    return Request.get("/ielts_speaking/get_ielts_speaking_topics", params: {
      "part_category_code": partCategoryCode,
      "topic_type": topicType
    }).then((value) {
      return IeltsSpeakingTopicsModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 获取 雅思指南数据
  static Future<IeltsPrescriptionModel> getIeltsSpeakingGuide() {
    return Request.get("/ielts_speaking/ielts_speaking_guide").then((value) {
      return IeltsPrescriptionModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 获取ielts
  static Future<IeltsSpeakingPracticeModel> startPractice(
      int topicId, int practiceType, List<int> topicIdArray) {
    return Request.post("/ielts_speaking/start_practice", data: {
      "topic_id": topicId,
      "practice_type": practiceType,
      "topic_id_array": topicIdArray
    }).then((value) {
      return IeltsSpeakingPracticeModel.fromJson(value);
    }, onError: (_) {
      return null;
    });
  }

  /// 获取探索数据
  static Future<ExploreTopicListModel> exploreTopicList(String exploreType) {
    return Request.get("/explore/topic_list", params: {
      "explore_type": exploreType,
    }).then((value) {
      return ExploreTopicListModel.fromJson(value);
    }, onError: (_) {
      return null;
    });
  }

  //雅思回答
  static Future<IeltsSpeakingChatModel> ieltsSpeakingChat(
      int practiceId, int questionId, String answer, String? userAudioUrl) {
    return Request.post("/ielts_speaking/chat", data: {
      "practice_id": practiceId,
      "question_id": questionId,
      "answer": answer,
      "user_audio_url": userAudioUrl
    }).then((value) {
      return IeltsSpeakingChatModel.fromJson(value);
    }, onError: (_) {
      return null;
    });
  }

  //雅思语法检测
  static Future<IeltsSpeakingGrammarCheckModel> ieltsSpeakingGrammarCheck(
      int practiceId, int questionId, String text) {
    return Request.post("/ielts_speaking/grammar_check", data: {
      "practice_id": practiceId,
      "question_id": questionId,
      "text": text,
    }).then((value) {
      return IeltsSpeakingGrammarCheckModel.fromJson(value);
    }, onError: (_) {
      return null;
    });
  }

  //雅思发音评测
  static Future<IeltsSpeakingSoeModel> ieltsSpeakingSoe(
      int practiceId, int questionId, String text, String audioUrl) {
    return Request.post("/ielts_speaking/soe", data: {
      "practice_id": practiceId,
      "question_id": questionId,
      "text": text,
      "audio_url": audioUrl
    }).then((value) {
      return IeltsSpeakingSoeModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //雅思词汇分析
  static Future<IeltsSpeakingAnalyzeVocabularyModel>
      ieltsSpeakingAnalyzeVocabularye(
          int practiceId, int questionId, String answer) {
    return Request.post("/ielts_speaking/analyze_vocabulary", data: {
      "practice_id": practiceId,
      "question_id": questionId,
      "answer": answer
    }).then((value) {
      return IeltsSpeakingAnalyzeVocabularyModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //雅思答案示例
  static Future<IeltsSpeakingQuestionAnswerModel>
      ieltsSpeakingGetSnswerByQuestionId(int questionId) {
    return Request.get("/ielts_speaking/get_answer_by_question_id", params: {
      "question_id": questionId,
    }).then((value) {
      return IeltsSpeakingQuestionAnswerModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  //获取雅思主题题目
  static Future<IeltsSpeakingTopicQuestionModel> ieltsSpeakingGetTopicquestions(
      int topicId) {
    return Request.get("/ielts_speaking/get_ielts_speaking_questions", params: {
      "topic_id": topicId,
    }).then((value) {
      return IeltsSpeakingTopicQuestionModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<ToolUserTryTimesModel> getUserToolTryTimes() {
    return Request.post("/tools/get_user_try_times", data: {}).then((value) {
      return ToolUserTryTimesModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<ToolUserWritingPageModel> getUserCompositionAssessmentPage(
      int pageNum, int pageSize) {
    return Request.post("/tools/get_user_composition_assessment_page", data: {
      "page_num": pageNum,
      "page_size": pageSize,
    }).then((value) {
      return ToolUserWritingPageModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<ToolUserWritingDetailModel> getUserCompositionAssessmentDetail(
      int id) {
    return Request.post("/tools/get_user_composition_assessment_detail", data: {
      "id": id,
    }).then((value) {
      return ToolUserWritingDetailModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 工具: 翻译
  static Future<ToolTranslateModel> translate(
      String text, String source_language, String target_language) {
    return Request.post("/tools/translation", data: {
      "text": text,
      "source_language": source_language,
      "target_language": target_language
    }).then((value) {
      return ToolTranslateModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 工具: 同声传译
  static Future<ToolTranslateModel> voiceTranslate(
      String text, String source_language, String target_language) {
    return Request.post("/tools/simultaneous_translation", data: {
      "text": text,
      "source_language": source_language,
      "target_language": target_language
    }).then((value) {
      return ToolTranslateModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 工具: ocr识别
  static Future<ToolOcrModel> ocr(String image_url) {
    return Request.post("/tools/ocr", data: {
      "image_url": image_url,
    }).then((value) {
      return ToolOcrModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  // 写作批改
  static Future<ToolUserWritingCompositionAssessmentModel>
      compositionAssessment(String text) {
    return Request.post("/composition_assessment", data: {
      "text": text,
    }).then((value) {
      return ToolUserWritingCompositionAssessmentModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  // 写作批改优化
  static Future<Stream<List<int>>> compositionAssessmentOptimize(int id) {
    return Request.streamPost("/composition_assessment_optimize", data: {
      "id": id,
    }).then((value) {
      return value;
    }, onError: (_) {
      return false;
    });
  }

  static Future<Stream<List<int>>> englistWriting(String compositionType,
      String directions, String title, String wordCount) {
    return Request.streamPost("/writing", data: {
      "composition_type": compositionType,
      "directions": directions,
      "title": title,
      "word_count": wordCount
    }).then((value) {
      return value;
    }, onError: (_) {
      return false;
    });
  }

  /// 获取ielts
  static Future<ExploreChatModel> exploreChat(String topicCode,
      String exploreType, String? input, int? conversationId) {
    return Request.post("/explore/chat", data: {
      "topic_code": topicCode,
      "explore_type": exploreType,
      "conversation_id": conversationId,
      "input": input
    }).then((value) {
      return ExploreChatModel.fromJson(value);
    }, onError: (_) {
      return null;
    });
  }

  static Future<ToolUserEnglishWritingRecodsModel> getUserEnglishWritingPage(
      int pageNum, int pageSize) {
    return Request.post("/tools/get_user_english_writing_page", data: {
      "page_num": pageNum,
      "page_size": pageSize,
    }).then((value) {
      return ToolUserEnglishWritingRecodsModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<ToolUserEnglishWritingDetailModel> getUserEnglishWritingDetail(
      int id) {
    return Request.post("/tools/get_user_english_writing_detail",
        data: {"id": id}).then((value) {
      return ToolUserEnglishWritingDetailModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  // 获取快速学习的练习列表
  static Future<QuickStudyQuestionModel> getQuickStudyPracticeList(
      String type) {
    return Request.post("/quick_practice/start_practice",
        data: {"practice_type": type}).then((value) {
      return QuickStudyQuestionModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  // 快速练习: 回答题目
  static Future<QuickStudyAnswerModel> quickStudyAnswer(
      String type, String answer, int userPracticeRecordId) {
    return Request.post("/quick_practice/submit_practice_answer", data: {
      "user_practice_record_id": userPracticeRecordId,
      "user_answer": answer,
      "practice_type": type
    }).then((value) {
      return QuickStudyAnswerModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  // 快速练习: 结果页面
  static Future<QuickStudyResultModel> getQuickStudyResult(
      String type, int practiceId) {
    return Request.get("/quick_practice/get_practice_result",
            params: {"practice_round_id": practiceId, "practice_type": type})
        .then((value) {
      return QuickStudyResultModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  // 快速练习: 批量收藏
  static Future<BatchCollectModel> batchCollect(List<String> textList) {
    return Request.post("/system_scene_topics_course/batch_collect_vocabulary",
        data: {"vocabulary_text_list": textList}).then((value) {
      return BatchCollectModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  // 快速练习: 反馈难度
  static Future<RebackDifficultyModel> rebackDifficulty(String type, int rate) {
    return Request.post("/quick_practice/rate",
        data: {"practice_type": type, "rate": rate}).then((value) {
      return RebackDifficultyModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<void> setIsMixedTeaching(num isMixedTeaching) {
    return Request.post("/user_guide_page/set_is_mixed_teaching",
        data: {"is_mixed_teaching": isMixedTeaching}).then((value) {
      return;
    }, onError: (_) {
      return false;
    });
  }

  /// 场景: 举报场景
  static Future<RebackSceneModel> rebackScene(String sceneCode, String reason) {
    return Request.post("/community_scene_topics/report_topic",
        data: {"topic_code": sceneCode, "report_reason": reason}).then((value) {
      return RebackSceneModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 场景: 获取举报原因列表
  static Future<StringListModel> getSceneRebackReasonList() {
    return Request.get("/community_scene_topics/get_report_reason_list").then(
        (value) {
      return StringListModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<LearningPhaseModel> getLearningPhaseList() {
    return Request.get("/user_guide_page/get_learning_phase_list").then(
        (value) {
      return LearningPhaseModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  // 自定义场景: 获取自定义场景类型列表
  static Future<CustomSceneTypeListModel> customeSceneTypeList() {
    return Request.get("/community_scene_topics/get_order_by_list").then(
        (value) {
      return CustomSceneTypeListModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<void> setLearningPhase(String code) {
    return Request.post("/user_guide_page/set_learning_phase",
        data: {"learning_phase": code}).then((value) {
      return;
    }, onError: (_) {
      return false;
    });
  }

  // 自定义场景: 分页查询
  static Future<CustomSceneListModel> getCustomSceneList(
    int pageNum,
    int pageSize,
    String? keyWord,
    String? orderBy,
  ) {
    return Request.get("/community_scene_topics/get_topic_list", params: {
      "page_index": pageNum,
      "page_size": pageSize,
      "keyword": keyWord ?? '',
      "order_by": orderBy ?? '',
    }).then((value) {
      return CustomSceneListModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<EnglishNamePageModel> getEnglishNamesPage(
      int pageNum, int pageSize, String code, int gender) {
    return Request.get("/user_guide_page/get_english_names_page", params: {
      "page_index": pageNum,
      "page_size": pageSize,
      "keyword": code,
      "gender": gender
    }).then((value) {
      return EnglishNamePageModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 系统场景: 分页查询
  static Future<SystemSceneListModel> getSystemSceneList(
    int pageNum,
    int pageSize,
    String? keyWord,
    String? orderBy,
    String? classCode,
  ) {
    return Request.get("/system_scene_topics/get_topic_list", params: {
      "page_index": pageNum,
      "page_size": pageSize,
      "keyword": keyWord ?? '',
      "order_by": orderBy ?? '',
      "class_code": classCode ?? '',
    }).then((value) {
      return SystemSceneListModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 自定义场景: 我的创建列表
  static Future<MyCreateListModel> getMyCreateCustomeSceneList(
    int pageNum,
    int pageSize,
    int status,
  ) {
    return Request.get("/community_scene_topics/get_my_created_topic_list",
        params: {
          "page_index": pageNum,
          "page_size": pageSize,
          "status": status,
        }).then((value) {
      return MyCreateListModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 自定义场景: 我的收藏列表
  static Future<MyCollectListModel> getMyCollectSceneList(
    int pageNum,
    int pageSize,
  ) {
    return Request.get("/community_scene_topics/get_my_collected_topic_list",
        params: {
          "page_index": pageNum,
          "page_size": pageSize,
        }).then((value) {
      return MyCollectListModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 自定义场景: 收藏和取消收藏
  static Future<EmptyModel> collectScene(String topicCode, bool isCollect) {
    final uriPath = isCollect
        ? "/community_scene_topics/collect_topic"
        : "/community_scene_topics/uncollect_topic";
    return Request.post(uriPath, data: {"topic_code": topicCode}).then((value) {
      return EmptyModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 自定义场景: 获取场景详情
  static Future<SceneDetailV2Model> getSceneDetailV2(String topicCode) {
    return Request.get("/community_scene_topics/get_topic_detail",
        params: {"topic_code": topicCode}).then((value) {
      return SceneDetailV2Model.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  static Future<EnglishNamePageModel> getCommonEnglishNamesPage(
      int pageNum, int pageSize, int gender) {
    return Request.get("/user_guide_page/get_common_english_names_page",
        params: {
          "page_index": pageNum,
          "page_size": pageSize,
          "gender": gender
        }).then((value) {
      return EnglishNamePageModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 自定义场景: 创建场景
  static Future<EmptyModel> createCustomScene(
    String myRole,
    String aiRole,
    String description,
    int isPrivate,
    String? imageUrl,
    String? target,
  ) {
    return Request.post("/community_scene_topics/create_topic", data: {
      "target": target ?? '',
      "my_role": myRole,
      "ai_role": aiRole,
      "description": description,
      "image_url": imageUrl ?? '',
      "is_private": isPrivate,
    }).then((value) {
      return EmptyModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 自定义场景: 随机生成场景描述
  static Future<OnlyStringModel> randomGenerateSceneDesc(
    String aiRole,
    String myRole,
    String? target,
  ) {
    return Request.post("/community_scene_topics/generate_description", data: {
      "ai_role": aiRole,
      "my_role": myRole,
      "target": target ?? '',
    }).then((value) {
      return OnlyStringModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 自定义场景: 随机生成场景
  static Future<RandomGenerateSceneModel> randomGenerateScene() {
    return Request.post("/community_scene_topics/generate_custom_scene",
        data: {}).then((value) {
      return RandomGenerateSceneModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 自定义场景: 随机生成场景目标
  static Future<OnlyStringModel> randomGenerateSceneTarget(
      String aiRole, String myRole, String desc) {
    return Request.post("/community_scene_topics/generate_custom_scene_target",
        data: {
          "ai_role": aiRole,
          "my_role": myRole,
          "description": desc,
        }).then((value) {
      return OnlyStringModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 自定义场景: 随机生成你的角色
  static Future<OnlyStringModel> randomGenerateMyRole() {
    return Request.post("/community_scene_topics/generate_custom_scene_my_role",
        data: {}).then((value) {
      return OnlyStringModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  // 自定义场景: 随机生成我AI角色
  static Future<OnlyStringModel> randomGenerateAiRole(String myRole) {
    return Request.post("/community_scene_topics/generate_custom_scene_ai_role",
        data: {"my_role": myRole}).then((value) {
      return OnlyStringModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 自定义场景: 删除场景
  static Future<EmptyModel> deleteCustomScene(int id) {
    return Request.post("/community_scene_topics/delete_topic",
        data: {"id": id}).then((value) {
      return EmptyModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 自定义场景: 更新场景
  static Future<EmptyModel> updateCustomScene(
    int id,
    String myRole,
    String aiRole,
    String description,
    int isPrivate,
    String? imageUrl,
    String? target,
  ) {
    return Request.post("/community_scene_topics/update_topic", data: {
      "id": id,
      "target": target ?? '',
      "my_role": myRole,
      "ai_role": aiRole,
      "description": description,
      "image_url": imageUrl ?? '',
      "is_private": isPrivate,
    }).then((value) {
      return EmptyModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 自定义场景: 获取我的创建列表状态分类
  static Future<MyCreateStatusListModel> getMyCreateListStatusCategory() {
    return Request.get("/community_scene_topics/get_topic_count_dict").then(
        (value) {
      return MyCreateStatusListModel.fromJson(value);
    }, onError: (_) {
      return false;
    });
  }

  /// 获取会员购买记录
  /// 返回最近购买会员的用户列表
  static Future<PurchaseRecordModel> getPurchaseRecord() {
    return Request.get("/promotion/get_purchase_record").then((value) {
      return PurchaseRecordModel.fromJson(value);
    }, onError: (_) {
      return const PurchaseRecordModel();
    });
  }

  /// 获取分享图片
  static Future<GetShareImageModel> getShareImageList(
    num conversationId,
    String conversationType,
  ) {
    return Request.post("/chat/generate_share_posters", data: {
      "conversation_id": conversationId,
      "conversation_type": conversationType,
    }).then((value) {
      return GetShareImageModel.fromJson(value);
    }, onError: (_) {
      return GetShareImageModel(data: [], code: '0');
    });
  }
}
