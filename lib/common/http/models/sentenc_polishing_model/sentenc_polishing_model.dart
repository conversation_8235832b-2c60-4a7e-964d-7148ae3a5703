import 'data.dart';

class SentencPolishingModel {
  String? code;
  Data? data;

  SentencPolishingModel({this.code, this.data});

  factory SentencPolishingModel.fromJson(Map<String, dynamic> json) {
    return SentencPolishingModel(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
