class Data {
  String? explanation;
  String? polishedSentence;

  Data({this.explanation, this.polishedSentence});

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        explanation: json['explanation'] as String?,
        polishedSentence: json['polished_sentence'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'explanation': explanation,
        'polished_sentence': polishedSentence,
      };
}
