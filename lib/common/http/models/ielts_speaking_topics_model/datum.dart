class Datum {
  String? chinese;
  String? date;
  String? english;
  int? id;
  String? imageUrl;
  String? partCategoryCode;
  String? topicCode;
  int? topicType;
  dynamic usageCount;

  Datum({
    this.chinese,
    this.date,
    this.english,
    this.id,
    this.imageUrl,
    this.partCategoryCode,
    this.topicCode,
    this.topicType,
    this.usageCount,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        chinese: json['chinese'] as String?,
        date: json['date'] as String?,
        english: json['english'] as String?,
        id: json['id'] as int?,
        imageUrl: json['image_url'] as String?,
        partCategoryCode: json['part_category_code'] as String?,
        topicCode: json['topic_code'] as String?,
        topicType: json['topic_type'] as int?,
        usageCount: json['usage_count'] as dynamic,
      );

  Map<String, dynamic> toJson() => {
        'chinese': chinese,
        'date': date,
        'english': english,
        'id': id,
        'image_url': imageUrl,
        'part_category_code': partCategoryCode,
        'topic_code': topicCode,
        'topic_type': topicType,
        'usage_count': usageCount,
      };
}
