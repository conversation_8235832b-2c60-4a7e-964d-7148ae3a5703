import 'datum.dart';

class IeltsSpeakingTopicsModel {
  String? code;
  List<Datum>? data;

  IeltsSpeakingTopicsModel({this.code, this.data});

  factory IeltsSpeakingTopicsModel.fromJson(Map<String, dynamic> json) {
    return IeltsSpeakingTopicsModel(
      code: json['code'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => Datum.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
