import 'data.dart';

class FileUploadModel {
  String? code;
  Data? data;

  FileUploadModel({this.code, this.data});

  factory FileUploadModel.fromJson(Map<String, dynamic> json) {
    return FileUploadModel(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
