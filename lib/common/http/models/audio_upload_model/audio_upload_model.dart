import 'data.dart';

class AudioUploadModel {
  String? code;
  Data? data;

  AudioUploadModel({this.code, this.data});

  factory AudioUploadModel.fromJson(Map<String, dynamic> json) {
    return AudioUploadModel(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
