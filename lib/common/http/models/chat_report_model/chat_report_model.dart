import 'data.dart';

class ChatReportModel {
  String? code;
  Data? data;

  ChatReportModel({this.code, this.data});

  factory ChatReportModel.fromJson(Map<String, dynamic> json) {
    return ChatReportModel(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
