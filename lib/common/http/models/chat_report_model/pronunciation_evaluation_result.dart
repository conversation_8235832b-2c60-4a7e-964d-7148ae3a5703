class PronunciationEvaluationResult {
  String? name;
  int? score;

  PronunciationEvaluationResult({this.name, this.score});

  factory PronunciationEvaluationResult.fromJson(Map<String, dynamic> json) {
    return PronunciationEvaluationResult(
      name: json['name'] as String?,
      score: json['score'] as int?,
    );
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'score': score,
      };
}
