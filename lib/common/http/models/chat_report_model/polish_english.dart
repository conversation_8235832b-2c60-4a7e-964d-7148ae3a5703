class PolishEnglish {
  final String? audioUrl;
  final String? explanation;
  final String? name;
  final String? polishedSentence;

  PolishEnglish(
      {required this.audioUrl,
      required this.explanation,
      required this.name,
      required this.polishedSentence});

  factory PolishEnglish.fromJson(Map<String, dynamic> json) {
    return PolishEnglish(
        audioUrl: json['audio_url'] as String?,
        explanation: json['explanation'] as String?,
        name: json['name'] as String?,
        polishedSentence: json['polished_sentence'] as String?);
  }
}
