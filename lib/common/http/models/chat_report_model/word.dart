import 'dart:ui';

class Word {
  int? pronAccuracy;
  int? pronFluency;
  int? rank;
  String? word;
  String? rightSymbol;

  Word(
      {this.pronAccuracy,
      this.pronFluency,
      this.rank,
      this.word,
      this.rightSymbol});

  factory Word.fromJson(Map<String, dynamic> json) => Word(
      pronAccuracy: json['pron_accuracy'] as int?,
      pronFluency: json['pron_fluency'] as int?,
      rank: json['rank'] as int?,
      word: json['word'] as String?,
      rightSymbol: json['right_symbol'] as String?);

  Color get color {
    if (rank == 1) {
      return const Color.fromARGB(255, 249, 50, 0);
    }
    if (rank == 2) {
      return const Color(0xFF061B1F);
    }
    if (rank == 3) {
      return const Color.fromARGB(255, 42, 201, 92);
    }
    return const Color(0xFF061B1F);
  }

  Map<String, dynamic> toJson() => {
        'pron_accuracy': pronAccuracy,
        'pron_fluency': pronFluency,
        'rank': rank,
        'word': word,
      };
}
