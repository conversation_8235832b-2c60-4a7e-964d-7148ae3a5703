import 'improvement_list.dart';
import 'pronunciation_evaluation_result.dart';

class Data {
  int? conversationRounds;
  String? encouragement;
  int? excellentRounds;
  String? goalChinese;
  String? goalEnglish;
  bool? hasFinishGoal;
  List<ImprovementList>? improvementList;
  int? improvementRounds;
  int? numCoreVocabularies;
  List<PronunciationEvaluationResult>? pronunciationEvaluationResults;
  String? radarChartUrl;
  int? totals;

  Data({
    this.conversationRounds,
    this.encouragement,
    this.excellentRounds,
    this.goalChinese,
    this.goalEnglish,
    this.hasFinishGoal,
    this.improvementList,
    this.improvementRounds,
    this.numCoreVocabularies,
    this.pronunciationEvaluationResults,
    this.radarChartUrl,
    this.totals,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        conversationRounds: json['conversation_rounds'] as int?,
        encouragement: json['encouragement'] as String?,
        excellentRounds: json['excellent_rounds'] as int?,
        goalChinese: json['goal_chinese'] as String?,
        goalEnglish: json['goal_english'] as String?,
        hasFinishGoal: json['has_finish_goal'] as bool?,
        improvementList: (json['improvement_list'] as List<dynamic>?)
            ?.map((e) => ImprovementList.fromJson(e as Map<String, dynamic>))
            .toList(),
        improvementRounds: json['improvement_rounds'] as int?,
        numCoreVocabularies: json['num_core_vocabularies'] as int?,
        pronunciationEvaluationResults:
            (json['pronunciation_evaluation_results'] as List<dynamic>?)
                ?.map((e) => PronunciationEvaluationResult.fromJson(
                    e as Map<String, dynamic>))
                .toList(),
        radarChartUrl: json['radar_chart_url'] as String?,
        totals: json['totals'] as int?,
      );

  Map<String, dynamic> toJson() => {
        'conversation_rounds': conversationRounds,
        'encouragement': encouragement,
        'excellent_rounds': excellentRounds,
        'goal_chinese': goalChinese,
        'goal_english': goalEnglish,
        'has_finish_goal': hasFinishGoal,
        'improvement_list': improvementList?.map((e) => e.toJson()).toList(),
        'improvement_rounds': improvementRounds,
        'num_core_vocabularies': numCoreVocabularies,
        'pronunciation_evaluation_results':
            pronunciationEvaluationResults?.map((e) => e.toJson()).toList(),
        'radar_chart_url': radarChartUrl,
        'totals': totals,
      };
}
