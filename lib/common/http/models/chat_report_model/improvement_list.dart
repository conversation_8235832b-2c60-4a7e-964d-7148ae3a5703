import 'polish_english.dart';
import 'word.dart';

class ImprovementList {
  String? amendment;
  String? audioResult;
  String? explanation;
  String? original;
  List<PolishEnglish>? polishEnglish;
  int? pronAccuracy;
  int? pronCompletion;
  int? pronFluency;
  int? speed;
  String? speedComment;
  int? suggestScore;
  String? suggestedComment;
  String? ukAudioUrl;
  String? usAudioUrl;
  List<Word>? words;
  //当前选中的单词
  PolishEnglish? _currentPolishedEnglish;

  PolishEnglish? get currentPolishEnglish {
    if (_currentPolishedEnglish != null) {
      return _currentPolishedEnglish;
    }
    if (polishEnglish == null || polishEnglish!.isEmpty) {
      return null;
    }
    return polishEnglish?.first;
  }

  set currentPolishEnglish(PolishEnglish? polishEnglish) {
    _currentPolishedEnglish = polishEnglish;
  }

  ImprovementList({
    this.amendment,
    this.audioResult,
    this.explanation,
    this.original,
    this.polishEnglish,
    this.pronAccuracy,
    this.pronCompletion,
    this.pronFluency,
    this.speed,
    this.speedComment,
    this.suggestScore,
    this.suggestedComment,
    this.ukAudioUrl,
    this.usAudioUrl,
    this.words,
  });

  factory ImprovementList.fromJson(Map<String, dynamic> json) {
    return ImprovementList(
      amendment: json['amendment'] as String?,
      audioResult: json['audio_result'] as String?,
      explanation: json['explanation'] as String?,
      original: json['original'] as String?,
      polishEnglish: (json['polish_english'] as List<dynamic>?)
          ?.map((e) => PolishEnglish.fromJson(e as Map<String, dynamic>))
          .toList(),
      pronAccuracy: json['pron_accuracy'] as int?,
      pronCompletion: json['pron_completion'] as int?,
      pronFluency: json['pron_fluency'] as int?,
      speed: json['speed'] as int?,
      speedComment: json['speed_comment'] as String?,
      suggestScore: json['suggest_score'] as int?,
      suggestedComment: json['suggested_comment'] as String?,
      ukAudioUrl: json['uk_audio_url'] as String?,
      usAudioUrl: json['us_audio_url'] as String?,
      words: (json['words'] as List<dynamic>?)
          ?.map((e) => Word.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'amendment': amendment,
        'audio_result': audioResult,
        'explanation': explanation,
        'original': original,
        'pron_accuracy': pronAccuracy,
        'pron_completion': pronCompletion,
        'pron_fluency': pronFluency,
        'speed': speed,
        'speed_comment': speedComment,
        'suggest_score': suggestScore,
        'suggested_comment': suggestedComment,
        'uk_audio_url': ukAudioUrl,
        'us_audio_url': usAudioUrl,
        'words': words?.map((e) => e.toJson()).toList(),
      };
}
