import './data.dart';

class ToolUserWritingPageModel {
  String? code;
  Data? data;

  ToolUserWritingPageModel({this.code, this.data});

  ToolUserWritingPageModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}
