class Recods {
  String? createTime;
  int? id;
  String? originalText;

  Recods({this.createTime, this.id, this.originalText});

  Recods.fromJson(Map<String, dynamic> json) {
    createTime = json['create_time'];
    id = json['id'];
    originalText = json['original_text'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['create_time'] = this.createTime;
    data['id'] = this.id;
    data['original_text'] = this.originalText;
    return data;
  }
}
