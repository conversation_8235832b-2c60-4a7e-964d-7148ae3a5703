class Data {
  String? secretId;
  String? secretKey;
  int? appId;

  Data({this.secretId, this.secretKey, this.appId});

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        secretId: json['secret_id'] as String?,
        secretKey: json['secret_key'] as String?,
        appId: int.parse(json['app_id']),
      );

  Map<String, dynamic> toJson() => {
        'secret_id': secretId,
        'secret_key': secretKey,
        'app_id': appId,
      };
}
