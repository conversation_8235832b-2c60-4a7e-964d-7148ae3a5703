import 'data.dart';

class SystemSecretModel {
  String? code;
  Data? data;

  SystemSecretModel({this.code, this.data});

  factory SystemSecretModel.fromJson(Map<String, dynamic> json) {
    return SystemSecretModel(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
