import 'list.dart';

class ReportList {
  String? groupBy;
  List<ListItem>? list;

  ReportList({this.groupBy, this.list});

  factory ReportList.fromJson(Map<String, dynamic> json) {
    var list = json['list']
        ?.map<ListItem>((e) => ListItem.fromJson(e as Map<String, dynamic>))
        .toList();
    return ReportList(
      groupBy: json['group_by'] as String?,
      list: list,
    );
  }
}
