class ListItem {
  int? conversationId;
  int? practiceId;
  int? practiceType;
  String? date;
  String? endTime;
  double? grammarScore;
  double? pronunciationScore;
  String? startTime;
  String? topicName;
  String? topicImgUrl;
  double? totalScore;
  double? vocabularyScore;
  double? fluencyScore;

  ListItem({
    this.conversationId,
    this.practiceId,
    this.practiceType,
    this.date,
    this.endTime,
    this.grammarScore,
    this.pronunciationScore,
    this.topicImgUrl,
    this.startTime,
    this.topicName,
    this.totalScore,
    this.vocabularyScore,
    this.fluencyScore,
  });

  factory ListItem.fromJson(Map<String, dynamic> json) => ListItem(
        conversationId: json['conversation_id'] as int?,
        practiceId: json['practice_id'] as int?,
        date: json['date'] as String?,
        endTime: json['end_time'] as String?,
        grammarScore: json['grammar_score'] != null
            ? double.tryParse(json['grammar_score'].toString())
            : null,
        pronunciationScore: json['pronunciation_score'] != null
            ? double.tryParse(json['pronunciation_score'].toString())
            : null,
        topicImgUrl: json['topic_img_url'] as String?,
        vocabularyScore: json['vocabulary_score'] != null
            ? double.tryParse(json['vocabulary_score'].toString())
            : null,
        fluencyScore: json['fluency_score'] != null
            ? double.tryParse(json['fluency_score'].toString())
            : null,
        startTime: json['start_time'] as String?,
        topicName: json['topic_name'] as String?,
        totalScore: json['total_score'] != null
            ? double.tryParse(json['total_score'].toString())
            : null,
        practiceType:
            json['practice_type'] != null ? json['practice_type'] as int : 0,
      );

  Map<String, dynamic> toJson() => {
        'conversation_id': conversationId,
        'practice_id': practiceId,
        'practice_type': practiceType,
        'date': date,
        'end_time': endTime,
        'grammar_score': grammarScore,
        'pronunciation_score': pronunciationScore,
        'start_time': startTime,
        'topic_name': topicName,
        'total_score': totalScore,
        'vocabulary_score': vocabularyScore,
      };
}
