import 'report_list.dart';

class Data {
  int? pageNum;
  int? pageSize;
  List<ReportList>? reportList;
  int? reportListLength;
  int? totalCount;
  int? totalPage;

  Data({
    this.pageNum,
    this.pageSize,
    this.reportList,
    this.reportListLength,
    this.totalCount,
    this.totalPage,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        pageNum: json['page_num'] as int?,
        pageSize: json['page_size'] as int?,
        reportList: (json['report_list'] as List<dynamic>?)
            ?.map((e) => ReportList.fromJson(e as Map<String, dynamic>))
            .toList(),
        reportListLength: json['report_list_length'] as int?,
        totalCount: json['total_count'] as int?,
        totalPage: json['total_page'] as int?,
      );
}
