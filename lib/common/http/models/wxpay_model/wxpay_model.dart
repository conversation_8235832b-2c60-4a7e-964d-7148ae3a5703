import 'data.dart';

class WxpayModel {
  String? code;
  Data? data;

  WxpayModel({this.code, this.data});

  factory WxpayModel.fromJson(Map<String, dynamic> json) => WxpayModel(
        code: json['code'] as String?,
        data: json['data'] == null
            ? null
            : Data.fromJson(json['data'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
