class Data {
  String? appid;
  String? partnerid;
  String? prepayid;
  String? package;
  String? nonceStr;
  String? timestamp;
  String? sign;

  Data({
    this.appid,
    this.partnerid,
    this.prepayid,
    this.package,
    this.nonceStr,
    this.timestamp,
    this.sign,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        appid: json['appid'] as String?,
        partnerid: json['partnerid'] as String?,
        prepayid: json['prepayid'] as String?,
        package: json['package'] as String?,
        nonceStr: json['nonceStr'] as String?,
        timestamp: json['timestamp'] as String?,
        sign: json['sign'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'appid': appid,
        'partnerid': partnerid,
        'prepayid': prepayid,
        'package': package,
        'nonceStr': nonceStr,
        'timestamp': timestamp,
        'sign': sign,
      };
}
