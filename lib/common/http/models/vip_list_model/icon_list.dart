class IconList {
  String? iconUrl;
  String? name;
  String? remark;

  IconList({this.iconUrl, this.name, this.remark});

  factory IconList.fromJson(Map<String, dynamic> json) => IconList(
        iconUrl: json['icon_url'] as String?,
        name: json['name'] as String?,
        remark: json['remark'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'icon_url': iconUrl,
        'name': name,
        'remark': remark,
      };
}
