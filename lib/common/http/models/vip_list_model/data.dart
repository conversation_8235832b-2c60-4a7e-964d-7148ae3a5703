import 'icon_list.dart';

class Data {
  List<IconList>? iconList;
  String? name;
  String? originalPrice;
  String? currency;
  String? discount;
  int? packageType;
  String? price;
  String? promotionalSlogan;
  String? purchased;
  String? remark;
  String? isRecommend;
  String? productId;

  Data({
    this.iconList,
    this.name,
    this.originalPrice,
    this.currency,
    this.discount,
    this.packageType,
    this.price,
    this.promotionalSlogan,
    this.isRecommend,
    this.productId,
    this.purchased,
    this.remark,
  });

  IconList? getIcon(int index) {
    if (iconList!.length > index) {
      return iconList![index];
    }
    return null;
  }

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        iconList: (json['icon_list'] as List<dynamic>?)
            ?.map((e) => IconList.fromJson(e as Map<String, dynamic>))
            .toList(),
        name: json['name'] as String?,
        originalPrice: json['original_price'] as String?,
        currency: json['currency'] as String?,
        discount: json['discount'] as String?,
        packageType: json['package_type'] as int?,
        price: json['price'] as String?,
        promotionalSlogan: json['promotional_slogan'] as String?,
        isRecommend: json['is_recommend'] as String?,
        productId: json['product_id'] as String?,
        purchased: json['purchased'] as String?,
        remark: json['remark'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'icon_list': iconList?.map((e) => e.toJson()).toList(),
        'name': name,
        'original_price': originalPrice,
        'currency': currency,
        'discount': discount,
        'package_type': packageType,
        'price': price,
        'promotional_slogan': promotionalSlogan,
        'is_recommend': isRecommend,
        'product_id': productId,
        'purchased': purchased,
        'remark': remark,
      };
}
