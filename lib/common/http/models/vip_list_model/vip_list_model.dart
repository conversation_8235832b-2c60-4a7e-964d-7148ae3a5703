import 'data.dart';

class VipListModel {
  String? code;
  List<Data>? data;

  VipListModel({this.code, this.data});

  factory VipListModel.fromJson(Map<String, dynamic> json) => VipListModel(
        code: json['code'] as String?,
        data: (json['data'] as List<dynamic>?)
            ?.map((e) => Data.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
