class QuickStudyResultModel {
  String? code;
  Data? data;

  QuickStudyResultModel({required this.code, required this.data});

  QuickStudyResultModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  String? practiceType;
  int? accuracy;
  String? chinese;
  String? description;
  String? english;
  List<Vocabularies>? vocabularies;

  Data(
      {this.practiceType,
      this.accuracy,
      this.chinese,
      this.description,
      this.english,
      this.vocabularies});

  Data.fromJson(Map<String, dynamic> json) {
    practiceType = json['practice_type'];
    accuracy = json['accuracy'];
    chinese = json['chinese'];
    description = json['description'];
    english = json['english'];
    if (json['vocabularies'] != null) {
      vocabularies = <Vocabularies>[];
      json['vocabularies'].forEach((v) {
        vocabularies!.add(Vocabularies.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['practice_type'] = this.practiceType;
    data['accuracy'] = this.accuracy;
    data['chinese'] = this.chinese;
    data['description'] = this.description;
    data['english'] = this.english;
    if (this.vocabularies != null) {
      data['vocabularies'] = this.vocabularies!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Vocabularies {
  int? id;
  String? chinese;
  String? word;
  bool? isCollected;
  Vocabularies({this.id, this.chinese, this.word});

  Vocabularies.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    chinese = json['chinese'];
    word = json['word'];
    isCollected = json['is_collected'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['chinese'] = this.chinese;
    data['word'] = this.word;
    data['is_collected'] = this.isCollected;
    return data;
  }
}
