import 'quick_study_practice_item.dart';

class QuickStudyQuestionModel {
  String? code;
  List<QuickStudyPracticeItem>? data;

  QuickStudyQuestionModel({required this.code, required this.data});

  factory QuickStudyQuestionModel.fromJson(Map<String, dynamic> json) =>
      QuickStudyQuestionModel(
        code: json['code'],
        // 只要第一个元素
        data: (json['data'] as List<dynamic>)
            .map((e) =>
                QuickStudyPracticeItem.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = code;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
