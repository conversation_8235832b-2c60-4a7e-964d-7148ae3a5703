class QuickStudyAnswerModel {
  String? code;
  bool? data;

  QuickStudyAnswerModel({this.code, this.data});

  QuickStudyAnswerModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['data'] = this.data;
    return data;
  }
}
