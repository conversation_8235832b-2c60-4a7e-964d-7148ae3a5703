class QuickStudyPracticeItem {
  int? answerIsCorrect;
  List<String>? answers;
  String? correctAnswer;
  String? correctAnswerTranslation;
  int? id;
  int? isFinish;
  int? practiceRoundId;
  List<String>? questionArray;
  List<String>? translationArray;
  String? word;

  QuickStudyPracticeItem(
      {this.answerIsCorrect,
      this.answers,
      this.correctAnswer,
      this.correctAnswerTranslation,
      this.id,
      this.isFinish,
      this.practiceRoundId,
      this.questionArray,
      this.translationArray,
      this.word});

  QuickStudyPracticeItem.fromJson(Map<String, dynamic> json) {
    answerIsCorrect = json['answer_is_correct'];
    answers = json['answers'] != null ? json['answers'].cast<String>() : [];
    correctAnswer = json['correct_answer'];
    correctAnswerTranslation = json['correct_answer_translation'] ?? '';
    id = json['id'];
    isFinish = json['is_finish'];
    practiceRoundId = json['practice_round_id'];
    questionArray = json['question_array'] != null ? json['question_array'].cast<String>() : [];
    translationArray = json['translation_array'] != null ? json['translation_array'].cast<String>() : [];
    word = json['word'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['answer_is_correct'] = this.answerIsCorrect;
    data['answers'] = this.answers;
    data['correct_answer'] = this.correctAnswer;
    data['correct_answer_translation'] = this.correctAnswerTranslation;
    data['id'] = this.id;
    data['is_finish'] = this.isFinish;
    data['practice_round_id'] = this.practiceRoundId;
    data['question_array'] = this.questionArray;
    data['translation_array'] = this.translationArray;
    data['word'] = this.word;
    return data;
  }
}
