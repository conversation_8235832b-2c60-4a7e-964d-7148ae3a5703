class ToolTranslateModel {
  String? code;
  String? data;

  ToolTranslateModel({this.code, this.data});

  ToolTranslateModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['data'] = this.data;
    return data;
  }
}
