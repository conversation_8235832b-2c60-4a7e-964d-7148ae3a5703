class ListItem {
  int? conversationId;
  String? date;
  String? endTime;
  int? grammarScore;
  int? pronunciationScore;
  String? startTime;
  String? topicName;
  int? totalScore;

  ListItem({
    this.conversationId,
    this.date,
    this.endTime,
    this.grammarScore,
    this.pronunciationScore,
    this.startTime,
    this.topicName,
    this.totalScore,
  });

  factory ListItem.fromJson(Map<String, dynamic> json) => ListItem(
        conversationId: json['conversation_id'] as int?,
        date: json['date'] as String?,
        endTime: json['end_time'] as String?,
        grammarScore: json['grammar_score'] as int?,
        pronunciationScore: json['pronunciation_score'] as int?,
        startTime: json['start_time'] as String?,
        topicName: json['topic_name'] as String?,
        totalScore: json['total_score'] as int?,
      );

  Map<String, dynamic> toJson() => {
        'conversation_id': conversationId,
        'date': date,
        'end_time': endTime,
        'grammar_score': grammarScore,
        'pronunciation_score': pronunciationScore,
        'start_time': startTime,
        'topic_name': topicName,
        'total_score': totalScore,
      };
}
