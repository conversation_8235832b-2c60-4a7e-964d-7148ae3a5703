import 'list.dart';

class IeltsSpeakingList {
  String? groupBy;
  List<ListItem>? list;

  IeltsSpeakingList({this.groupBy, this.list});

  factory IeltsSpeakingList.fromJson(Map<String, dynamic> json) {
    var list = json['list']
        ?.map<ListItem>((e) => ListItem.fromJson(e as Map<String, dynamic>))
        .toList();
    return IeltsSpeakingList(
      groupBy: json['group_by'] as String?,
      list: list,
    );
  }
}
