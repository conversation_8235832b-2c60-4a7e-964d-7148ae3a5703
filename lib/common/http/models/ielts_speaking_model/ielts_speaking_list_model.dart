import 'data.dart';

class IeltsSpeakingListModel {
  String? code;
  Data? data;

  IeltsSpeakingListModel({this.code, this.data});

  factory IeltsSpeakingListModel.fromJson(Map<String, dynamic> json) {
    return IeltsSpeakingListModel(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }
}
