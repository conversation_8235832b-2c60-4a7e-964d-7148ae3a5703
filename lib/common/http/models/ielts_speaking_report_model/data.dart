import 'explanation_of_score.dart';
import 'record.dart';

class Data {
  final int? coreVocabulariesCount;
  final String? encouragement;
  final ExplanationOfScore? explanationOfScore;
  final String? fluencyScore;
  final String? grammarScore;
  final String? pronunciationScore;
  final String? radarChartUrl;
  final List<List<Record>>? records;
  final List<String>? topicTitle;
  final List<String>? topicChinese;
  final List<String>? topicCodeArray;
  final List<int>? topicIdArray;
  final int? practiceType;
  final String? totalScore;
  final String? vocabularyScore;

  const Data({
    this.coreVocabulariesCount,
    this.encouragement,
    this.explanationOfScore,
    this.fluencyScore,
    this.grammarScore,
    this.pronunciationScore,
    this.radarChartUrl,
    this.records,
    this.topicTitle,
    this.topicChinese,
    this.topicIdArray,
    this.topicCodeArray,
    this.practiceType,
    this.totalScore,
    this.vocabularyScore,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
      coreVocabulariesCount: json['core_vocabularies_count'] as int?,
      practiceType: json['practice_type'] as int?,
      encouragement: json['encouragement'] as String?,
      explanationOfScore:
          ExplanationOfScore.fromJson(json['explanation_of_score']),
      fluencyScore: json['fluency_score'] as String?,
      grammarScore: json['grammar_score'] as String?,
      pronunciationScore: json['pronunciation_score'] as String?,
      radarChartUrl: json['radar_chart_url'] as String?,
      records: (json['records'] as List<dynamic>?)
          ?.map((e) => (e as List<dynamic>)
              .map((e) => Record.fromJson(e as Map<String, dynamic>))
              .toList())
          .toList(),
      topicTitle: (json['topic_title'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      topicChinese: (json['topic_chinese'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      topicCodeArray: (json['topic_code_array'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      topicIdArray: (json['topic_id_array'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList(),
      totalScore: json['total_score'] as String?,
      vocabularyScore: json['vocabulary_score'] as String?);

  Map<String, dynamic> toJson() => {
        'core_vocabularies_count': coreVocabulariesCount,
        'encouragement': encouragement,
        'explanation_of_score': explanationOfScore?.toJson(),
        'fluency_score': fluencyScore,
        'grammar_score': grammarScore,
        'pronunciation_score': pronunciationScore,
        'radar_chart_url': radarChartUrl,
        'records':
            records?.map((e) => e.map((e) => e.toJson()).toList()).toList(),
        'topic_title': topicTitle,
        'topic_chinese': topicChinese,
        'topic_id_array': topicIdArray,
        'topic_code_array': topicCodeArray,
        'total_score': totalScore,
        'vocabulary_score': vocabularyScore,
        'practice_type': practiceType
      };
}
