import 'data.dart';

class IeltsSpeakingReportModel {
  String? code;
  Data? data;

  IeltsSpeakingReportModel({this.code, this.data});

  factory IeltsSpeakingReportModel.fromJson(Map<String, dynamic> json) {
    return IeltsSpeakingReportModel(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
