import 'answer.dart';
import 'grammar_detection_results.dart';
import 'pronunciation_evaluation_results.dart';
import 'question.dart';
import 'vocabulary_evaluation_result.dart';

class Record {
  List<Answer>? answer;

  GrammarDetectionResults? grammarDetectionResults;

  int? partType;

  PronunciationEvaluationResults? pronunciationEvaluationResults;
  Question? question;

  String? questionAudioUrl;

  int? questionId;

  int? questionIndex;

  VocabularyEvaluationResult? vocabularyEvaluationResult;

  //当前选中的范文
  Answer? _currentAnswer;

  Answer? get currentAnswer {
    if (_currentAnswer != null) {
      return _currentAnswer;
    }
    if (answer == null || answer!.isEmpty) {
      return null;
    }
    return answer?.first;
  }

  set currentAnswer(Answer? answer) {
    _currentAnswer = answer;
  }

  Record({
    this.answer,
    this.grammarDetectionResults,
    this.partType,
    this.pronunciationEvaluationResults,
    this.question,
    this.questionAudioUrl,
    this.questionId,
    this.questionIndex,
    this.vocabularyEvaluationResult,
  });

  factory Record.fromJson(Map<String, dynamic> json) => Record(
      answer: (json['answer'] as List<dynamic>?)
          ?.map((e) => Answer.fromJson(e as Map<String, dynamic>))
          .toList(),
      grammarDetectionResults:
          GrammarDetectionResults.fromJson(json['grammar_detection_results']),
      partType: json['part_type'] as int?,
      pronunciationEvaluationResults: PronunciationEvaluationResults.fromJson(
          json['pronunciation_evaluation_results']),
      question: Question.fromJson(json['question']),
      questionAudioUrl: json['question_audio_url'] as String?,
      questionId: json['question_id'] as int?,
      questionIndex: json['question_index'] as int?,
      vocabularyEvaluationResult: VocabularyEvaluationResult.fromJson(
          json['vocabulary_evaluation_result']));

  Map<String, dynamic> toJson() => {
        'answer': answer?.map((e) => e.toJson()).toList(),
        'grammar_detection_results': grammarDetectionResults?.toJson(),
        'part_type': partType,
        'pronunciation_evaluation_results':
            pronunciationEvaluationResults?.toJson(),
        'question': question?.toJson(),
        'question_audio_url': questionAudioUrl,
        'question_id': questionId,
        'question_index': questionIndex,
        'vocabulary_evaluation_result': vocabularyEvaluationResult?.toJson()
      };
}
