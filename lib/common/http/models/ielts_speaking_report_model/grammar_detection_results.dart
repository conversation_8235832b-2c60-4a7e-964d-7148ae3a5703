class GrammarDetectionResults {
  final String? correction;
  final String? explanation;
  final bool? isCorrect;
  final String? text;
  final String? audioUrl;

  const GrammarDetectionResults({
    this.correction,
    this.explanation,
    this.isCorrect,
    this.text,
    this.audioUrl,
  });

  factory GrammarDetectionResults.fromJson(Map<String, dynamic> json) =>
      GrammarDetectionResults(
          correction: json['correction'] as String?,
          explanation: json['explanation'] as String?,
          isCorrect: json['is_correct'] as bool?,
          text: json['text'] as String?,
          audioUrl: json['audio_url'] as String?);

  Map<String, dynamic> toJson() => {
        'correction': correction,
        'explanation': explanation,
        'is_correct': isCorrect,
        'text': text,
        'audio_url': audioUrl
      };
}
