class ExplanationOfScore {
  String? fluencyScore;
  String? grammarScore;
  String? pronunciationScore;
  String? vocabularyScore;

  ExplanationOfScore(
      {this.fluencyScore,
      this.grammarScore,
      this.pronunciationScore,
      this.vocabularyScore});

  factory ExplanationOfScore.fromJson(Map<String, dynamic> json) {
    return ExplanationOfScore(
        fluencyScore: json['fluency_score'] as String?,
        grammarScore: json['grammar_score'] as String?,
        pronunciationScore: json['pronunciation_score'] as String?,
        vocabularyScore: json['vocabulary_score'] as String?);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['fluency_score'] = this.fluencyScore;
    data['grammar_score'] = this.grammarScore;
    data['pronunciation_score'] = this.pronunciationScore;
    data['vocabulary_score'] = this.vocabularyScore;
    return data;
  }
}
