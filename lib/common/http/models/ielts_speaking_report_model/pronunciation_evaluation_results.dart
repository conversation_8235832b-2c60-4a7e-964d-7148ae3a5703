import 'word.dart';

class PronunciationEvaluationResults {
  final String? audioUrl;
  final String? defaultAudioUrl;
  final int? pronAccuracy;
  final int? pronCompletion;
  final int? pronFluency;
  final int? speed;
  final String? speedComment;
  final String? suggestedComment;
  final int? suggestedScore;
  final String? text;
  final String? ukAudioUrl;
  final String? usAudioUrl;
  final List<Word>? words;

  const PronunciationEvaluationResults({
    this.audioUrl,
    this.defaultAudioUrl,
    this.pronAccuracy,
    this.pronCompletion,
    this.pronFluency,
    this.speed,
    this.speedComment,
    this.suggestedComment,
    this.suggestedScore,
    this.text,
    this.ukAudioUrl,
    this.usAudioUrl,
    this.words,
  });

  factory PronunciationEvaluationResults.fromJson(Map<String, dynamic> json) =>
      PronunciationEvaluationResults(
          audioUrl: json['audio_url'] as String?,
          defaultAudioUrl: json['default_audio_url'] as String?,
          pronAccuracy: json['pron_accuracy'] as int?,
          pronCompletion: json['pron_completion'] as int?,
          pronFluency: json['pron_fluency'] as int?,
          speed: json['speed'] as int?,
          speedComment: json['speed_comment'] as String?,
          suggestedComment: json['suggested_comment'] as String?,
          suggestedScore: json['suggested_score'] as int?,
          text: json['text'] as String?,
          ukAudioUrl: json['uk_audio_url'] as String?,
          usAudioUrl: json['us_audio_url'] as String?,
          words: (json['words'] as List<dynamic>?)
              ?.map((e) => Word.fromJson(e as Map<String, dynamic>))
              .toList());

  Map<String, dynamic> toJson() => {
        'audio_url': audioUrl,
        'default_audio_url': defaultAudioUrl,
        'pron_accuracy': pronAccuracy,
        'pron_completion': pronCompletion,
        'pron_fluency': pronFluency,
        'speed': speed,
        'speed_comment': speedComment,
        'suggested_comment': suggestedComment,
        'suggested_score': suggestedScore,
        'text': text,
        'uk_audio_url': ukAudioUrl,
        'us_audio_url': usAudioUrl,
        'words': words?.map((e) => e.toJson()).toList()
      };
}
