class Answer {
  final String? audioUrl;
  final String? chinese;
  final String? english;
  final String? label;

  const Answer({this.audioUrl, this.chinese, this.english, this.label});

  factory Answer.fromJson(Map<String, dynamic> json) => Answer(
      audioUrl: json['audio_url'] as String?,
      chinese: json['chinese'] as String?,
      english: json['english'] as String?,
      label: json['label'] as String?);

  Map<String, dynamic> toJson() => {
        'audio_url': audioUrl,
        'chinese': chinese,
        'english': english,
        'label': label
      };
}
