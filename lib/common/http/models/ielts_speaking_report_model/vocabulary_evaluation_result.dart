class VocabularyEvaluationResult {
  final String? explanation;
  final String? score;

  const VocabularyEvaluationResult({this.explanation, this.score});

  factory VocabularyEvaluationResult.fromJson(Map<String, dynamic> json) =>
      VocabularyEvaluationResult(
          explanation: json['explanation'] as String?,
          score: json['score'] as String?);

  Map<String, dynamic> toJson() => {'explanation': explanation, 'score': score};
}
