class Data {
  int? toolTryTimesCritiqueOfWriting;
  int? toolTryTimesEnglishWriting;
  int? toolTryTimesSimultaneousTranslation;
  int? toolTryTimesTranslated;

  Data(
      {this.toolTryTimesCritiqueOfWriting,
      this.toolTryTimesEnglishWriting,
      this.toolTryTimesSimultaneousTranslation,
      this.toolTryTimesTranslated});

  Data.fromJson(Map<String, dynamic> json) {
    toolTryTimesCritiqueOfWriting = json['tool_try_times_critique_of_writing'];
    toolTryTimesEnglishWriting = json['tool_try_times_english_writing'];
    toolTryTimesSimultaneousTranslation =
        json['tool_try_times_simultaneous_translation'];
    toolTryTimesTranslated = json['tool_try_times_translated'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['tool_try_times_critique_of_writing'] =
        this.toolTryTimesCritiqueOfWriting;
    data['tool_try_times_english_writing'] = this.toolTryTimesEnglishWriting;
    data['tool_try_times_simultaneous_translation'] =
        this.toolTryTimesSimultaneousTranslation;
    data['tool_try_times_translated'] = this.toolTryTimesTranslated;
    return data;
  }
}
