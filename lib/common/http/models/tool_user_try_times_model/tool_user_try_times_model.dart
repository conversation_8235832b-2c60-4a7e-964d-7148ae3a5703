import './data.dart';

class ToolUserTryTimesModel {
  String? code;
  Data? data;

  ToolUserTryTimesModel({this.code, this.data});

  ToolUserTryTimesModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}
