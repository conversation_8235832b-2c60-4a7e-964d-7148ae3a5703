class Data {
  List<String>? amendments;
  int? content;
  String? createTime;
  int? id;
  String? optimizeRst;
  String? originalText;
  int? sentence;
  int? structure;
  String? summary;
  int? vocabulary;

  Data(
      {this.amendments,
      this.content,
      this.createTime,
      this.id,
      this.optimizeRst,
      this.originalText,
      this.sentence,
      this.structure,
      this.summary,
      this.vocabulary});

  Data.fromJson(Map<String, dynamic> json) {
    amendments = json['amendments'].cast<String>();
    content = json['content'];
    createTime = json['create_time'];
    id = json['id'];
    optimizeRst = json['optimize_rst'];
    originalText = json['original_text'];
    sentence = json['sentence'];
    structure = json['structure'];
    summary = json['summary'];
    vocabulary = json['vocabulary'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['amendments'] = this.amendments;
    data['content'] = this.content;
    data['create_time'] = this.createTime;
    data['id'] = this.id;
    data['optimize_rst'] = this.optimizeRst;
    data['original_text'] = this.originalText;
    data['sentence'] = this.sentence;
    data['structure'] = this.structure;
    data['summary'] = this.summary;
    data['vocabulary'] = this.vocabulary;
    return data;
  }
}
