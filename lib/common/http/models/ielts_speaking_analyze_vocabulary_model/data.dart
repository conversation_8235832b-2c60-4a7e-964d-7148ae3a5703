class Data {
  String? score;
  String? explanation;

  Data({this.score, this.explanation});

  Data.fromJson(Map<String, dynamic> json) {
    score = json['score'];
    explanation = json['explanation'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['score'] = this.score;
    data['explanation'] = this.explanation;
    return data;
  }
}
