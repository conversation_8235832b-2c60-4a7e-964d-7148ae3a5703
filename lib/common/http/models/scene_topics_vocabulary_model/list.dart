class Vocabulary {
  dynamic additional;
  bool? collected;
  String? defaultPronunciationUrl;
  String? definition;
  int? id;
  int? index;
  bool? learned;
  String? score;
  String? ukPhonetic;
  String? ukPronunciationUrl;
  String? usPhonetic;
  String? usPronunciationUrl;
  String? userPronunciationUrl;
  String? vocabulary;

  Vocabulary({
    this.additional,
    this.collected,
    this.defaultPronunciationUrl,
    this.definition,
    this.id,
    this.index,
    this.learned,
    this.score,
    this.ukPhonetic,
    this.ukPronunciationUrl,
    this.usPhonetic,
    this.usPronunciationUrl,
    this.userPronunciationUrl,
    this.vocabulary,
  });

  factory Vocabulary.fromJson(Map<String, dynamic> json) => Vocabulary(
        additional: json['additional'] as dynamic,
        collected: json['collected'] as bool?,
        defaultPronunciationUrl: json['default_pronunciation_url'] as String?,
        definition: json['definition'] as String?,
        id: json['id'] as int?,
        index: json['index'] as int?,
        learned: json['learned'] as bool?,
        score: json['score'] as String?,
        ukPhonetic: json['uk_phonetic'] as String?,
        ukPronunciationUrl: json['uk_pronunciation_url'] as String?,
        usPhonetic: json['us_phonetic'] as String?,
        usPronunciationUrl: json['us_pronunciation_url'] as String?,
        userPronunciationUrl: json['user_pronunciation_url'] as String?,
        vocabulary: json['vocabulary'] as String?,
      );
}
