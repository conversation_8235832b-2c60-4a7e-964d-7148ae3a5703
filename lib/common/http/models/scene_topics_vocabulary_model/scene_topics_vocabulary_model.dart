import 'data.dart';

class SceneTopicsVocabularyModel {
  String? code;
  Data? data;

  SceneTopicsVocabularyModel({this.code, this.data});

  factory SceneTopicsVocabularyModel.fromJson(Map<String, dynamic> json) =>
      SceneTopicsVocabularyModel(
        code: json['code'] as String?,
        data: json['data'] == null
            ? null
            : Data.fromJson(json['data'] as Map<String, dynamic>),
      );
}
