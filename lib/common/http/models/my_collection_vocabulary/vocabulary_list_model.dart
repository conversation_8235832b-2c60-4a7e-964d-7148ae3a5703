import 'translation_list_model.dart';

class VocabularyList {
  String? additional;
  String? collectDate;
  String? defaultPronunciationUrl;
  String? definition;
  int? id;
  String? initialLetter;
  List<TranslationList>? translationList;
  String? ukPronunciationUrl;
  String? usPronunciationUrl;
  String? vocabulary;

  VocabularyList({
    this.additional,
    this.collectDate,
    this.defaultPronunciationUrl,
    this.definition,
    this.id,
    this.initialLetter,
    this.translationList,
    this.ukPronunciationUrl,
    this.usPronunciationUrl,
    this.vocabulary,
  });

  factory VocabularyList.fromJson(Map<String, dynamic> json) {
    return VocabularyList(
      additional: json['additional'] as String?,
      collectDate: json['collect_date'] as String?,
      defaultPronunciationUrl: json['default_pronunciation_url'] as String?,
      definition: json['definition'] as String?,
      id: json['id'] as int?,
      initialLetter: json['initial_letter'] as String?,
      translationList: (json['translation_list'] as List<dynamic>?)
          ?.map((e) => TranslationList.fromJson(e as Map<String, dynamic>))
          .toList(),
      ukPronunciationUrl: json['uk_pronunciation_url'] as String?,
      usPronunciationUrl: json['us_pronunciation_url'] as String?,
      vocabulary: json['vocabulary'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'additional': additional,
        'collect_date': collectDate,
        'default_pronunciation_url': defaultPronunciationUrl,
        'definition': definition,
        'id': id,
        'initial_letter': initialLetter,
        'translation_list': translationList?.map((e) => e.toJson()).toList(),
        'uk_pronunciation_url': ukPronunciationUrl,
        'us_pronunciation_url': usPronunciationUrl,
        'vocabulary': vocabulary,
      };
}
