import 'vocabulary_list_model.dart';

class VocabularyCategory {
  String? groupBy;
  List<VocabularyList>? vocabularyList;

  VocabularyCategory({this.groupBy, this.vocabularyList});

  factory VocabularyCategory.fromJson(Map<String, dynamic> json) =>
      VocabularyCategory(
        groupBy: json['group_by'] as String?,
        vocabularyList: (json['vocabulary_list'] as List<dynamic>?)
            ?.map((e) => VocabularyList.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'group_by': groupBy,
        'vocabulary_list': vocabularyList?.map((e) => e.toJson()).toList(),
      };
}
