import 'data.model.dart';

class MyCollectionVocabulary {
  String? code;
  Data? data;

  MyCollectionVocabulary({this.code, this.data});

  factory MyCollectionVocabulary.fromJson(Map<String, dynamic> json) {
    return MyCollectionVocabulary(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
