import 'vocabulary_model.dart';

class Data {
  int? count;
  int? pageNum;
  int? pageSize;
  int? totalPage;
  List<VocabularyCategory>? vocabularys;
  int? vocabularyListLength;

  Data({
    this.count,
    this.pageNum,
    this.pageSize,
    this.totalPage,
    this.vocabularys,
    this.vocabularyListLength,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        count: json['count'] as int?,
        pageNum: json['page_num'] as int?,
        pageSize: json['page_size'] as int?,
        totalPage: json['total_page'] as int?,
        vocabularys: (json['vocabulary_list'] as List<dynamic>?)
            ?.map((e) => VocabularyCategory.fromJson(e as Map<String, dynamic>))
            .toList(),
        vocabularyListLength: json['vocabulary_list_length'] as int?,
      );

  Map<String, dynamic> toJson() => {
        'count': count,
        'page_num': pageNum,
        'page_size': pageSize,
        'total_page': totalPage,
        'vocabulary_list': vocabularys?.map((e) => e.toJson()).toList(),
        'vocabulary_list_length': vocabularyListLength,
      };
}
