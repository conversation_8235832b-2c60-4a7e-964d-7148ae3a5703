import 'data.dart';

class PromotionConfig {
  String? code;
  Data? data;

  PromotionConfig({this.code, this.data});

  factory PromotionConfig.fromJson(Map<String, dynamic> json) {
    return PromotionConfig(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
