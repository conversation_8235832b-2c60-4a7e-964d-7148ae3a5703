class Data {
  dynamic bannerUrl;
  String? code;
  String? expirationDatetime;
  String? labelUrl;
  String? logoUrl;
  String? lowerLeftLabelUrl;
  String? name;
  int? remainingDays;
  int? remainingHours;
  int? remainingMinutes;
  int? remainingSeconds;
  int? remainingSecondsTotal;
  String? slogan;

  Data({
    this.bannerUrl,
    this.code,
    this.expirationDatetime,
    this.labelUrl,
    this.logoUrl,
    this.lowerLeftLabelUrl,
    this.name,
    this.remainingDays,
    this.remainingHours,
    this.remainingMinutes,
    this.remainingSeconds,
    this.remainingSecondsTotal,
    this.slogan,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        bannerUrl: json['banner_url'] as dynamic,
        code: json['code'] as String?,
        expirationDatetime: json['expiration_datetime'] as String?,
        labelUrl: json['label_url'] as String?,
        lowerLeftLabelUrl: json['lower_left_label_url'] as String?,
        name: json['name'] as String?,
        remainingDays: json['remaining_days'] as int?,
        remainingHours: json['remaining_hours'] as int?,
        remainingMinutes: json['remaining_minutes'] as int?,
        remainingSeconds: json['remaining_seconds'] as int?,
        remainingSecondsTotal: json['remaining_seconds_total'] as int?,
        slogan: json['slogan'] as String?,
        logoUrl: json['logo_url'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'banner_url': bannerUrl,
        'code': code,
        'expiration_datetime': expirationDatetime,
        'label_url': labelUrl,
        'lower_left_label_url': lowerLeftLabelUrl,
        'name': name,
        'remaining_days': remainingDays,
        'remaining_hours': remainingHours,
        'remaining_minutes': remainingMinutes,
        'remaining_seconds': remainingSeconds,
        'remaining_seconds_total': remainingSecondsTotal,
        'slogan': slogan,
        'logoUrl': logoUrl
      };
}
