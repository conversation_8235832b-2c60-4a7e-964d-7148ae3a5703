import 'data.dart';

class ChToEnAudioModel {
  String? code;
  Data? data;

  ChToEnAudioModel({this.code, this.data});

  factory ChToEnAudioModel.fromJson(Map<String, dynamic> json) {
    return ChToEnAudioModel(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
