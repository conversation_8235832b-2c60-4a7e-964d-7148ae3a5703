class Data {
  String? audioDuration;
  double? costTime;
  String? question;
  String? reply;
  String? replyAudioUrl;

  Data({
    this.audioDuration,
    this.costTime,
    this.question,
    this.reply,
    this.replyAudioUrl,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        // audioDuration: json['audio_duration'] as String?,
        costTime: (json['cost_time'] as num?)?.toDouble(),
        question: json['question'] as String?,
        reply: json['reply'] as String?,
        replyAudioUrl: json['reply_audio_url'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'audio_duration': audioDuration,
        'cost_time': costTime,
        'question': question,
        'reply': reply,
        'reply_audio_url': replyAudioUrl,
      };
}
