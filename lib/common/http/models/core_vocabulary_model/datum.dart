import 'translation_list.dart';

class Datum {
  String? additional;
  bool? collected;
  String? defaultPronunciationUrl;
  String? definition;
  List<TranslationList>? translationList;
  String? ukPhonetic;
  String? ukPronunciationUrl;
  String? usPhonetic;
  String? usPronunciationUrl;
  String? vocabulary;

  Datum({
    this.additional,
    this.collected,
    this.defaultPronunciationUrl,
    this.definition,
    this.translationList,
    this.ukPhonetic,
    this.ukPronunciationUrl,
    this.usPhonetic,
    this.usPronunciationUrl,
    this.vocabulary,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        additional: json['additional'] as String?,
        collected: json['collected'] as bool?,
        defaultPronunciationUrl: json['default_pronunciation_url'] as String?,
        definition: json['definition'] as String?,
        translationList: (json['translation_list'] as List<dynamic>?)
            ?.map((e) => TranslationList.fromJson(e as Map<String, dynamic>))
            .toList(),
        ukPhonetic: json['uk_phonetic'] as String?,
        ukPronunciationUrl: json['uk_pronunciation_url'] as String?,
        usPhonetic: json['us_phonetic'] as String?,
        usPronunciationUrl: json['us_pronunciation_url'] as String?,
        vocabulary: json['vocabulary'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'additional': additional,
        'collected': collected,
        'default_pronunciation_url': defaultPronunciationUrl,
        'definition': definition,
        'translation_list': translationList?.map((e) => e.toJson()).toList(),
        'uk_phonetic': ukPhonetic,
        'uk_pronunciation_url': ukPronunciationUrl,
        'us_phonetic': usPhonetic,
        'us_pronunciation_url': usPronunciationUrl,
        'vocabulary': vocabulary,
      };
}
