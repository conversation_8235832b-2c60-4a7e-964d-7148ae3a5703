import 'datum.dart';

class CoreVocabularyModel {
  String? code;
  List<Datum>? data;

  CoreVocabularyModel({this.code, this.data});

  factory CoreVocabularyModel.fromJson(Map<String, dynamic> json) {
    return CoreVocabularyModel(
      code: json['code'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => Datum.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
