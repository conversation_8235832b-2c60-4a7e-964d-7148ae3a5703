class Data {
  String? code;
  String? description;
  String? iconUrl;
  String? name;

  Data({this.code, this.description, this.iconUrl, this.name});

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        code: json['code'] as String?,
        description: json['description'] as String?,
        iconUrl: json['icon_url'] as String?,
        name: json['name'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'description': description,
        'icon_url': iconUrl,
        'name': name,
      };
}
