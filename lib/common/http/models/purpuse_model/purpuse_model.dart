import 'data.dart';

class PurpuseModel {
  String? code;
  List<Data>? data;

  PurpuseModel({this.code, this.data});

  factory PurpuseModel.fromJson(Map<String, dynamic> json) => PurpuseModel(
        code: json['code'] as String?,
        data: (json['data'] as List<dynamic>?)
            ?.map((e) => Data.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
