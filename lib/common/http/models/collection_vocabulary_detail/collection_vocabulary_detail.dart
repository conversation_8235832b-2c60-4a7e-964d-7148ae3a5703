import 'data.dart';

class CollectionVocabularyDetail {
  String? code;
  Data? data;

  CollectionVocabularyDetail({this.code, this.data});

  factory CollectionVocabularyDetail.fromJson(Map<String, dynamic> json) {
    return CollectionVocabularyDetail(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
