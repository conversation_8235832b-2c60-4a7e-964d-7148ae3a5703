class TranslationList {
  String? partOfSpeech;
  String? translation;

  TranslationList({this.partOfSpeech, this.translation});

  factory TranslationList.fromJson(Map<String, dynamic> json) {
    return TranslationList(
      partOfSpeech: json['part_of_speech'] as String?,
      translation: json['translation'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'part_of_speech': partOfSpeech,
        'translation': translation,
      };
}
