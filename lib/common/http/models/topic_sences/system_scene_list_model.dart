import 'datum.dart';

class SystemSceneListModel {
  String? code;
  Data? data;

  SystemSceneListModel({this.code, this.data});

  SystemSceneListModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  int? currentPage;
  List<Datum>? items;
  int? total;
  int? totalPages;

  Data({this.currentPage, this.items, this.total, this.totalPages});

  Data.fromJson(Map<String, dynamic> json) {
    currentPage = json['current_page'];

    if (json['items'] != null) {
      items = [];
      json['items'].forEach((v) {
        items!.add(Datum.fromJson(v));
      });
    }
    total = json['total'];
    totalPages = json['total_pages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['current_page'] = this.currentPage;
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    data['total'] = this.total;
    data['total_pages'] = this.totalPages;
    return data;
  }
}
