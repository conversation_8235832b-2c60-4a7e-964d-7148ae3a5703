import 'datum.dart';

class TopicSences {
  String? code;
  List<Datum>? data;

  TopicSences({this.code, this.data});

  factory TopicSences.fromJson(Map<String, dynamic> json) => TopicSences(
        code: json['code'] as String?,
        data: (json['data'] as List<dynamic>?)
            ?.map((e) => Datum.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
