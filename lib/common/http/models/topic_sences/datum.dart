class Datum {
  String? chinese;
  String? courseLevel;
  String? courseLevelName;
  String? english;
  int? free;
  String? imageUrl;
  String? target;
  String? topicCode;
  int? usageCount;

  Datum(
      {this.chinese,
      this.courseLevel,
      this.courseLevelName,
      this.english,
      this.free,
      this.imageUrl,
      this.target,
      this.topicCode,
      this.usageCount});

  Datum.fromJson(Map<String, dynamic> json) {
    chinese = json['chinese'];
    courseLevel = json['course_level'];
    courseLevelName = json['course_level_name'];
    english = json['english'];
    free = json['free'];
    imageUrl = json['image_url'];
    target = json['target'];
    topicCode = json['topic_code'];
    usageCount = json['usage_count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['chinese'] = this.chinese;
    data['course_level'] = this.courseLevel;
    data['course_level_name'] = this.courseLevelName;
    data['english'] = this.english;
    data['free'] = this.free;
    data['image_url'] = this.imageUrl;
    data['target'] = this.target;
    data['topic_code'] = this.topicCode;
    data['usage_count'] = this.usageCount;
    return data;
  }
}
