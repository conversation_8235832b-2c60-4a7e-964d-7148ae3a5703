import 'datum.dart';

class CarouselListModel {
  String? code;
  List<Datum>? data;

  CarouselListModel({this.code, this.data});

  factory CarouselListModel.fromJson(Map<String, dynamic> json) {
    return CarouselListModel(
      code: json['code'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => Datum.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
