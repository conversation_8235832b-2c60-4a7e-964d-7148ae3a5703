import 'phone_list.dart';

class Data {
  String? audioUrl;
  List<PhoneList>? phoneList;
  int? pronAccuracy;
  int? pronCompletion;
  int? pronFluency;
  int? suggestedScore;
  String? text;

  Data({
    this.audioUrl,
    this.phoneList,
    this.pronAccuracy,
    this.pronCompletion,
    this.pronFluency,
    this.suggestedScore,
    this.text,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        audioUrl: json['audio_url'] as String?,
        phoneList: (json['phone_list'] as List<dynamic>?)
            ?.map((e) => PhoneList.fromJson(e as Map<String, dynamic>))
            .toList(),
        pronAccuracy: json['pron_accuracy'] as int?,
        pronCompletion: json['pron_completion'] as int?,
        pronFluency: json['pron_fluency'] as int?,
        suggestedScore: json['suggested_score'] as int?,
        text: json['text'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'audio_url': audioUrl,
        'phone_list': phoneList?.map((e) => e.toJson()).toList(),
        'pron_accuracy': pronAccuracy,
        'pron_completion': pronCompletion,
        'pron_fluency': pronFluency,
        'suggested_score': suggestedScore,
        'text': text,
      };
}
