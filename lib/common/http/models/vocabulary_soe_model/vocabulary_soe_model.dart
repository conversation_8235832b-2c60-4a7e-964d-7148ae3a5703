import 'data.dart';

class VocabularySoeModel {
  String? code;
  Data? data;

  VocabularySoeModel({this.code, this.data});

  factory VocabularySoeModel.fromJson(Map<String, dynamic> json) {
    return VocabularySoeModel(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
