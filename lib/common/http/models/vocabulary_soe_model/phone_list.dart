class PhoneList {
  String? phone;
  int? pronAccuracy;
  int? rank;

  PhoneList({this.phone, this.pronAccuracy, this.rank});

  factory PhoneList.fromJson(Map<String, dynamic> json) => PhoneList(
        phone: json['phone'] as String?,
        pronAccuracy: json['pron_accuracy'] as int?,
        rank: json['rank'] as int?,
      );

  Map<String, dynamic> toJson() => {
        'phone': phone,
        'pron_accuracy': pronAccuracy,
        'rank': rank,
      };
}
