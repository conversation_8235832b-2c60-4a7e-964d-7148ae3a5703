import 'topic_type_list.dart';

class Datum {
  String? partCategoryCode;
  String? title;
  List<TopicTypeList>? topicTypeList;

  Datum({this.partCategoryCode, this.title, this.topicTypeList});

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        partCategoryCode: json['part_category_code'] as String?,
        title: json['title'] as String?,
        topicTypeList: (json['topic_type_list'] as List<dynamic>?)
            ?.map((e) => TopicTypeList.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'part_category_code': partCategoryCode,
        'title': title,
        'topic_type_list': topicTypeList?.map((e) => e.toJson()).toList(),
      };
}
