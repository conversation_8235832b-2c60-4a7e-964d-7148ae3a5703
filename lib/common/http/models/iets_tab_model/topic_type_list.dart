class TopicTypeList {
  int? topicCount;
  int? topicType;
  String? topicTypeName;

  TopicTypeList({this.topicCount, this.topicType, this.topicTypeName});

  factory TopicTypeList.fromJson(Map<String, dynamic> json) => TopicTypeList(
        topicCount: json['topic_count'] as int?,
        topicType: json['topic_type'] as int?,
        topicTypeName: json['topic_type_name'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'topic_count': topicCount,
        'topic_type': topicType,
        'topic_type_name': topicTypeName,
      };
}
