import 'datum.dart';

class IetsTabModel {
  String? code;
  List<Datum>? data;

  IetsTabModel({this.code, this.data});

  factory IetsTabModel.fromJson(Map<String, dynamic> json) => IetsTabModel(
        code: json['code'] as String?,
        data: (json['data'] as List<dynamic>?)
            ?.map((e) => Datum.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
