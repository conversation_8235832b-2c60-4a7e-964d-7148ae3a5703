import 'data.dart';

class RoleListModel {
  String? code;
  List<Data>? data;

  RoleListModel({this.code, this.data});

  factory RoleListModel.fromJson(Map<String, dynamic> json) => RoleListModel(
        code: json['code'] as String?,
        data: (json['data'] as List<dynamic>?)
            ?.map((e) => Data.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
