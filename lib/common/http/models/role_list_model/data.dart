class Data {
  num? age;
  String? ageGroup;
  String? audioUrl;
  String? bgImageUrl;
  String? characterType;
  String? conversationVideoUrl;
  String? englishVariation;
  num? gender;
  String? greeting;
  String? greetingCn;
  String? greetingVideoUrl;
  num? id;
  String? imageUrl;
  num? isRecommended;
  String? name;
  num? numberOfUsers;
  String? openaiTtsVoice;
  String? selfIntroduction;
  String? staticVideoUrl;
  String? voice;

  Data({
    this.age,
    this.ageGroup,
    this.audioUrl,
    this.bgImageUrl,
    this.characterType,
    this.conversationVideoUrl,
    this.englishVariation,
    this.gender,
    this.greeting,
    this.greetingCn,
    this.greetingVideoUrl,
    this.id,
    this.imageUrl,
    this.isRecommended,
    this.name,
    this.numberOfUsers,
    this.openaiTtsVoice,
    this.selfIntroduction,
    this.staticVideoUrl,
    this.voice,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        age: json['age'] as num?,
        ageGroup: json['age_group'] as String?,
        audioUrl: json['audio_url'] as String?,
        bgImageUrl: json['bg_image_url'] as String?,
        characterType: json['character_type'] as String?,
        conversationVideoUrl: json['conversation_video_url'] as String?,
        englishVariation: json['english_variation'] as String?,
        gender: json['gender'] as num?,
        greeting: json['greeting'] as String?,
        greetingCn: json['greeting_cn'] as String?,
        greetingVideoUrl: json['greeting_video_url'] as String?,
        id: json['id'] as num?,
        imageUrl: json['image_url'] as String?,
        isRecommended: json['is_recommended'] as num?,
        name: json['name'] as String?,
        numberOfUsers: json['number_of_users'] as num?,
        openaiTtsVoice: json['openai_tts_voice'] as String?,
        selfIntroduction: json['self_introduction'] as String?,
        staticVideoUrl: json['static_video_url'] as String?,
        voice: json['voice'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'age': age,
        'age_group': ageGroup,
        'audio_url': audioUrl,
        'bg_image_url': bgImageUrl,
        'character_type': characterType,
        'conversation_video_url': conversationVideoUrl,
        'english_variation': englishVariation,
        'gender': gender,
        'greeting': greeting,
        'greeting_cn': greetingCn,
        'greeting_video_url': greetingVideoUrl,
        'id': id,
        'image_url': imageUrl,
        'is_recommended': isRecommended,
        'name': name,
        'number_of_users': numberOfUsers,
        'openai_tts_voice': openaiTtsVoice,
        'self_introduction': selfIntroduction,
        'static_video_url': staticVideoUrl,
        'voice': voice,
      };
}
