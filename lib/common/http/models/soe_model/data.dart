import 'dart:convert';

import 'package:equatable/equatable.dart';

import 'word.dart';

class Data extends Equatable {
  final String? audioUrl;
  final int? pronAccuracy;
  final int? pronCompletion;
  final int? pronFluency;
  final int? suggestedScore;
  final String? text;
  final int? speed;
  final List<Word>? words;
  final String? speedComment;
  final String? usAudioUrl;
  final String? ukAudioUrl;
  final String? defaultAudioUrl;

  const Data(
      {this.audioUrl,
      this.pronAccuracy,
      this.pronCompletion,
      this.pronFluency,
      this.suggestedScore,
      this.text,
      this.words,
      this.speed,
      this.usAudioUrl,
      this.ukAudioUrl,
      this.defaultAudioUrl,
      this.speedComment});

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        audioUrl: json['audio_url'] as String?,
        pronAccuracy: json['pron_accuracy'] as int?,
        pronCompletion: json['pron_completion'] as int?,
        pronFluency: json['pron_fluency'] as int?,
        speed: json['speed'] as int?,
        speedComment: json['speed_comment'] as String?,
        usAudioUrl: json['us_audio_url'] as String?,
        ukAudioUrl: json['uk_audio_url'] as String?,
        defaultAudioUrl: json['default_audio_url'] as String?,
        suggestedScore: json['suggested_score'] as int?,
        text: json['text'] as String?,
        words: (json['words'] as List<dynamic>?)
            ?.map((e) => Word.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'audio_url': audioUrl,
        'pron_accuracy': pronAccuracy,
        'pron_completion': pronCompletion,
        'pron_fluency': pronFluency,
        'suggested_score': suggestedScore,
        'text': text,
        'words': words?.map((e) => e.toJson()).toList(),
      };

  Data copyWith(
      {String? audioUrl,
      int? pronAccuracy,
      int? pronCompletion,
      int? pronFluency,
      int? suggestedScore,
      String? text,
      String? speedComment,
      List<Word>? words,
      String? ukAudioUrl,
      String? usAudioUrl,
      int? speed}) {
    return Data(
        audioUrl: audioUrl ?? this.audioUrl,
        pronAccuracy: pronAccuracy ?? this.pronAccuracy,
        pronCompletion: pronCompletion ?? this.pronCompletion,
        pronFluency: pronFluency ?? this.pronFluency,
        suggestedScore: suggestedScore ?? this.suggestedScore,
        speed: speed ?? this.speed,
        text: text ?? this.text,
        words: words ?? this.words,
        ukAudioUrl: ukAudioUrl ?? this.ukAudioUrl,
        usAudioUrl: usAudioUrl ?? this.usAudioUrl,
        speedComment: speedComment ?? this.speedComment);
  }

  @override
  List<Object?> get props {
    return [
      audioUrl,
      pronAccuracy,
      pronCompletion,
      pronFluency,
      suggestedScore,
      text,
      words,
    ];
  }
}
