import 'package:equatable/equatable.dart';

import 'data.dart';

class SoeModel extends Equatable {
  final String? code;
  final Data? data;

  const SoeModel({this.code, this.data});

  factory SoeModel.fromJson(Map<String, dynamic> json) => SoeModel(
        code: json['code'] as String?,
        data: json['data'] == null
            ? null
            : Data.fromJson(json['data'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };

  SoeModel copyWith({
    String? code,
    Data? data,
  }) {
    return SoeModel(
      code: code ?? this.code,
      data: data ?? this.data,
    );
  }

  @override
  List<Object?> get props => [code, data];
}
