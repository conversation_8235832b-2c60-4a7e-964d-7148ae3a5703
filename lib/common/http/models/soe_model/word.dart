import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class Word extends Equatable {
  final int? pronAccuracy;
  final int? pronFluency;
  final int? rank;
  final String? word;
  final String? rightSymbol;
  const Word(
      {this.pronAccuracy,
      this.pronFluency,
      this.rank,
      this.word,
      this.rightSymbol});

  factory Word.fromJson(Map<String, dynamic> json) => Word(
        pronAccuracy: json['pron_accuracy'] as int?,
        pronFluency: json['pron_fluency'] as int?,
        rank: json['rank'] as int?,
        word: json['word'] as String?,
        rightSymbol: json['right_symbol'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'pron_accuracy': pronAccuracy,
        'pron_fluency': pronFluency,
        'rank': rank,
        'word': word,
        'right_symbol': rightSymbol,
      };

  Word copyWith({
    int? pronAccuracy,
    int? pronFluency,
    int? rank,
    String? word,
  }) {
    return Word(
      pronAccuracy: pronAccuracy ?? this.pronAccuracy,
      pronFluency: pronFluency ?? this.pronFluency,
      rank: rank ?? this.rank,
      word: word ?? this.word,
    );
  }

  @override
  List<Object?> get props =>
      [pronAccuracy, pronFluency, rank, word, rightSymbol];

  Color get color {
    if (rank == 1) {
      return const Color.fromARGB(255, 249, 50, 0);
    }
    if (rank == 2) {
      return const Color(0xFF061B1F);
    }
    if (rank == 3) {
      return const Color.fromARGB(255, 42, 201, 92);
    }
    return const Color(0xFF061B1F);
  }
}
