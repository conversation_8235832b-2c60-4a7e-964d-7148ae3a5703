import 'data.dart';

class IeltsTopicDetailModel {
  String? code;
  Data? data;

  IeltsTopicDetailModel({this.code, this.data});

  factory IeltsTopicDetailModel.fromJson(Map<String, dynamic> json) =>
      IeltsTopicDetailModel(
        code: json['code'] as String?,
        data: json['data'] == null
            ? null
            : Data.fromJson(json['data'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
