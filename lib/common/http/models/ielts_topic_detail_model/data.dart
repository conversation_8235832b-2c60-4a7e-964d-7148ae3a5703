class Data {
  String? chineseTopicName;
  String? partCategoryName;
  List<Questions>? questions;
  String? topicCode;
  int? topicId;
  String? topicName;

  Data(
      {this.chineseTopicName,
      this.partCategoryName,
      this.questions,
      this.topicCode,
      this.topicId,
      this.topicName});

  Data.fromJson(Map<String, dynamic> json) {
    chineseTopicName = json['chinese_topic_name'];
    partCategoryName = json['part_category_name'];
    if (json['questions'] != null) {
      questions = <Questions>[];
      json['questions'].forEach((v) {
        questions!.add(new Questions.fromJson(v));
      });
    }
    topicCode = json['topic_code'];
    topicId = json['topic_id'];
    topicName = json['topic_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['chinese_topic_name'] = this.chineseTopicName;
    data['part_category_name'] = this.partCategoryName;
    if (this.questions != null) {
      data['questions'] = this.questions!.map((v) => v.toJson()).toList();
    }
    data['topic_code'] = this.topicCode;
    data['topic_id'] = this.topicId;
    data['topic_name'] = this.topicName;
    return data;
  }
}

class Questions {
  List<ItemList>? list;
  String? partName;

  Questions({this.list, this.partName});

  Questions.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      list = <ItemList>[];
      json['list'].forEach((v) {
        list!.add(new ItemList.fromJson(v));
      });
    }
    partName = json['part_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.list != null) {
      data['list'] = this.list!.map((v) => v.toJson()).toList();
    }
    data['part_name'] = this.partName;
    return data;
  }
}

class ItemList {
  String? chinese;
  String? english;

  ItemList({this.chinese, this.english});

  ItemList.fromJson(Map<String, dynamic> json) {
    chinese = json['chinese'];
    english = json['english'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['chinese'] = this.chinese;
    data['english'] = this.english;
    return data;
  }
}
