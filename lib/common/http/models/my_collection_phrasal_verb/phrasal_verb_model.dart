import 'phrasal_verb_list_model.dart';

class PhrasalVerbCategory {
  String? groupBy;
  List<PhrasalVerbList>? phrasalVerbList;

  PhrasalVerbCategory({this.groupBy, this.phrasalVerbList});

  factory PhrasalVerbCategory.fromJson(Map<String, dynamic> json) =>
      PhrasalVerbCategory(
        groupBy: json['group_by'] as String?,
        phrasalVerbList: (json['phrasal_verb_list'] as List<dynamic>?)
            ?.map((e) => PhrasalVerbList.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'group_by': groupBy,
        'phrasal_verb_list': phrasalVerbList?.map((e) => e.toJson()).toList(),
      };
} 