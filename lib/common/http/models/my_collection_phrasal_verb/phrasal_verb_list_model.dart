class PhrasalVerbList {
  String? cefrLevel;
  String? collectDate;
  String? commonDefinition;
  String? defaultPronunciationUrl;
  int? id;
  String? initialLetter;
  String? phrasalVerb;
  String? ukPronunciationUrl;
  String? usPronunciationUrl;
  String? cognitiveTip;
  String? chineseExample;
  String? englishExample;

  PhrasalVerbList({
    this.cefrLevel,
    this.collectDate,
    this.commonDefinition,
    this.defaultPronunciationUrl,
    this.id,
    this.initialLetter,
    this.phrasalVerb,
    this.ukPronunciationUrl,
    this.usPronunciationUrl,
    this.cognitiveTip,
    this.chineseExample,
    this.englishExample,
  });

  factory PhrasalVerbList.fromJson(Map<String, dynamic> json) {
    return PhrasalVerbList(
      cefrLevel: json['cefr_level'] as String?,
      collectDate: json['collect_date'] as String?,
      commonDefinition: json['common_definition'] as String?,
      defaultPronunciationUrl: json['default_pronunciation_url'] as String?,
      id: json['id'] as int?,
      initialLetter: json['initial_letter'] as String?,
      phrasalVerb: json['phrasal_verb'] as String?,
      ukPronunciationUrl: json['uk_pronunciation_url'] as String?,
      usPronunciationUrl: json['us_pronunciation_url'] as String?,
      cognitiveTip: json['cognitive_tip'] as String?,
      chineseExample: json['chinese_example'] as String?,
      englishExample: json['english_example'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'cefr_level': cefrLevel,
        'collect_date': collectDate,
        'common_definition': commonDefinition,
        'default_pronunciation_url': defaultPronunciationUrl,
        'id': id,
        'initial_letter': initialLetter,
        'phrasal_verb': phrasalVerb,
        'uk_pronunciation_url': ukPronunciationUrl,
        'us_pronunciation_url': usPronunciationUrl,
        'cognitive_tip': cognitiveTip,
        'chinese_example': chineseExample,
        'english_example': englishExample,
      };
} 