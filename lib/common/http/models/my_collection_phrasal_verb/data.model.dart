import 'phrasal_verb_model.dart';

class Data {
  int? count;
  int? pageNum;
  int? pageSize;
  int? totalPage;
  List<PhrasalVerbCategory>? phrasalVerbList;
  int? phrasalVerbListLength;

  Data({
    this.count,
    this.pageNum,
    this.pageSize,
    this.totalPage,
    this.phrasalVerbList,
    this.phrasalVerbListLength,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        count: json['count'] as int?,
        pageNum: json['page_num'] as int?,
        pageSize: json['page_size'] as int?,
        totalPage: json['total_page'] as int?,
        phrasalVerbList: (json['phrasal_verb_list'] as List<dynamic>?)
            ?.map((e) => PhrasalVerbCategory.fromJson(e as Map<String, dynamic>))
            .toList(),
        phrasalVerbListLength: json['phrasal_verb_list_length'] as int?,
      );

  Map<String, dynamic> toJson() => {
        'count': count,
        'page_num': pageNum,
        'page_size': pageSize,
        'total_page': totalPage,
        'phrasal_verb_list': phrasalVerbList?.map((e) => e.toJson()).toList(),
        'phrasal_verb_list_length': phrasalVerbListLength,
      };
} 