import 'data.model.dart';

class MyCollectionPhrasalVerb {
  String? code;
  Data? data;

  MyCollectionPhrasalVerb({this.code, this.data});

  factory MyCollectionPhrasalVerb.fromJson(Map<String, dynamic> json) {
    return MyCollectionPhrasalVerb(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
} 