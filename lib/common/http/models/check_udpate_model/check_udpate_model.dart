import 'data.dart';

class CheckUdpateModel {
  String? code;
  Data? data;

  CheckUdpateModel({this.code, this.data});

  factory CheckUdpateModel.fromJson(Map<String, dynamic> json) {
    return CheckUdpateModel(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
