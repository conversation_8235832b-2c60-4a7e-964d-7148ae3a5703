class Data {
  String? appDownloadUrl;
  bool? forceUpdate;
  String? latestVersion;
  int? latestVersionNumber;
  bool? needUpdate;
  String? releaseTime;
  String? updateContent;

  Data({
    this.appDownloadUrl,
    this.forceUpdate,
    this.latestVersion,
    this.latestVersionNumber,
    this.needUpdate,
    this.releaseTime,
    this.updateContent,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        appDownloadUrl: json['app_download_url'] as String?,
        forceUpdate: json['force_update'] as bool?,
        latestVersion: json['latest_version'] as String?,
        latestVersionNumber: json['latest_version_number'] as int?,
        needUpdate: json['need_update'] as bool?,
        releaseTime: json['release_time'] as String?,
        updateContent: json['update_content'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'app_download_url': appDownloadUrl,
        'force_update': forceUpdate,
        'latest_version': latestVersion,
        'latest_version_number': latestVersionNumber,
        'need_update': needUpdate,
        'release_time': releaseTime,
        'update_content': updateContent,
      };
}
