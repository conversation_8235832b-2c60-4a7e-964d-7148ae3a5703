// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'data.dart';

class UserInfoModel {
  String? code;
  Data? data;

  UserInfoModel({this.code, this.data});

  @override
  String toString() => 'UserInfoModel(code: $code, data: $data)';

  factory UserInfoModel.fromMap(Map<String, dynamic> data) => UserInfoModel(
        code: data['code'] as String?,
        data: data['data'] == null
            ? null
            : Data.fromMap(data['data'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toMap() => {
        'code': code,
        'data': data?.toMap(),
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [UserInfoModel].
  factory UserInfoModel.fromJson(String data) {
    return UserInfoModel.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [UserInfoModel] to a JSON string.
  String toJson() => json.encode(toMap());

  @override
  bool operator ==(covariant UserInfoModel other) {
    if (identical(this, other)) return true;

    return other.code == code && other.data == data;
  }

  @override
  int get hashCode => code.hashCode ^ data.hashCode;
}
