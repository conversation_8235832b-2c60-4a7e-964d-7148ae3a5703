// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'user_settings.dart';

class Data {
  String? accessToken;
  String? accountNo;
  num? availableDays;
  num? availableMinutes;
  String? avatarUrl;
  num? complimentaryMinutes;
  num? daysOfStudy;
  String? englishLevelChineseAbbreviation;
  bool? enterTheProcess;
  bool? isVisitor;
  String? englishLevel;
  dynamic expirationDate;
  num? hasFinishTest;
  num? memberType;
  String? membershipLevel;
  String? nickname;
  String? phone;
  String? promotionCode;
  String? promotionQrcodeUrl;
  num? usedMinutes;
  num? userId;
  UserSettings? userSettings;
  int? sex;

  Data({
    this.accessToken,
    this.accountNo,
    this.availableDays,
    this.availableMinutes,
    this.avatarUrl,
    this.complimentaryMinutes,
    this.daysOfStudy,
    this.englishLevelChineseAbbreviation,
    this.enterTheProcess,
    this.isVisitor,
    this.englishLevel,
    this.expirationDate,
    this.hasFinishTest,
    this.memberType,
    this.membershipLevel,
    this.nickname,
    this.phone,
    this.promotionCode,
    this.promotionQrcodeUrl,
    this.usedMinutes,
    this.userId,
    this.userSettings,
    this.sex
  });

  @override
  String toString() {
    return 'Data(accessToken: $accessToken, accountNo: $accountNo, availableDays: $availableDays, availableMinutes: $availableMinutes, avatarUrl: $avatarUrl, complimentaryMinutes: $complimentaryMinutes, daysOfStudy: $daysOfStudy, englishLevelChineseAbbreviation: $englishLevelChineseAbbreviation, expirationDate: $expirationDate, hasFinishTest: $hasFinishTest, memberType: $memberType, membershipLevel: $membershipLevel, nickname: $nickname, phone: $phone, promotionCode: $promotionCode, promotionQrcodeUrl: $promotionQrcodeUrl, usedMinutes: $usedMinutes, userId: $userId, userSettings: $userSettings,enterTheProcess:$enterTheProcess,isVisitor:$isVisitor,sex:$sex)';
  }

  factory Data.fromMap(Map<String, dynamic> data) => Data(
        accessToken: data['access_token'] as String?,
        accountNo: data['account_no'] as String?,
        availableDays: data['available_days'] as num?,
        availableMinutes: data['available_minutes'] as num?,
        avatarUrl: data['avatar_url'] as String?,
        complimentaryMinutes: data['complimentary_minutes'] as num?,
        daysOfStudy: data['days_of_study'] as num?,
        englishLevelChineseAbbreviation:
            data['english_level_chinese_abbreviation'] as String?,
        englishLevel: data['english_level'] as String?,
        expirationDate: data['expiration_date'] as dynamic,
        hasFinishTest: data['has_finish_test'] as num?,
        memberType: data['member_type'] as num?,
        membershipLevel: data['membership_level'] as String?,
        nickname: data['nickname'] as String?,
        phone: data['phone'] as String?,
        promotionCode: data['promotion_code'] as String?,
        promotionQrcodeUrl: data['promotion_qrcode_url'] as String?,
        usedMinutes: data['used_minutes'] as num?,
        enterTheProcess: data['enter_the_process'] as bool?,
        isVisitor: data['is_visitor'] as bool?,
        userId: data['user_id'] as num?,
        userSettings: data['user_settings'] == null
            ? null
            : UserSettings.fromMap(
                data['user_settings'] as Map<String, dynamic>),
        sex: data['sex'] as int?,
      );

  Map<String, dynamic> toMap() => {
        'access_token': accessToken,
        'account_no': accountNo,
        'available_days': availableDays,
        'available_minutes': availableMinutes,
        'avatar_url': avatarUrl,
        'complimentary_minutes': complimentaryMinutes,
        'days_of_study': daysOfStudy,
        'english_level_chinese_abbreviation': englishLevelChineseAbbreviation,
        'english_level': englishLevel,
        'expiration_date': expirationDate,
        'has_finish_test': hasFinishTest,
        'member_type': memberType,
        'membership_level': membershipLevel,
        'enter_the_process': enterTheProcess,
        'is_visitor': isVisitor,
        'nickname': nickname,
        'phone': phone,
        'promotion_code': promotionCode,
        'promotion_qrcode_url': promotionQrcodeUrl,
        'used_minutes': usedMinutes,
        'user_id': userId,
        'user_settings': userSettings?.toMap(),
        'sex':sex

      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [Data].
  factory Data.fromJson(String data) {
    return Data.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [Data] to a JSON string.
  String toJson() => json.encode(toMap());

  @override
  bool operator ==(covariant Data other) {
    if (identical(this, other)) return true;

    return other.accessToken == accessToken &&
        other.accountNo == accountNo &&
        other.availableDays == availableDays &&
        other.availableMinutes == availableMinutes &&
        other.avatarUrl == avatarUrl &&
        other.complimentaryMinutes == complimentaryMinutes &&
        other.daysOfStudy == daysOfStudy &&
        other.englishLevelChineseAbbreviation ==
            englishLevelChineseAbbreviation &&
        other.englishLevel == englishLevel &&
        other.expirationDate == expirationDate &&
        other.hasFinishTest == hasFinishTest &&
        other.memberType == memberType &&
        other.membershipLevel == membershipLevel &&
        other.nickname == nickname &&
        other.phone == phone &&
        other.promotionCode == promotionCode &&
        other.promotionQrcodeUrl == promotionQrcodeUrl &&
        other.usedMinutes == usedMinutes &&
        other.userId == userId &&
        other.userSettings == userSettings &&
        other.sex == sex;
  }

  @override
  int get hashCode {
    return accessToken.hashCode ^
        accountNo.hashCode ^
        availableDays.hashCode ^
        availableMinutes.hashCode ^
        avatarUrl.hashCode ^
        complimentaryMinutes.hashCode ^
        daysOfStudy.hashCode ^
        englishLevelChineseAbbreviation.hashCode ^
        englishLevel.hashCode ^
        expirationDate.hashCode ^
        hasFinishTest.hashCode ^
        memberType.hashCode ^
        membershipLevel.hashCode ^
        nickname.hashCode ^
        phone.hashCode ^
        promotionCode.hashCode ^
        promotionQrcodeUrl.hashCode ^
        usedMinutes.hashCode ^
        userId.hashCode ^
        userSettings.hashCode ^
        sex.hashCode;
  }
}
