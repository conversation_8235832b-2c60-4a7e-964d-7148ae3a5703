import 'datum.dart';

class EnglishTapModel {
  String? code;
  List<Datum>? data;

  EnglishTapModel({this.code, this.data});

  factory EnglishTapModel.fromJson(Map<String, dynamic> json) {
    return EnglishTapModel(
      code: json['code'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => Datum.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
