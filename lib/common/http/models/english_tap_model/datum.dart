import 'package:flutter_app_kouyu/common/http/loading_status.dart';
import 'package:flutter_app_kouyu/common/http/models/sentenc_polishing_model/sentenc_polishing_model.dart';

class Datum {
  String? name;
  int? type;
  SentencPolishingModel? sentencPolishingModel;
  LoadingStatus loadingStatus = LoadingStatus.idle;

  Datum({this.name, this.type});

  factory Datum.fromJson(Map<String, dynamic> json) => Da<PERSON>(
        name: json['name'] as String?,
        type: json['type'] as int?,
      );

  Map<String, dynamic> toJson() => {
        'name': name,
        'type': type,
      };
}
