class Data {
  String? alexAudioUrl;
  String? aliceAudioUrl;
  String? annaAudioUrl;
  String? jarvisAudioUrl;
  String? audioUrl;
  String? chinese;
  String? english;
  String? label;
  bool? collected;

  Data(
      {this.alexAudioUrl,
      this.aliceAudioUrl,
      this.annaAudioUrl,
      this.jarvisAudioUrl,
      this.audioUrl,
      this.chinese,
      this.english,
      this.label,
      this.collected});

  Data.fromJson(Map<String, dynamic> json) {
    alexAudioUrl = json['Alex_audio_url'];
    aliceAudioUrl = json['Alice_audio_url'];
    annaAudioUrl = json['Anna_audio_url'];
    jarvisAudioUrl = json['Jarvis_audio_url'];
    audioUrl = json['audio_url'];
    chinese = json['chinese'];
    english = json['english'];
    label = json['label'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['Alex_audio_url'] = this.alexAudioUrl;
    data['Alice_audio_url'] = this.aliceAudioUrl;
    data['Anna_audio_url'] = this.annaAudioUrl;
    data['Jarvis_audio_url'] = this.jarvisAudioUrl;
    data['audio_url'] = this.audioUrl;
    data['chinese'] = this.chinese;
    data['english'] = this.english;
    data['label'] = this.label;
    return data;
  }
}
