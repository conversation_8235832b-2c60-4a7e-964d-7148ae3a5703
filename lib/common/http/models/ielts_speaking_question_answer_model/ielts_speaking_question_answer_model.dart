import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_question_answer_model/data.dart';

class IeltsSpeakingQuestionAnswerModel {
  String? code;
  List<Data>? data;

  IeltsSpeakingQuestionAnswerModel({this.code, this.data});

  IeltsSpeakingQuestionAnswerModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
