import 'part.dart';

class Data {
  Part? part1;
  Part? part2;
  Part? part3;

  Data({this.part1, this.part2, this.part3});

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        part1: json['Part1'] == null
            ? null
            : Part.fromJson(json['Part1'] as Map<String, dynamic>),
        part2: json['Part2'] == null
            ? null
            : Part.fromJson(json['Part2'] as Map<String, dynamic>),
        part3: json['Part3'] == null
            ? null
            : Part.fromJson(json['Part3'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'Part1': part1?.toJson(),
        'Part2': part2?.toJson(),
        'Part3': part3?.toJson(),
      };
}
