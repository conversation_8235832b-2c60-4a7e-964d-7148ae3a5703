class Part {
  String? example;
  String? headImageUrl;
  String? partContent;
  String? step1;
  String? step2;
  String? step3;
  List<String>? suggestStep;
  String? talkTime;
  String? title;

  Part({
    this.example,
    this.headImageUrl,
    this.partContent,
    this.step1,
    this.step2,
    this.step3,
    this.suggestStep,
    this.talkTime,
    this.title,
  });

  factory Part.fromJson(Map<String, dynamic> json) => Part(
        example: json['example'] as String?,
        headImageUrl: json['head_image_url'] as String?,
        partContent: json['part_content'] as String?,
        step1: json['step1'] as String?,
        step2: json['step2'] as String?,
        step3: json['step3'] as String?,
        suggestStep: (json['suggest_step'] as List<dynamic>).cast<String>(),
        talkTime: json['talk_time'] as String?,
        title: json['title'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'example': example,
        'head_image_url': headImageUrl,
        'part_content': partContent,
        'step1': step1,
        'step2': step2,
        'step3': step3,
        'suggest_step': suggestStep,
        'talk_time': talkTime,
        'title': title,
      };
}
