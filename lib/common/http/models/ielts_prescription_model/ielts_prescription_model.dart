import 'data.dart';

class IeltsPrescriptionModel {
  Data? data;

  IeltsPrescriptionModel({this.data});

  factory IeltsPrescriptionModel.fromJson(Map<String, dynamic> json) {
    return IeltsPrescriptionModel(
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'data': data?.toJson(),
      };
}
