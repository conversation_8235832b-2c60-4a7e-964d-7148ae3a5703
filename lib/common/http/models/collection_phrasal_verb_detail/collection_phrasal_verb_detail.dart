import 'data.dart';

class CollectionPhrasalVerbDetail {
  String? code;
  Data? data;

  CollectionPhrasalVerbDetail({this.code, this.data});

  factory CollectionPhrasalVerbDetail.fromJson(Map<String, dynamic> json) {
    return CollectionPhrasalVerbDetail(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
} 