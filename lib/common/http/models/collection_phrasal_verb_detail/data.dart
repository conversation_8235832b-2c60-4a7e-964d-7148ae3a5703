class Data {
  String? chineseExample;
  String? cognitiveTip;
  String? commonDefinition;
  String? englishExample;
  int? id;
  String? phrasalVerb;
  String? ukPronUrl;
  String? usPronUrl;
  String? score;
  String? userPronunciationUrl;

  Data({
    this.chineseExample,
    this.cognitiveTip,
    this.commonDefinition,
    this.englishExample,
    this.id,
    this.phrasalVerb,
    this.ukPronUrl,
    this.usPronUrl
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        chineseExample: json['chinese_example'] as String?,
        cognitiveTip: json['cognitive_tip'] as String?,
        commonDefinition: json['common_definition'] as String?,
        englishExample: json['english_example'] as String?,
        id: json['id'] as int?,
        phrasalVerb: json['phrasal_verb'] as String?,
        ukPronUrl: json['uk_pron_url'] as String?,
        usPronUrl: json['us_pron_url'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'chinese_example': chineseExample,
        'cognitive_tip': cognitiveTip,
        'common_definition': commonDefinition,
        'english_example': englishExample,
        'id': id,
        'phrasal_verb': phrasalVerb,
        'uk_pron_url': ukPronUrl,
        'us_pron_url': usPronUrl,
      };
} 