class Datum {
  String? code;
  int? completedCount;
  String? description;
  String? englishName;
  String? imageUrl;
  int? isCompleted;
  String? name;
  int? totalCount;

  Datum({
    this.code,
    this.completedCount,
    this.description,
    this.englishName,
    this.imageUrl,
    this.isCompleted,
    this.name,
    this.totalCount,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        code: json['code'] as String?,
        completedCount: json['completed_count'] as int?,
        description: json['description'] as String?,
        englishName: json['english_name'] as String?,
        imageUrl: json['image_url'] as String?,
        isCompleted: json['is_completed'] as int?,
        name: json['name'] as String?,
        totalCount: json['total_count'] as int?,
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'completed_count': completedCount,
        'description': description,
        'english_name': englishName,
        'image_url': imageUrl,
        'is_completed': isCompleted,
        'name': name,
        'total_count': totalCount,
      };
}
