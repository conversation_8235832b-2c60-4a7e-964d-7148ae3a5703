import 'datum.dart';

class StudyProgressListModel {
  String? code;
  List<Datum>? data;

  StudyProgressListModel({this.code, this.data});

  factory StudyProgressListModel.fromJson(Map<String, dynamic> json) {
    return StudyProgressListModel(
      code: json['code'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => Datum.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
