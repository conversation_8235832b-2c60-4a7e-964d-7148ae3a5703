import 'datum.dart';

class SenceTopicsClass {
  String? code;
  List<Datum>? data;

  SenceTopicsClass({this.code, this.data});

  factory SenceTopicsClass.fromJson(Map<String, dynamic> json) {
    return SenceTopicsClass(
      code: json['code'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => Datum.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
