class Datum {
  String? code;
  String? description;
  int? id;
  String? name;

  Datum({this.code, this.description, this.id, this.name});

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        code: json['code'] as String?,
        description: json['description'] as String?,
        id: json['id'] as int?,
        name: json['name'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'description': description,
        'id': id,
        'name': name,
      };
}
