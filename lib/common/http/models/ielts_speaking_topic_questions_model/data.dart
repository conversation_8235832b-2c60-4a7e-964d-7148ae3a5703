import 'question.dart';

class Data {
  String? chineseTopicName;
  String? partCategoryName;
  List<Question>? questions;
  String? topicCode;
  int? topicId;
  String? topicName;

  Data(
      {this.chineseTopicName,
      this.partCategoryName,
      this.questions,
      this.topicCode,
      this.topicId,
      this.topicName});

  Data.fromJson(Map<String, dynamic> json) {
    chineseTopicName = json['chinese_topic_name'];
    partCategoryName = json['part_category_name'];
    if (json['questions'] != null) {
      questions = <Question>[];
      json['questions'].forEach((v) {
        questions!.add(new Question.fromJson(v));
      });
    }
    topicCode = json['topic_code'];
    topicId = json['topic_id'];
    topicName = json['topic_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['chinese_topic_name'] = this.chineseTopicName;
    data['part_category_name'] = this.partCategoryName;
    if (this.questions != null) {
      data['questions'] = this.questions!.map((v) => v.toJson()).toList();
    }
    data['topic_code'] = this.topicCode;
    data['topic_id'] = this.topicId;
    data['topic_name'] = this.topicName;
    return data;
  }
}
