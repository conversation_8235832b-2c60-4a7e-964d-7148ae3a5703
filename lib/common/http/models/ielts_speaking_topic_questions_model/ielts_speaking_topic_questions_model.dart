import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_topic_questions_model/data.dart';

class IeltsSpeakingTopicQuestionModel {
  String? code;
  Data? data;

  IeltsSpeakingTopicQuestionModel({this.code, this.data});

  IeltsSpeakingTopicQuestionModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}
