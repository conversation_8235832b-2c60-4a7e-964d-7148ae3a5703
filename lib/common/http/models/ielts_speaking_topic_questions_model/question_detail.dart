class QuestionDetail {
  String? chinese;
  String? english;

  QuestionDetail({this.chinese, this.english});

  QuestionDetail.fromJson(Map<String, dynamic> json) {
    chinese = json['chinese'];
    english = json['english'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['chinese'] = this.chinese;
    data['english'] = this.english;
    return data;
  }
}
