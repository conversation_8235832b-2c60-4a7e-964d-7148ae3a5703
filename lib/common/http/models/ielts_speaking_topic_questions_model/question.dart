import 'dart:core';
import 'question_detail.dart';

class Question {
  List<QuestionDetail>? list;
  String? partName;

  Question({this.list, this.partName});

  Question.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      list = <QuestionDetail>[];
      json['list'].forEach((v) {
        list!.add(new QuestionDetail.fromJson(v));
      });
    }
    partName = json['part_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.list != null) {
      data['list'] = this.list!.map((v) => v.toJson()).toList();
    }
    data['part_name'] = this.partName;
    return data;
  }
}
