class Data {
  String? additional;
  String? chineseMeaning;
  String? collectDate;
  String? defaultPronunciationUrl;
  int? id;
  String? initialLetter;
  String? sentence;
  String? source;
  int? sourceId;
  String? ukPronunciationUrl;
  String? usPronunciationUrl;
  dynamic vocabularies;
  String? score;
  String? userPronunciationUrl;

  Data({
    this.additional,
    this.chineseMeaning,
    this.collectDate,
    this.defaultPronunciationUrl,
    this.id,
    this.initialLetter,
    this.sentence,
    this.source,
    this.sourceId,
    this.ukPronunciationUrl,
    this.usPronunciationUrl,
    this.vocabularies,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        additional: json['additional'] as String?,
        chineseMeaning: json['chinese_meaning'] as String?,
        collectDate: json['collect_date'] as String?,
        defaultPronunciationUrl: json['default_pronunciation_url'] as String?,
        id: json['id'] as int?,
        initialLetter: json['initial_letter'] as String?,
        sentence: json['sentence'] as String?,
        source: json['source'] as String?,
        sourceId: json['source_id'] as int?,
        ukPronunciationUrl: json['uk_pronunciation_url'] as String?,
        usPronunciationUrl: json['us_pronunciation_url'] as String?,
        vocabularies: json['vocabularies'] as dynamic,
      );

  Map<String, dynamic> toJson() => {
        'additional': additional,
        'chinese_meaning': chineseMeaning,
        'collect_date': collectDate,
        'default_pronunciation_url': defaultPronunciationUrl,
        'id': id,
        'initial_letter': initialLetter,
        'sentence': sentence,
        'source': source,
        'source_id': sourceId,
        'uk_pronunciation_url': ukPronunciationUrl,
        'us_pronunciation_url': usPronunciationUrl,
        'vocabularies': vocabularies,
      };
}
