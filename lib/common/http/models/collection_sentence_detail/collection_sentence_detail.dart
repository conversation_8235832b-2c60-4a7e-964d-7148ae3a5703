import 'data.dart';

class CollectionSentenceDetail {
  String? code;
  Data? data;

  CollectionSentenceDetail({this.code, this.data});

  factory CollectionSentenceDetail.fromJson(Map<String, dynamic> json) {
    return CollectionSentenceDetail(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
