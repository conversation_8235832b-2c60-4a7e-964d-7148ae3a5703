class Data {
  bool? correct;
  String? correction;
  String? explanation;

  Data({this.correct, this.correction, this.explanation});

  Data.fromJson(Map<String, dynamic> json) {
    correct = json['correct'];
    correction = json['correction'];
    explanation = json['explanation'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['correct'] = this.correct;
    data['correction'] = this.correction;
    data['explanation'] = this.explanation;
    return data;
  }
}
