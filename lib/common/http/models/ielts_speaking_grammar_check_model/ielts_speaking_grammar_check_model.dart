import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_grammar_check_model/data.dart';

class IeltsSpeakingGrammarCheckModel {
  String? code;
  Data? data;

  IeltsSpeakingGrammarCheckModel({this.code, this.data});

  IeltsSpeakingGrammarCheckModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}
