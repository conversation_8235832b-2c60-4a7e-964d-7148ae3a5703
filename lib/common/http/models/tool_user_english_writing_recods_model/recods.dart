class Recods {
  String? aiText;
  String? createTime;
  int? id;
  String? title;

  Recods({this.aiText, this.createTime, this.id, this.title});

  Recods.fromJson(Map<String, dynamic> json) {
    aiText = json['ai_text'];
    createTime = json['create_time'];
    id = json['id'];
    title = json['title'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ai_text'] = this.aiText;
    data['create_time'] = this.createTime;
    data['id'] = this.id;
    data['title'] = this.title;
    return data;
  }
}
