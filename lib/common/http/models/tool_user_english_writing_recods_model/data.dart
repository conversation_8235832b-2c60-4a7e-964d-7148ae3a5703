import './recods.dart';

class Data {
  int? pageNum;
  int? pageSize;
  List<Recods>? recods;
  int? recodsList;
  int? totalCount;
  int? totalPage;

  Data(
      {this.pageNum,
      this.pageSize,
      this.recods,
      this.recodsList,
      this.totalCount,
      this.totalPage});

  Data.fromJson(Map<String, dynamic> json) {
    pageNum = json['page_num'];
    pageSize = json['page_size'];
    if (json['recods'] != null) {
      recods = <Recods>[];
      json['recods'].forEach((v) {
        recods!.add(new Recods.fromJson(v));
      });
    }
    recodsList = json['recods_list'];
    totalCount = json['total_count'];
    totalPage = json['total_page'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['page_num'] = this.pageNum;
    data['page_size'] = this.pageSize;
    if (this.recods != null) {
      data['recods'] = this.recods!.map((v) => v.toJson()).toList();
    }
    data['recods_list'] = this.recodsList;
    data['total_count'] = this.totalCount;
    data['total_page'] = this.totalPage;
    return data;
  }
}
