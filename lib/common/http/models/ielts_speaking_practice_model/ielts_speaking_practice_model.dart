import 'data.dart';

class IeltsSpeakingPracticeModel {
  String? code;
  Data? data;

  IeltsSpeakingPracticeModel({this.code, this.data});

  factory IeltsSpeakingPracticeModel.fromJson(Map<String, dynamic> json) {
    return IeltsSpeakingPracticeModel(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
