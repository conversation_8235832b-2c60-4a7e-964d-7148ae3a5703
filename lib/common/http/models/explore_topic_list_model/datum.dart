class Datum {
  String? bgImageUrl;
  String? description;
  String? exploreType;
  String? iconUrl;
  int? id;
  int? isRecommended;
  // 1dark  0 普通
  int isBackgroundStyle;
  String? opening;
  String? title;
  String? topicCode;
  dynamic ttsChannel;
  String? userQuickReply;
  dynamic voice;

  Datum({
    this.bgImageUrl,
    this.description,
    this.exploreType,
    this.iconUrl,
    this.id,
    this.isRecommended,
    this.opening,
    this.title,
    this.topicCode,
    this.ttsChannel,
    this.userQuickReply,
    this.voice,
    required this.isBackgroundStyle,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
      bgImageUrl: json['bg_image_url'] as String?,
      description: json['description'] as String?,
      exploreType: json['explore_type'] as String?,
      iconUrl: json['icon_url'] as String?,
      id: json['id'] as int?,
      isRecommended: json['is_recommended'] as int?,
      opening: json['opening'] as String?,
      title: json['title'] as String?,
      topicCode: json['topic_code'] as String?,
      ttsChannel: json['tts_channel'] as dynamic,
      userQuickReply: json['user_quick_reply'] as String?,
      voice: json['voice'] as dynamic,
      isBackgroundStyle: json['is_background_style'] as int? ?? 0);

  Map<String, dynamic> toJson() => {
        'bg_image_url': bgImageUrl,
        'description': description,
        'explore_type': exploreType,
        'icon_url': iconUrl,
        'id': id,
        'is_recommended': isRecommended,
        'is_background_style': isBackgroundStyle,
        'opening': opening,
        'title': title,
        'topic_code': topicCode,
        'tts_channel': ttsChannel,
        'user_quick_reply': userQuickReply,
        'voice': voice,
      };
}
