import 'datum.dart';

class ExploreTopicListModel {
  List<Datum>? data;

  ExploreTopicListModel({this.data});

  factory ExploreTopicListModel.fromJson(Map<String, dynamic> json) {
    return ExploreTopicListModel(
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => Datum.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
