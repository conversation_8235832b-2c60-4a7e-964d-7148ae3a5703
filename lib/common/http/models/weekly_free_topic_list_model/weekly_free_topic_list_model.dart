import 'datum.dart';

class WeeklyFreeTopicListModel {
  String? code;
  List<Datum>? data;

  WeeklyFreeTopicListModel({this.code, this.data});

  factory WeeklyFreeTopicListModel.fromJson(Map<String, dynamic> json) {
    return WeeklyFreeTopicListModel(
      code: json['code'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => Datum.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
