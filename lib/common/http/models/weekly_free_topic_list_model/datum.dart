class Datum {
  String? chinese;
  String? courseLevelName;
  String? imageUrl;
  String? topicCode;
  int? usageCount;

  Datum({
    this.chinese,
    this.courseLevelName,
    this.imageUrl,
    this.topicCode,
    this.usageCount,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        chinese: json['chinese'] as String?,
        courseLevelName: json['course_level_name'] as String?,
        imageUrl: json['image_url'] as String?,
        topicCode: json['topic_code'] as String?,
        usageCount: json['usage_count'] as int?,
      );

  Map<String, dynamic> toJson() => {
        'chinese': chinese,
        'course_level_name': courseLevelName,
        'image_url': imageUrl,
        'topic_code': topicCode,
        'usage_count': usageCount,
      };
}
