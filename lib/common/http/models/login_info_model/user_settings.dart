import 'dart:convert';

class UserSettings {
  num? characterAge;
  List<String>? characterAgeGroup;
  String? characterAudioUrl;
  String? iconUrl;
  String? chatCharacterUrl;
  dynamic characterConversationVideoUrl;
  String? characterEnglishVariation;
  String? characterMbti;
  num? characterGender;
  String? characterGreeting;
  String? characterGreetingCn;
  dynamic characterGreetingVideoUrl;
  String? characterImageUrl;
  String? characterName;
  String? characterSelfIntroduction;
  dynamic characterStaticVideoUrl;
  String? characterType;
  String? characterVoice;
  num? isVideoOpen;
  num? playbackSpeed;
  num? userId;

  UserSettings(
      {this.characterAge,
      this.characterAgeGroup,
      this.characterAudioUrl,
      this.characterConversationVideoUrl,
      this.characterEnglishVariation,
      this.characterMbti,
      this.characterGender,
      this.characterGreeting,
      this.characterGreetingCn,
      this.characterGreetingVideoUrl,
      this.characterImageUrl,
      this.characterName,
      this.characterSelfIntroduction,
      this.characterStaticVideoUrl,
      this.characterType,
      this.characterVoice,
      this.isVideoOpen,
      this.playbackSpeed,
      this.userId,
      this.iconUrl,
      this.chatCharacterUrl});

  @override
  String toString() {
    return 'UserSettings(characterAge: $characterAge, characterAgeGroup: $characterAgeGroup, characterAudioUrl: $characterAudioUrl, iconUrl: $iconUrl, chatCharacterUrl: $chatCharacterUrl, characterConversationVideoUrl: $characterConversationVideoUrl, characterEnglishVariation: $characterEnglishVariation, characterMbti: $characterMbti, characterGender: $characterGender, characterGreeting: $characterGreeting, characterGreetingCn: $characterGreetingCn, characterGreetingVideoUrl: $characterGreetingVideoUrl, characterImageUrl: $characterImageUrl, characterName: $characterName, characterSelfIntroduction: $characterSelfIntroduction, characterStaticVideoUrl: $characterStaticVideoUrl, characterType: $characterType, characterVoice: $characterVoice, isVideoOpen: $isVideoOpen, playbackSpeed: $playbackSpeed, userId: $userId)';
  }

  factory UserSettings.fromMap(Map<String, dynamic> data) => UserSettings(
        characterAge: data['character_age'] as num?,
        characterAgeGroup: List<String>.from(data['character_age_group']),
        characterAudioUrl: data['character_audio_url'] as String?,
        iconUrl: data['icon_url'] as String?,
        chatCharacterUrl: data['chat_character_url'] as String?,
        characterConversationVideoUrl:
            data['character_conversation_video_url'] as dynamic,
        characterEnglishVariation:
            data['character_english_variation'] as String?,
        characterMbti: data['character_mbti'] as String?,
        characterGender: data['character_gender'] as num?,
        characterGreeting: data['character_greeting'] as String?,
        characterGreetingCn: data['character_greeting_cn'] as String?,
        characterGreetingVideoUrl:
            data['character_greeting_video_url'] as dynamic,
        characterImageUrl: data['character_image_url'] as String?,
        characterName: data['character_name'] as String?,
        characterSelfIntroduction:
            data['character_self_introduction'] as String?,
        characterStaticVideoUrl: data['character_static_video_url'] as dynamic,
        characterType: data['character_type'] as String?,
        characterVoice: data['character_voice'] as String?,
        isVideoOpen: data['is_video_open'] as num?,
        playbackSpeed: data['playback_speed'] as num?,
        userId: data['user_id'] as num?,
      );

  Map<String, dynamic> toMap() => {
        'character_age': characterAge,
        'character_age_group': characterAgeGroup,
        'character_audio_url': characterAudioUrl,
        'icon_url': iconUrl,
        'chat_character_url': chatCharacterUrl,
        'character_conversation_video_url': characterConversationVideoUrl,
        'character_english_variation': characterEnglishVariation,
        'character_mbti': characterMbti,
        'character_gender': characterGender,
        'character_greeting': characterGreeting,
        'character_greeting_cn': characterGreetingCn,
        'character_greeting_video_url': characterGreetingVideoUrl,
        'character_image_url': characterImageUrl,
        'character_name': characterName,
        'character_self_introduction': characterSelfIntroduction,
        'character_static_video_url': characterStaticVideoUrl,
        'character_type': characterType,
        'character_voice': characterVoice,
        'is_video_open': isVideoOpen,
        'playback_speed': playbackSpeed,
        'user_id': userId,
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [UserSettings].
  factory UserSettings.fromJson(String data) {
    return UserSettings.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [UserSettings] to a JSON string.
  String toJson() => json.encode(toMap());
}
