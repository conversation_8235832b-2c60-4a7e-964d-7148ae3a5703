import 'dart:convert';

import 'data.dart';

class LoginInfoModel {
  String? code;
  Data? data;

  LoginInfoModel({this.code, this.data});

  @override
  String toString() => 'LoginInfoModel(code: $code, data: $data)';

  factory LoginInfoModel.fromMap(Map<String, dynamic> data) {
    return LoginInfoModel(
      code: data['code'] as String?,
      data: data['data'] == null
          ? null
          : Data.fromMap(data['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toMap() => {
        'code': code,
        'data': data?.toMap(),
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [LoginInfoModel].
  factory LoginInfoModel.fromJson(String data) {
    return LoginInfoModel.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [LoginInfoModel] to a JSON string.
  String toJson() => json.encode(toMap());
}
