import 'dart:convert';

import 'package:flutter/material.dart';

import 'user_settings.dart';

class Data {
  String? accessToken;
  num? availableMinutes;
  String? avatarUrl;
  num? complimentaryMinutes;
  num? daysOfStudy;
  String? englishLevelChineseAbbreviation;
  bool? enterTheProcess;
  dynamic expirationDate;
  num? memberType;
  String? membershipLevel;
  String? nickname;
  String? promotionCode;
  num? usedMinutes;
  num? userId;
  UserSettings? userSettings;

  Data({
    this.accessToken,
    this.availableMinutes,
    this.avatarUrl,
    this.complimentaryMinutes,
    this.daysOfStudy,
    this.englishLevelChineseAbbreviation,
    this.enterTheProcess,
    this.expirationDate,
    this.memberType,
    this.membershipLevel,
    this.nickname,
    this.promotionCode,
    this.usedMinutes,
    this.userId,
    this.userSettings,
  });

  @override
  String toString() {
    return 'Data(accessToken: $accessToken, availableMinutes: $availableMinutes, avatarUrl: $avatarUrl, complimentaryMinutes: $complimentaryMinutes, daysOfStudy: $daysOfStudy, englishLevelChineseAbbreviation: $englishLevelChineseAbbreviation, expirationDate: $expirationDate, memberType: $memberType, membershipLevel: $membershipLevel, nickname: $nickname, promotionCode: $promotionCode, usedMinutes: $usedMinutes, userId: $userId, userSettings: $userSettings,enterTheProcess: $enterTheProcess)';
  }

  factory Data.fromMap(Map<String, dynamic> data) => Data(
        accessToken: data['access_token'] as String?,
        availableMinutes: data['available_minutes'] as num?,
        avatarUrl: data['avatar_url'] as String?,
        complimentaryMinutes: data['complimentary_minutes'] as num?,
        daysOfStudy: data['days_of_study'] as num?,
        englishLevelChineseAbbreviation:
            data['english_level_chinese_abbreviation'] as String?,
        expirationDate: data['expiration_date'] as dynamic,
        memberType: data['member_type'] as num?,
        membershipLevel: data['membership_level'] as String?,
        nickname: data['nickname'] as String?,
        promotionCode: data['promotion_code'] as String?,
        usedMinutes: (data['used_minutes'] as num?)?.toDouble(),
        userId: data['user_id'] as num?,
        enterTheProcess: data['enter_the_process'] as bool?,
        userSettings: data['user_settings'] == null
            ? null
            : UserSettings.fromMap(
                data['user_settings'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toMap() => {
        'access_token': accessToken,
        'available_minutes': availableMinutes,
        'avatar_url': avatarUrl,
        'complimentary_minutes': complimentaryMinutes,
        'days_of_study': daysOfStudy,
        'english_level_chinese_abbreviation': englishLevelChineseAbbreviation,
        'expiration_date': expirationDate,
        'member_type': memberType,
        'membership_level': membershipLevel,
        'nickname': nickname,
        'promotion_code': promotionCode,
        'used_minutes': usedMinutes,
        'enter_the_process': enterTheProcess,
        'user_id': userId,
        'user_settings': userSettings?.toMap(),
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [Data].
  factory Data.fromJson(String data) {
    return Data.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [Data] to a JSON string.
  String toJson() => json.encode(toMap());
}
