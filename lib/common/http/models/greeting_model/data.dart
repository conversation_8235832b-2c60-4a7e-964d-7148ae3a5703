class Data {
  num? audioDuration;
  num? conversationId;
  String? greeting;
  dynamic mp4Url;
  String? replyAudioUrl;

  Data({
    this.audioDuration,
    this.conversationId,
    this.greeting,
    this.mp4Url,
    this.replyAudioUrl,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        audioDuration: json['audio_duration'] as num?,
        conversationId: json['conversation_id'] as num?,
        greeting: json['greeting'] as String?,
        mp4Url: json['mp4_url'] as dynamic,
        replyAudioUrl: json['reply_audio_url'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'audio_duration': audioDuration,
        'conversation_id': conversationId,
        'greeting': greeting,
        'mp4_url': mp4Url,
        'reply_audio_url': replyAudioUrl,
      };
}
