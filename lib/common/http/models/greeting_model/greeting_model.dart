import 'data.dart';

class GreetingModel {
  String? code;
  Data? data;

  GreetingModel({this.code, this.data});

  factory GreetingModel.fromJson(Map<String, dynamic> json) => GreetingModel(
        code: json['code'] as String?,
        data: json['data'] == null
            ? null
            : Data.fromJson(json['data'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
