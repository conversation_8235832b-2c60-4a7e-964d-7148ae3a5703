import 'data.dart';

class ChatModel {
  String? code;
  Data? data;

  ChatModel({this.code, this.data});

  factory ChatModel.fromJson(Map<String, dynamic> json) => ChatModel(
        code: json['code'] as String?,
        data: json['data'] == null
            ? null
            : Data.fromJson(json['data'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
