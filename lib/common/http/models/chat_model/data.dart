class Data {
  num? audioDuration;
  num? conversationId;
  double? costTimeGpt;
  double? costToWav;
  String? question;
  String? reply;
  String? replyAudioUrl;
  num? clientSentenceId;

  Data(
      {this.audioDuration,
      this.conversationId,
      this.costTimeGpt,
      this.costToWav,
      this.question,
      this.reply,
      this.replyAudioUrl,
      this.clientSentenceId});

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        audioDuration: json['audio_duration'] as num?,
        conversationId: json['conversation_id'] as num?,
        clientSentenceId: json['client_sentence_id'] as num?,
        costTimeGpt: (json['cost_time_gpt'] as num?)?.toDouble(),
        costToWav: (json['cost_to_wav'] as num?)?.toDouble(),
        question: json['question'] as String?,
        reply: json['reply'] as String?,
        replyAudioUrl: json['reply_audio_url'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'audio_duration': audioDuration,
        'conversation_id': conversationId,
        'client_sentence_id': clientSentenceId,
        'cost_time_gpt': costTimeGpt,
        'cost_to_wav': costToWav,
        'question': question,
        'reply': reply,
        'reply_audio_url': replyAudioUrl,
      };
}
