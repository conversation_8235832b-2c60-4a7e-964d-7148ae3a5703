import 'data.dart';

class ExploreChatModel {
  String? code;
  Data? data;

  ExploreChatModel({this.code, this.data});

  factory ExploreChatModel.fromJson(Map<String, dynamic> json) {
    return ExploreChatModel(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
