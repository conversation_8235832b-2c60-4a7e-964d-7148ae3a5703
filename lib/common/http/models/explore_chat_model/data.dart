class Data {
  String? audioUrl;
  int? conversationId;
  String? reply;
  List<String>? userQuickReply;

  Data({
    this.audioUrl,
    this.conversationId,
    this.reply,
    this.userQuickReply,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        audioUrl: json['audio_url'] as String?,
        conversationId: json['conversation_id'] as int?,
        reply: json['reply'] as String?,
        userQuickReply: json['user_quick_reply'] == null
            ? []
            : List<String>.from(json['user_quick_reply']),
      );

  Map<String, dynamic> toJson() => {
        'audio_url': audioUrl,
        'conversation_id': conversationId,
        'reply': reply,
        'user_quick_reply': userQuickReply,
      };
}
