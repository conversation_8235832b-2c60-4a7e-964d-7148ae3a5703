import 'package:equatable/equatable.dart';

class RecordData extends Equatable {
  final String imageUrl;
  final String name;

  const RecordData({
    this.imageUrl = '',
    this.name = '',
  });

  factory RecordData.fromJson(Map<String, dynamic> json) {
    return RecordData(
      imageUrl: json['image_url']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'image_url': imageUrl,
      'name': name,
    };
  }

  @override
  List<Object?> get props => [imageUrl, name];
} 