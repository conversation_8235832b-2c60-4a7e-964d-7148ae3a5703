import 'package:equatable/equatable.dart';
import 'record_data.dart';

class PurchaseRecordModel extends Equatable {
  final String code;
  final List<RecordData> data;

  const PurchaseRecordModel({
    this.code = '',
    this.data = const [],
  });

  factory PurchaseRecordModel.fromJson(Map<String, dynamic> json) {
    return PurchaseRecordModel(
      code: json['code']?.toString() ?? '',
      data: (json['data'] as List<dynamic>?)
              ?.map((e) => RecordData.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'data': data.map((e) => e.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [code, data];
} 