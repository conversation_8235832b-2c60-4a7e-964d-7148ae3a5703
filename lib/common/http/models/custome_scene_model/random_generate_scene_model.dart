class RandomGenerateSceneModel {
  String? code;
  Data? data;

  RandomGenerateSceneModel({this.code, this.data});

  RandomGenerateSceneModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  String? aiRole;
  String? target;
  String? description;
  String? myRole;

  Data({this.aiRole, this.target, this.description, this.myRole});

  Data.fromJson(Map<String, dynamic> json) {
    aiRole = json['ai_role'];
    target = json['target'];
    description = json['description'];
    myRole = json['my_role'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ai_role'] = this.aiRole;
    data['target'] = this.target;
    data['description'] = this.description;
    data['my_role'] = this.myRole;
    return data;
  }
}
