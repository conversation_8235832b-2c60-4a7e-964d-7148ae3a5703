class MyCreateStatusListModel {
  String? code;
  List<Data>? data;

  MyCreateStatusListModel({this.code, this.data});

  MyCreateStatusListModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    if (json['data'] != null) {
      data = [];
      json['data'].forEach((v) {
        data?.add(Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  /// 标题
  String? key;
  /// 状态
  int? value;
  int? count;

  Data({this.key, this.value, this.count});

  Data.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['key'] = this.key;
    data['value'] = this.value;
    data['count'] = this.count;
    return data;
  }
}
