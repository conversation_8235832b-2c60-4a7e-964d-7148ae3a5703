class SceneDetailV2Model {
  String? code;
  Detail? data;

  SceneDetailV2Model({this.code, this.data});

  SceneDetailV2Model.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'] != null ? new Detail.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Detail {
  int? id;
  String? aiRole;
  String? chinese;
  String? courseLevelName;
  String? english;
  bool? hasReport;
  String? imageUrl;
  int? lastConversationId;
  int? lastTeachConversationId;
  int? learningStatus;
  String? myRole;
  int? reportConversationId;
  String? target;
  String? topicCode;
  int? status;
  int? usageCount;
  String? creatorNickname;
  int? hasCollect;
  int? isCreator;

  Detail(
      {this.aiRole,
      this.chinese,
      this.courseLevelName,
      this.english,
      this.hasReport,
      this.imageUrl,
      this.lastConversationId,
      this.lastTeachConversationId,
      this.learningStatus,
      this.myRole,
      this.reportConversationId,
      this.target,
      this.topicCode,
      this.status,
      this.usageCount,
      this.creatorNickname,
      this.hasCollect,
      this.isCreator});

  Detail.fromJson(Map<String, dynamic> json) {
    aiRole = json['ai_role'];
    chinese = json['chinese'];
    courseLevelName = json['course_level_name'];
    english = json['english'];
    hasReport = json['has_report'];
    imageUrl = json['image_url'];
    lastConversationId = json['last_conversation_id'];
    lastTeachConversationId = json['last_teach_conversation_id'];
    learningStatus = json['learning_status'];
    myRole = json['my_role'];
    reportConversationId = json['report_conversation_id'];
    target = json['target'];
    topicCode = json['topic_code'];
    status = json['status'];
    usageCount = json['usage_count'];
    creatorNickname = json['creator_nickname'];
    hasCollect = json['has_collect'];
    isCreator = json['is_creator'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ai_role'] = this.aiRole;
    data['chinese'] = this.chinese;
    data['course_level_name'] = this.courseLevelName;
    data['english'] = this.english;
    data['has_report'] = this.hasReport;
    data['image_url'] = this.imageUrl;
    data['last_conversation_id'] = this.lastConversationId;
    data['last_teach_conversation_id'] = this.lastTeachConversationId;
    data['learning_status'] = this.learningStatus;
    data['my_role'] = this.myRole;
    data['report_conversation_id'] = this.reportConversationId;
    data['target'] = this.target;
    data['topic_code'] = this.topicCode;
    data['status'] = this.status;
    data['usage_count'] = this.usageCount;
    data['creator_nickname'] = this.creatorNickname;
    data['has_collect'] = this.hasCollect;
    data['is_creator'] = this.isCreator;
    data['id'] = this.id;
    return data;
  }
}
