class GetShareImageModel {
  String? code;
  List<ShareImageDataItem>? data;

  GetShareImageModel({this.code, this.data});

  GetShareImageModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    if (json['data'] != null) {
      data = [];
      json['data'].forEach((v) {
        data?.add(ShareImageDataItem.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ShareImageDataItem {
  String? poster_url;
  String? style;

  ShareImageDataItem({this.poster_url, this.style});

  ShareImageDataItem.fromJson(Map<String, dynamic> json) {
    poster_url = json['poster_url'];
    style = json['style'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['poster_url'] = this.poster_url;
    data['style'] = this.style;
    return data;
  }
}
