class MyCreateListModel {
  String? code;
  Data? data;

  MyCreateListModel({this.code, this.data});

  MyCreateListModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    if (json['data'] != null) {
      data = Data.fromJson(json['data']);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['data'] = this.data?.toJson();
    return data;
  }
}

class Data {
  int? currentPage;
  List<Item>? items;
  int? total;
  int? totalPages;

  Data({this.currentPage, this.items, this.total, this.totalPages});

  Data.fromJson(Map<String, dynamic> json) {
    currentPage = json['current_page'];
    if (json['items'] != null) {
      items = [];
      json['items'].forEach((v) {
        items?.add(Item.fromJson(v));
      });
    }
    total = json['total'];
    totalPages = json['total_pages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['current_page'] = this.currentPage;
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    data['total'] = this.total;
    data['total_pages'] = this.totalPages;
    return data;
  }
}

class Item {
  String? aiRole;
  String? chinese;
  int? id;
  String? imageUrl;
  String? myRole;
  String? target;
  String? topicCode;
  int? status;
  String? nickName;
  String? rejectReason;

  Item(
      {this.aiRole,
      this.chinese,
      this.id,
      this.imageUrl,
      this.myRole,
      this.target,
      this.topicCode,
      this.status,
      this.nickName,
      this.rejectReason});

  Item.fromJson(Map<String, dynamic> json) {
    aiRole = json['ai_role'];
    chinese = json['chinese'];
    id = json['id'];
    imageUrl = json['image_url'];
    myRole = json['my_role'];
    target = json['target'];
    topicCode = json['topic_code'];
    status = json['status'];
    nickName = json['nickname'];
    rejectReason = json['reject_reason'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ai_role'] = this.aiRole;
    data['chinese'] = this.chinese;
    data['id'] = this.id;
    data['image_url'] = this.imageUrl;
    data['my_role'] = this.myRole;
    data['target'] = this.target;
    data['topic_code'] = this.topicCode;
    data['status'] = this.status;
    data['nickname'] = this.nickName;
    data['reject_reason'] = this.rejectReason;
    return data;
  }
}
