class StringListModel {
  String? code;
  List<String>? data;

  StringListModel({this.code, this.data});

  StringListModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data']?.cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['data'] = this.data?.toList();
    return data;
  }
}
