class CustomSceneListItem {
  String? chinese;
  String? imageUrl;
  String? nickName;
  String? target;
  String? topicCode;
  int? usageCount;

  CustomSceneListItem({
    this.chinese,
    this.imageUrl,
    this.nickName,
    this.target,
    this.topicCode,
    this.usageCount,
  });

  factory CustomSceneListItem.fromJson(Map<String, dynamic> json) =>
      CustomSceneListItem(
        chinese: json['chinese'] as String?,
        imageUrl: json['image_url'] as String?,
        nickName: json['nickname'] as String?,
        target: json['target'] as String?,
        topicCode: json['topic_code'] as String?,
        usageCount: json['usage_count'] as int?,
      );

  Map<String, dynamic> toJson() => {
        'chinese': chinese,
        'image_url': imageUrl,
        'nickname': nickName,
        'target': target,
        'topic_code': topicCode,
        'usage_count': usageCount,
      };
}
