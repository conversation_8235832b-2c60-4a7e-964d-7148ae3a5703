import 'custome_scene_list_item.dart';

class CustomSceneListModel {
  String? code;
  Data? data;

  CustomSceneListModel({this.code, this.data});

  factory CustomSceneListModel.fromJson(Map<String, dynamic> json) =>
      CustomSceneListModel(
        code: json['code'] as String?,
        data: Data.fromJson(json['data'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}

class Data {
  int? currentPage;
  List<CustomSceneListItem>? items;
  int? total;
  int? totalPages;

  Data({this.currentPage, this.items, this.total, this.totalPages});

  Data.fromJson(Map<String, dynamic> json) {
    currentPage = json['current_page'];
    if (json['items'] != null) {
      items = [];
      json['items'].forEach((v) {
        items?.add(CustomSceneListItem.fromJson(v));
      });
    }
    total = json['total'];
    totalPages = json['total_pages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['current_page'] = this.currentPage;
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    data['total'] = this.total;
    data['total_pages'] = this.totalPages;
    return data;
  }
}
