import 'package:equatable/equatable.dart';

class Data extends Equatable {
  final bool? correct;
  final String? correction;
  final String? explanation;

  const Data({this.correct, this.correction, this.explanation});

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        correct: json['correct'] as bool?,
        correction: json['correction'] as String?,
        explanation: json['explanation'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'correct': correct,
        'correction': correction,
        'explanation': explanation,
      };

  Data copyWith({
    bool? correct,
    String? correction,
    String? explanation,
  }) {
    return Data(
      correct: correct ?? this.correct,
      correction: correction ?? this.correction,
      explanation: explanation ?? this.explanation,
    );
  }

  @override
  List<Object?> get props => [correct, correction, explanation];
}
