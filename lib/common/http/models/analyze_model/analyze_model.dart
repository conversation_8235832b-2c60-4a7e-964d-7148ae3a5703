import 'package:equatable/equatable.dart';

import 'data.dart';

class AnalyzeModel extends Equatable {
  final String? code;
  final Data? data;

  const AnalyzeModel({this.code, this.data});

  factory AnalyzeModel.fromJson(Map<String, dynamic> json) => AnalyzeModel(
        code: json['code'] as String?,
        data: json['data'] == null
            ? null
            : Data.fromJson(json['data'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };

  AnalyzeModel copyWith({
    String? code,
    Data? data,
  }) {
    return AnalyzeModel(
      code: code ?? this.code,
      data: data ?? this.data,
    );
  }

  @override
  List<Object?> get props => [code, data];
}
