import 'data.dart';

class CollectionCollocationDetail {
  String? code;
  Data? data;

  CollectionCollocationDetail({this.code, this.data});

  factory CollectionCollocationDetail.fromJson(Map<String, dynamic> json) {
    return CollectionCollocationDetail(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}