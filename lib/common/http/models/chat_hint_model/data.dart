class Data {
  String? audioUrl;
  String? chinese;
  String? english;
  bool? collected;

  Data({this.audioUrl, this.chinese, this.english, this.collected});

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        audioUrl: json['audio_url'] as String?,
        chinese: json['chinese'] as String?,
        english: json['english'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'audio_url': audioUrl,
        'chinese': chinese,
        'english': english,
      };
}
