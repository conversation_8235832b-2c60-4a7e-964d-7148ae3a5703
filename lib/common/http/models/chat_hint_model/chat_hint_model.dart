import 'data.dart';

class ChatHintModel {
  String? code;
  List<Data>? data;

  ChatHintModel({this.code, this.data});

  factory ChatHintModel.fromJson(Map<String, dynamic> json) => ChatHintModel(
        code: json['code'] as String?,
        data: (json['data'] as List<dynamic>?)
            ?.map((e) => Data.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
