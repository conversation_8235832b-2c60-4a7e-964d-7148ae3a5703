class ReferenceDialogue {
  String? chinese;
  String? english;

  ReferenceDialogue({this.chinese, this.english});

  factory ReferenceDialogue.fromJson(Map<String, dynamic> json) {
    return ReferenceDialogue(
      chinese: json['chinese'] as String?,
      english: json['english'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'chinese': chinese,
        'english': english,
      };
}
