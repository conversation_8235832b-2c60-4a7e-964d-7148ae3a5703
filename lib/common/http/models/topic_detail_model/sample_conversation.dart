import 'reference_dialogue.dart';
import 'target.dart';

class SampleConversation {
  String? myRole;
  List<ReferenceDialogue>? referenceDialogue;
  String? robotRole;
  List<Target>? targets;

  SampleConversation({
    this.myRole,
    this.referenceDialogue,
    this.robotRole,
    this.targets,
  });

  factory SampleConversation.fromJson(Map<String, dynamic> json) {
    return SampleConversation(
      myRole: json['my_role'] as String?,
      referenceDialogue: (json['reference_dialogue'] as List<dynamic>?)
          ?.map((e) => ReferenceDialogue.fromJson(e as Map<String, dynamic>))
          .toList(),
      robotRole: json['robot_role'] as String?,
      targets: (json['targets'] as List<dynamic>?)
          ?.map((e) => Target.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'my_role': myRole,
        'reference_dialogue':
            referenceDialogue?.map((e) => e.toJson()).toList(),
        'robot_role': robotRole,
        'targets': targets?.map((e) => e.toJson()).toList(),
      };
}
