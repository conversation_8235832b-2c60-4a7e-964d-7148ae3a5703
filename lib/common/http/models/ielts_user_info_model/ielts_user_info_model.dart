import 'data.dart';

class IeltsUserInfoModel {
  String? code;
  Data? data;

  IeltsUserInfoModel({this.code, this.data});

  factory IeltsUserInfoModel.fromJson(Map<String, dynamic> json) =>
      IeltsUserInfoModel(
        code: json['code'] as String?,
        data: json['data'] == null
            ? null
            : Data.fromJson(json['data'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
