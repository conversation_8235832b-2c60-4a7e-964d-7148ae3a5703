class Data {
  Desc? desc;
  String? encouragement;
  String? fluencyScore;
  String? grammarScore;
  int? part1ExerciseResidueTimes;
  int? part1MockResidueTimes;
  int? part23ExerciseResidueTimes;
  int? part23MockResidueTimes;
  int? practiceId;
  String? pronunciationScore;
  String? radarChartUrl;
  String? score;
  int? trialPart1TopicId;
  int? trialPart23TopicId;
  String? vocabularyScore;

  Data(
      {this.desc,
      this.encouragement,
      this.fluencyScore,
      this.grammarScore,
      this.part1ExerciseResidueTimes,
      this.part1MockResidueTimes,
      this.part23ExerciseResidueTimes,
      this.part23MockResidueTimes,
      this.practiceId,
      this.pronunciationScore,
      this.radarChartUrl,
      this.score,
      this.trialPart1TopicId,
      this.trialPart23TopicId,
      this.vocabularyScore});

  Data.fromJson(Map<String, dynamic> json) {
    desc = json['desc'] != null ? new Desc.fromJson(json['desc']) : null;
    encouragement = json['encouragement'];
    fluencyScore = json['fluency_score'];
    grammarScore = json['grammar_score'];
    part1ExerciseResidueTimes = json['part1_exercise_residue_times'];
    part1MockResidueTimes = json['part1_mock_residue_times'];
    part23ExerciseResidueTimes = json['part2_3_exercise_residue_times'];
    part23MockResidueTimes = json['part2_3_mock_residue_times'];
    practiceId = json['practice_id'];
    pronunciationScore = json['pronunciation_score'];
    radarChartUrl = json['radar_chart_url'];
    score = json['score'];
    trialPart1TopicId = json['trial_part1_topic_id'];
    trialPart23TopicId = json['trial_part2_3_topic_id'];
    vocabularyScore = json['vocabulary_score'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.desc != null) {
      data['desc'] = this.desc!.toJson();
    }
    data['encouragement'] = this.encouragement;
    data['fluency_score'] = this.fluencyScore;
    data['grammar_score'] = this.grammarScore;
    data['part1_exercise_residue_times'] = this.part1ExerciseResidueTimes;
    data['part1_mock_residue_times'] = this.part1MockResidueTimes;
    data['part2_3_exercise_residue_times'] = this.part23ExerciseResidueTimes;
    data['part2_3_mock_residue_times'] = this.part23MockResidueTimes;
    data['practice_id'] = this.practiceId;
    data['pronunciation_score'] = this.pronunciationScore;
    data['radar_chart_url'] = this.radarChartUrl;
    data['score'] = this.score;
    data['trial_part1_topic_id'] = this.trialPart1TopicId;
    data['trial_part2_3_topic_id'] = this.trialPart23TopicId;
    data['vocabulary_score'] = this.vocabularyScore;
    return data;
  }
}

class Desc {
  List<String>? rankDesc;
  List<String>? rateDesc;

  Desc({this.rankDesc, this.rateDesc});

  Desc.fromJson(Map<String, dynamic> json) {
    rankDesc = json['rank_desc'].cast<String>();
    rateDesc = json['rate_desc'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['rank_desc'] = this.rankDesc;
    data['rate_desc'] = this.rateDesc;
    return data;
  }
}
