import 'datum.dart';

class EnglishLevelModel {
  String? code;
  List<Datum>? data;

  EnglishLevelModel({this.code, this.data});

  factory EnglishLevelModel.fromJson(Map<String, dynamic> json) {
    return EnglishLevelModel(
      code: json['code'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => Datum.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}
