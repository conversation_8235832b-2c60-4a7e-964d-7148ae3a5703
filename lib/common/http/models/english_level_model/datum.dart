class Datum {
  String? chineseAbbreviation;
  String? description;
  String? icon;
  String? levelCn;
  String? levelEn;

  Datum({
    this.chineseAbbreviation,
    this.description,
    this.icon,
    this.levelCn,
    this.levelEn,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        chineseAbbreviation: json['chinese_abbreviation'] as String?,
        description: json['description'] as String?,
        icon: json['icon'] as String?,
        levelCn: json['level_cn'] as String?,
        levelEn: json['level_en'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'chinese_abbreviation': chineseAbbreviation,
        'description': description,
        'icon': icon,
        'level_cn': levelCn,
        'level_en': levelEn,
      };
}
