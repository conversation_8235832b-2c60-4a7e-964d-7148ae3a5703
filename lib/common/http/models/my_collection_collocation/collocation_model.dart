import 'collocation_list_model.dart';

class CollocationCategory {
  String? groupBy;
  List<CollocationList>? collocationList;

  CollocationCategory({this.groupBy, this.collocationList});

  factory CollocationCategory.fromJson(Map<String, dynamic> json) =>
      CollocationCategory(
        groupBy: json['group_by'] as String?,
        collocationList: (json['collocation_list'] as List<dynamic>?)
            ?.map((e) => CollocationList.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'group_by': groupBy,
        'collocation_list': collocationList?.map((e) => e.toJson()).toList(),
      };
} 