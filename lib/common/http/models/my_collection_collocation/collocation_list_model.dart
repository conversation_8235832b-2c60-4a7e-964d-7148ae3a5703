class CollocationList {
  String? cefrLevel;
  String? collectDate;
  String? commonDefinition;
  String? defaultPronunciationUrl;
  int? id;
  String? initialLetter;
  String? collocation;
  String? ukPronunciationUrl;
  String? usPronunciationUrl;
  String? cognitiveTip;
  String? chineseExample;
  String? englishExample;

  CollocationList({
    this.cefrLevel,
    this.collectDate,
    this.commonDefinition,
    this.defaultPronunciationUrl,
    this.id,
    this.initialLetter,
    this.collocation,
    this.ukPronunciationUrl,
    this.usPronunciationUrl,
    this.cognitiveTip,
    this.chineseExample,
    this.englishExample,
  });

  factory CollocationList.fromJson(Map<String, dynamic> json) {
    return CollocationList(
      cefrLevel: json['cefr_level'] as String?,
      collectDate: json['collect_date'] as String?,
      commonDefinition: json['common_definition'] as String?,
      defaultPronunciationUrl: json['default_pronunciation_url'] as String?,
      id: json['id'] as int?,
      initialLetter: json['initial_letter'] as String?,
      collocation: json['collocation'] as String?,
      ukPronunciationUrl: json['uk_pronunciation_url'] as String?,
      usPronunciationUrl: json['us_pronunciation_url'] as String?,
      cognitiveTip: json['cognitive_tip'] as String?,
      chineseExample: json['chinese_example'] as String?,
      englishExample: json['english_example'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'cefr_level': cefrLevel,
        'collect_date': collectDate,
        'common_definition': commonDefinition,
        'default_pronunciation_url': defaultPronunciationUrl,
        'id': id,
        'initial_letter': initialLetter,
        'collocation': collocation,
        'uk_pronunciation_url': ukPronunciationUrl,
        'us_pronunciation_url': usPronunciationUrl,
        'cognitive_tip': cognitiveTip,
        'chinese_example': chineseExample,
        'english_example': englishExample,
      };
} 