import 'collocation_model.dart';

class MyCollectionCollocation {
  String? code;
  CollocationData? data;

  MyCollectionCollocation({this.code, this.data});

  factory MyCollectionCollocation.fromJson(Map<String, dynamic> json) =>
      MyCollectionCollocation(
        code: json['code'] as String?,
        data: json['data'] == null
            ? null
            : CollocationData.fromJson(json['data'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}

class CollocationData {
  List<CollocationCategory>? collocationList;
  int? collocationListLength;
  int? count;
  int? pageNum;
  int? pageSize;
  int? totalPage;

  CollocationData({
    this.collocationList,
    this.collocationListLength,
    this.count,
    this.pageNum,
    this.pageSize,
    this.totalPage,
  });

  factory CollocationData.fromJson(Map<String, dynamic> json) => CollocationData(
        collocationList: (json['collocation_list'] as List<dynamic>?)
            ?.map((e) => CollocationCategory.fromJson(e as Map<String, dynamic>))
            .toList(),
        collocationListLength: json['collocation_list_length'] as int?,
        count: json['count'] as int?,
        pageNum: json['page_num'] as int?,
        pageSize: json['page_size'] as int?,
        totalPage: json['total_page'] as int?,
      );

  Map<String, dynamic> toJson() => {
        'collocation_list': collocationList?.map((e) => e.toJson()).toList(),
        'collocation_list_length': collocationListLength,
        'count': count,
        'page_num': pageNum,
        'page_size': pageSize,
        'total_page': totalPage,
      };
} 