class Data {
  int? currentPage;
  List<String>? items;
  int? total;
  int? totalPages;

  Data({this.currentPage, this.items, this.total, this.totalPages});

  Data.fromJson(Map<String, dynamic> json) {
    currentPage = json['current_page'];
    items = json['items'].cast<String>();
    total = json['total'];
    totalPages = json['total_pages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['current_page'] = this.currentPage;
    data['items'] = this.items;
    data['total'] = this.total;
    data['total_pages'] = this.totalPages;
    return data;
  }
}
