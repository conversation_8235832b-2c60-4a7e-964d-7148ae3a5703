import 'next_question.dart';
import 'question.dart';

class Data {
  bool? isEnd;
  int? lastFinishedQuestionIndex;
  NextQuestion? nextQuestion;
  String? nextQuestionAudioUrl;
  int? practiceId;
  Question? question;
  String? questionAudioUrl;
  int? questionId;
  int? totalQuestionCount;
  String? partCategory; // 类型，例如：“Part 1”
  String? topicChineseName; //   主题中文名： 例如 手表
  Data({
    this.isEnd,
    this.lastFinishedQuestionIndex,
    this.nextQuestion,
    this.nextQuestionAudioUrl,
    this.practiceId,
    this.question,
    this.questionAudioUrl,
    this.questionId,
    this.totalQuestionCount,
    this.partCategory,
    this.topicChineseName,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        isEnd: json['is_end'] as bool?,
        lastFinishedQuestionIndex: json['last_finished_question_index'] as int?,
        nextQuestion: json['next_question'] == null
            ? null
            : NextQuestion.fromJson(
                json['next_question'] as Map<String, dynamic>),
        nextQuestionAudioUrl: json['next_question_audio_url'] as String?,
        practiceId: json['practice_id'] as int?,
        question: json['question'] == null
            ? null
            : Question.fromJson(json['question'] as Map<String, dynamic>),
        questionAudioUrl: json['question_audio_url'] as String?,
        questionId: json['question_id'] as int?,
        totalQuestionCount: json['total_question_count'] as int?,
        partCategory: json['part_category'] as String?,
        topicChineseName: json['topic_chinese_name'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'is_end': isEnd,
        'last_finished_question_index': lastFinishedQuestionIndex,
        'next_question': nextQuestion?.toJson(),
        'next_question_audio_url': nextQuestionAudioUrl,
        'practice_id': practiceId,
        'question': question?.toJson(),
        'question_audio_url': questionAudioUrl,
        'question_id': questionId,
        'total_question_count': totalQuestionCount,
      };
}
