import 'data.dart';

class IeltsSpeakingChatModel {
  String? code;
  Data? data;

  IeltsSpeakingChatModel({this.code, this.data});

  factory IeltsSpeakingChatModel.fromJson(Map<String, dynamic> json) {
    return IeltsSpeakingChatModel(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
