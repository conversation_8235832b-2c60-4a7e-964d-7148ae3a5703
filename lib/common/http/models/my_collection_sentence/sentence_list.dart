class SentenceList {
  String? additional;
  String? chineseMeaning;
  String? collectDate;
  String? defaultPronunciationUrl;
  int? id;
  String? initialLetter;
  String? sentence;
  String? source;
  int? sourceId;
  String? ukPronunciationUrl;
  String? usPronunciationUrl;

  SentenceList({
    this.additional,
    this.chineseMeaning,
    this.collectDate,
    this.defaultPronunciationUrl,
    this.id,
    this.initialLetter,
    this.sentence,
    this.source,
    this.sourceId,
    this.ukPronunciationUrl,
    this.usPronunciationUrl,
  });

  String? souceChinese() {
    return SentenceSourceEnumExtension.fromName(source)?.chineseName;
  }

  factory SentenceList.fromJson(Map<String, dynamic> json) => SentenceList(
        additional: json['additional'] as String?,
        chineseMeaning: json['chinese_meaning'] as String?,
        collectDate: json['collect_date'] as String?,
        defaultPronunciationUrl: json['default_pronunciation_url'] as String?,
        id: json['id'] as int?,
        initialLetter: json['initial_letter'] as String?,
        sentence: json['sentence'] as String?,
        source: json['source'] as String?,
        sourceId: json['source_id'] as int?,
        ukPronunciationUrl: json['uk_pronunciation_url'] as String?,
        usPronunciationUrl: json['us_pronunciation_url'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'additional': additional,
        'chinese_meaning': chineseMeaning,
        'collect_date': collectDate,
        'default_pronunciation_url': defaultPronunciationUrl,
        'id': id,
        'initial_letter': initialLetter,
        'sentence': sentence,
        'source': source,
        'source_id': sourceId,
        'uk_pronunciation_url': ukPronunciationUrl,
        'us_pronunciation_url': usPronunciationUrl,
      };
}

enum SentenceSourceEnum {
  conversationReport("conversation_report", "对话报告"),
  conversationPrompt("conversation_prompt", "对话提示"),
  conversationHistory("conversation_history", "对话历史"),
  referenceAnswer("reference_answer", "参考答案"),
  courseStudy("course_study", "课程学习");

  final String name;
  final String chineseName;

  const SentenceSourceEnum(this.name, this.chineseName);
}

extension SentenceSourceEnumExtension on SentenceSourceEnum {
  static SentenceSourceEnum? fromName(String? name) {
    for (var value in SentenceSourceEnum.values) {
      if (value.name == name) {
        return value;
      }
    }
    return null;
  }
}
