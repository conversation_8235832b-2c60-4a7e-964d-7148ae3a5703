import 'data.dart';

class MyCollectionSentence {
  String? code;
  Data? data;

  MyCollectionSentence({this.code, this.data});

  factory MyCollectionSentence.fromJson(Map<String, dynamic> json) {
    return MyCollectionSentence(
      code: json['code'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
      };
}
