import 'sentence.dart';

class Data {
  int? count;
  int? pageNum;
  int? pageSize;
  List<Sentence>? sentences;
  int? sentenceListLength;
  int? totalPage;

  Data({
    this.count,
    this.pageNum,
    this.pageSize,
    this.sentences,
    this.sentenceListLength,
    this.totalPage,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        count: json['count'] as int?,
        pageNum: json['page_num'] as int?,
        pageSize: json['page_size'] as int?,
        sentences: (json['sentence_list'] as List<dynamic>?)
            ?.map((e) => Sentence.fromJson(e as Map<String, dynamic>))
            .toList(),
        sentenceListLength: json['sentence_list_length'] as int?,
        totalPage: json['total_page'] as int?,
      );

  Map<String, dynamic> toJson() => {
        'count': count,
        'page_num': pageNum,
        'page_size': pageSize,
        'sentence_list': sentences?.map((e) => e.toJson()).toList(),
        'sentence_list_length': sentenceListLength,
        'total_page': totalPage,
      };
}
