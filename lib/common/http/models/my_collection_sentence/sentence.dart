import 'sentence_list.dart';

class Sentence {
  String? groupBy;
  List<SentenceList>? sentenceList;

  Sentence({this.groupBy, this.sentenceList});

  factory Sentence.fromJson(Map<String, dynamic> json) => Sentence(
        groupBy: json['group_by'] as String?,
        sentenceList: (json['sentence_list'] as List<dynamic>?)
            ?.map((e) => SentenceList.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'group_by': groupBy,
        'sentence_list': sentenceList?.map((e) => e.toJson()).toList(),
      };
}
