class Sentence {
  String? chineseMeaning;
  String? defaultPronunciationUrl;
  int? id;
  int? index;
  bool? learned;
  String? sentence;
  String? roleName;

  String? ukPronunciationUrl;
  String? usPronunciationUrl;
  List<String>? vocabularies;
  String? userPronunciationUrl;
  String? score;
  bool? collected;

  Sentence(
      {this.chineseMeaning,
      this.defaultPronunciationUrl,
      this.id,
      this.index,
      this.learned,
      this.sentence,
      this.ukPronunciationUrl,
      this.usPronunciationUrl,
      this.vocabularies,
      this.userPronunciationUrl,
      this.score,
      this.roleName,
      this.collected});

  factory Sentence.fromJson(Map<String, dynamic> json) => Sentence(
        chineseMeaning: json['chinese_meaning'] as String?,
        defaultPronunciationUrl: json['default_pronunciation_url'] as String?,
        id: json['id'] as int?,
        index: json['index'] as int?,
        learned: json['learned'] as bool?,
        sentence: json['sentence'] as String?,
        ukPronunciationUrl: json['uk_pronunciation_url'] as String?,
        usPronunciationUrl: json['us_pronunciation_url'] as String?,
        vocabularies: List<String>.from(json['vocabularies']),
        userPronunciationUrl: json['user_pronunciation_url'] as String?,
        roleName: json['role_name'] as String?,
        score: "${json['score'] ?? ""}",
        collected: json['collected'] as bool?,
      );

  Map<String, dynamic> toJson() => {
        'chinese_meaning': chineseMeaning,
        'default_pronunciation_url': defaultPronunciationUrl,
        'id': id,
        'index': index,
        'learned': learned,
        'sentence': sentence,
        'uk_pronunciation_url': ukPronunciationUrl,
        'us_pronunciation_url': usPronunciationUrl,
        'vocabularies': vocabularies,
      };
}
