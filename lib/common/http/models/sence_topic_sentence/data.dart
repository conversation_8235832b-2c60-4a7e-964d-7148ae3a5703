import 'list.dart';

class Data {
  int? hasLearnedCount;
  List<Sentence>? list;
  int? listSize;

  Data({this.hasLearnedCount, this.list, this.listSize});

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        hasLearnedCount: json['has_learned_count'] as int?,
        list: (json['list'] as List<dynamic>?)
            ?.map((e) => Sentence.fromJson(e as Map<String, dynamic>))
            .toList(),
        listSize: json['list_size'] as int?,
      );
}
