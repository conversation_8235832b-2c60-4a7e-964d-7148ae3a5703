import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_soe_model/words.dart';

class Data {
  String? audioUrl;
  String? defaultAudioUrl;
  int? pronAccuracy;
  int? pronCompletion;
  int? pronFluency;
  int? speed;
  String? speedComment;
  String? suggestedComment;
  int? suggestedScore;
  String? text;
  String? ukAudioUrl;
  String? usAudioUrl;
  List<Words>? words;

  Data(
      {this.audioUrl,
      this.defaultAudioUrl,
      this.pronAccuracy,
      this.pronCompletion,
      this.pronFluency,
      this.speed,
      this.speedComment,
      this.suggestedComment,
      this.suggestedScore,
      this.text,
      this.ukAudioUrl,
      this.usAudioUrl,
      this.words});

  Data.fromJson(Map<String, dynamic> json) {
    audioUrl = json['audio_url'];
    defaultAudioUrl = json['default_audio_url'];
    pronAccuracy = json['pron_accuracy'];
    pronCompletion = json['pron_completion'];
    pronFluency = json['pron_fluency'];
    speed = json['speed'];
    speedComment = json['speed_comment'];
    suggestedComment = json['suggested_comment'];
    suggestedScore = json['suggested_score'];
    text = json['text'];
    ukAudioUrl = json['uk_audio_url'];
    usAudioUrl = json['us_audio_url'];
    if (json['words'] != null) {
      words = <Words>[];
      json['words'].forEach((v) {
        words!.add(new Words.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['audio_url'] = this.audioUrl;
    data['default_audio_url'] = this.defaultAudioUrl;
    data['pron_accuracy'] = this.pronAccuracy;
    data['pron_completion'] = this.pronCompletion;
    data['pron_fluency'] = this.pronFluency;
    data['speed'] = this.speed;
    data['speed_comment'] = this.speedComment;
    data['suggested_comment'] = this.suggestedComment;
    data['suggested_score'] = this.suggestedScore;
    data['text'] = this.text;
    data['uk_audio_url'] = this.ukAudioUrl;
    data['us_audio_url'] = this.usAudioUrl;
    if (this.words != null) {
      data['words'] = this.words!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
