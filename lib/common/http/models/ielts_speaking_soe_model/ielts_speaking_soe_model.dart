import 'package:flutter_app_kouyu/common/http/models/ielts_speaking_soe_model/data.dart';

class IeltsSpeakingSoeModel {
  String? code;
  Data? data;

  IeltsSpeakingSoeModel({this.code, this.data});

  IeltsSpeakingSoeModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}
