import 'dart:ui';

class Words {
  int? pronAccuracy;
  int? pronFluency;
  int? rank;
  String? rightSymbol;
  String? word;

  Words(
      {this.pronAccuracy,
      this.pronFluency,
      this.rank,
      this.rightSymbol,
      this.word});

  Words.fromJson(Map<String, dynamic> json) {
    pronAccuracy = json['pron_accuracy'];
    pronFluency = json['pron_fluency'];
    rank = json['rank'];
    rightSymbol = json['right_symbol'];
    word = json['word'];
  }

  Color get color {
    if (rank == 1) {
      return const Color.fromARGB(255, 249, 50, 0);
    }
    if (rank == 2) {
      return const Color(0xFF061B1F);
    }
    if (rank == 3) {
      return const Color.fromARGB(255, 42, 201, 92);
    }
    return const Color(0xFF061B1F);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['pron_accuracy'] = this.pronAccuracy;
    data['pron_fluency'] = this.pronFluency;
    data['rank'] = this.rank;
    data['right_symbol'] = this.rightSymbol;
    data['word'] = this.word;
    return data;
  }
}
