// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

enum LoadingStatus {
  idle,

  /// The request is in the process of being submitted.
  inProgress,

  /// The form has been submitted successfully.
  success,

  /// The form submission failed.
  failure,

  /// The form submission has been canceled.
  canceled
}

class LoadingState extends Equatable {
  final LoadingStatus? status;
  final String? message;

  const LoadingState({this.status, this.message});

  @override
  List<Object?> get props => [status, message];
}
