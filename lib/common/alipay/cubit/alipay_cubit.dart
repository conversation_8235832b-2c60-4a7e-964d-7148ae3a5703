import 'dart:async';

import 'package:alipay_kit/alipay_kit.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_app_kouyu/common/config/env.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/utils/log.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wechat_kit/wechat_kit.dart';

part 'alipay_state.dart';

class PayCubit extends Cubit<AlipayState> {
  Function(AlipayResp)? callBack;
  Function(WechatPayResp)? wxPayCallBack;

  PayCubit({this.callBack}) : super(AlipayInitial()) {
    _paySubs = AlipayKitPlatform.instance.payResp().listen(_listenPay);
    _respSubs = WechatKitPlatform.instance.respStream().listen(_listenResp);
  }
  late final StreamSubscription<AlipayResp> _paySubs;
  late final StreamSubscription<WechatResp> _respSubs;

  @override
  Future<void> close() {
    _paySubs.cancel();
    _respSubs.cancel();
    return super.close();
  }

  alipay(int packageType, Function(AlipayResp) callBack) async {
    this.callBack = callBack;
    final orderInfo = await Api.alipayRequest({"package_type": packageType});
    // if (EnvConfig.env == Env.product) {
    //   await AlipayKitPlatform.instance.setEnv(env: AlipayEnv.online);
    // } else {
    //   await AlipayKitPlatform.instance.setEnv(env: AlipayEnv.sandbox);
    // }
    AlipayKitPlatform.instance.pay(orderInfo: orderInfo);
  }

  wxpay(int packageType, Function(WechatPayResp) callBack) async {
    wxPayCallBack = callBack;
    final orderInfo = await Api.wxpayRequest({"package_type": packageType});

    WechatKitPlatform.instance.pay(
        appId: orderInfo.data!.appid!,
        partnerId: orderInfo.data!.partnerid!,
        prepayId: orderInfo.data!.prepayid!,
        package: orderInfo.data!.package!,
        nonceStr: orderInfo.data!.nonceStr!,
        timeStamp: orderInfo.data!.timestamp!,
        sign: orderInfo.data!.sign!);
  }

  void _listenPay(AlipayResp resp) {
    final String content = 'pay: ${resp.resultStatus} - ${resp.result}';
    Log.v("alipay result: $content");
    callBack?.call(resp);
  }

  void _listenResp(WechatResp resp) {
    if (resp is WechatAuthResp) {
      final String content = 'auth: ${resp.errorCode} ${resp.errorMsg}';
    } else if (resp is WechatShareMsgResp) {
      final String content = 'share: ${resp.errorCode} ${resp.errorMsg}';
    } else if (resp is WechatPayResp) {
      final String content = 'pay: ${resp.errorCode} ${resp.errorMsg}';
      Log.v('支付:$content');
      wxPayCallBack?.call(resp);
    } else if (resp is WechatLaunchMiniProgramResp) {
      final String content = 'mini program: ${resp.errorCode} ${resp.errorMsg}';
    }
  }
}
