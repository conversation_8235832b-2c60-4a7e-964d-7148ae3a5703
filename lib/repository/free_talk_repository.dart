// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/loading_status.dart';
import 'package:flutter_app_kouyu/common/http/models/analyze_model/analyze_model.dart';
import 'package:flutter_app_kouyu/common/http/models/audio_upload_model/audio_upload_model.dart';
import 'package:flutter_app_kouyu/common/http/models/ch_to_en_audio_model/ch_to_en_audio_model.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_hint_model/chat_hint_model.dart';
import 'package:flutter_app_kouyu/common/http/models/chat_model/chat_model.dart';
import 'package:flutter_app_kouyu/common/http/models/collect_sentence.dart';
import 'package:flutter_app_kouyu/common/http/models/en_to_ch_model.dart';
import 'package:flutter_app_kouyu/common/http/models/greeting_model/greeting_model.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';

class FreeTalkRepository {
  Future<TalkModel> getTimePeriodGreeting(
      {required int model, required String topic}) async {
    GreetingModel result =
        await Api.getTimePeriodGreeting({"mode": model, "topic": topic});
    return TalkModel.fromModel(result);
  }

  Future<TalkModel> getChat(data) async {
    ChatModel model = await Api.chatSummaryPredict(data);
    return TalkModel.fromChatModel(model);
  }

  Future<EnToCnModel> translate(String? text) async {
    return Api.translateToCH(text);
  }

  Future<CollectionSentence> collectionSentence(
      String? text, String source, String sourceId) {
    return Api.collectionSentence(text, source, sourceId);
  }

  Future<CollectionSentence> cancelCollectionSentence(String? text) {
    return Api.cancelCollectionSentence(text);
  }

  Future<CollectionSentence> collectVocabulary(String? text) {
    return Api.collectVocabulary(text);
  }

  Future<CollectionSentence> cancelCollectVocabulary(String? text) {
    return Api.cancelCollectVocabulary(text);
  }

  Future<ChatHintModel> getChatHist(String text) {
    return Api.getChatHint(text);
  }

  Future<ChatHintModel> generateNextSentence(
      String? topicName, String chatMoudle, int conversationId) {
    return Api.generateNextSentence(topicName, chatMoudle, conversationId);
  }

  Future<ChToEnAudioModel> chToEn(String text) {
    return Api.cnToEn(text);
  }

  Future<AnalyzeModel> analyze(TalkModel model, String sence) {
    return Api.analyze({
      "text": model.question,
      "client_sentence_id": model.clientSentenceId,
      "conversation_id": model.conversationId,
      "scene": sence,
    });
  }

  Future<SoeModel> soe(TalkModel model, String sence) {
    return Api.soe({
      "text": model.question,
      "audio_url": model.audioUrl,
      "client_sentence_id": model.clientSentenceId,
      "conversation_id": model.conversationId,
      "scene": sence,
    });
  }

  Future<AudioUploadModel> upload(String filePath,
      {required String topicCode, required String sence}) async {
    return await Api.uploadFile(filePath, topicCode: topicCode, scene: sence);
  }
}

class FreeTalkCache {
  final _cache = <String, List<TalkModel>>{};

  List<TalkModel>? get(String userId, conversationId) => _cache["term"];

  void set(String term, List<TalkModel> results) => _cache[term] = results;

  bool contains(String term) => _cache.containsKey(term);

  void remove(String term) => _cache.remove(term);
}

enum TalkSteamType { text, audioUrl, fullAudioUrl, unknown }

extension TalkSteamTypeExtension on TalkSteamType {
  String get name {
    switch (this) {
      case TalkSteamType.text:
        return "token";
      case TalkSteamType.audioUrl:
        return "audio_url";
      case TalkSteamType.fullAudioUrl:
        return "full_audi_url";
      case TalkSteamType.unknown:
        return "unknown";
    }
  }
}

class TalkSteamModel {
  //token 文字  audio_url 部分音频地址 full_audi_url 完整音频地址
  final TalkSteamType type;
  final String content;
  TalkSteamModel({
    required this.type,
    required this.content,
  });
  //json字符串转换
  factory TalkSteamModel.fromString(String jsonStr) {
    Map<String, dynamic> json = jsonDecode(jsonStr);
    final typeStr = json['type'];
    final content = json['content'];
    TalkSteamType type = TalkSteamType.unknown;
    //根据字符串匹配类型
    for (var element in TalkSteamType.values) {
      if (element.name == typeStr) {
        type = element;
        continue;
      }
    }

    return TalkSteamModel(
      type: type,
      content: content,
    );
  }
}

class TalkModel {
  final num? conversationId;
  final num? clientSentenceId;
  String? question;
  String? audioUrl;
  List<String> streamAudioUrls = [];
  String? translateText;
  //0 question  1  reply
  final int type;
  AnalyzeModel? analyzeModel;
  LoadingState? analyzeStatus;
  SoeModel? soeModel;
  LoadingState? soeStatus;
  final LoadingState? replyStatus;
  LoadingState? translateStatus;
  bool? collected;

  //雅思
  final int? practiceId;
  final int? questionId;

  TalkModel(
      {required this.type,
      this.conversationId,
      this.question,
      this.audioUrl,
      this.translateText,
      this.analyzeModel,
      this.soeModel,
      this.soeStatus,
      this.analyzeStatus,
      this.translateStatus,
      this.clientSentenceId,
      this.collected,
      this.replyStatus,
      this.practiceId,
      this.questionId});

  static TalkModel questionModel(
      num? conversationId, String? question, String? audioUrl,
      {LoadingState? analyzeStatus, LoadingState? soeStatus}) {
    return TalkModel(
        type: 1,
        conversationId: conversationId,
        question: question,
        audioUrl: audioUrl,
        analyzeStatus: analyzeStatus,
        clientSentenceId: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        soeStatus: soeStatus);
  }

  static TalkModel replyModel(num? conversationId, String? question,
      String? audioUrl, String? translateText) {
    return TalkModel(
        type: 0,
        conversationId: conversationId,
        question: question,
        audioUrl: audioUrl,
        translateText: translateText,
        replyStatus: const LoadingState(status: LoadingStatus.success));
  }

  factory TalkModel.fromModel(GreetingModel model) {
    return TalkModel(
        type: 0,
        conversationId: model.data?.conversationId,
        question: model.data?.greeting,
        audioUrl: model.data?.replyAudioUrl,
        translateText: "",
        replyStatus: const LoadingState(status: LoadingStatus.success));
  }

  factory TalkModel.fromChatModel(ChatModel model) {
    return TalkModel(
        type: 0,
        conversationId: model.data?.conversationId,
        question: model.data?.reply,
        audioUrl: model.data?.replyAudioUrl,
        clientSentenceId: model.data?.clientSentenceId,
        translateText: "",
        replyStatus: const LoadingState(status: LoadingStatus.success));
  }
}
