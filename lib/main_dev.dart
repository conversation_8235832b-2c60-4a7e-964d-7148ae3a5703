import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_kouyu/common/config/env.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/my_app.dart';

import 'common/utils/wx_sdk.dart';

void main() {
  //打包测试环境
  //flutter build ipa --export-method development --target=lib/main_dev.dart
  //sh ./pgyer_upload.sh -k cff5ed5278442901496c6a3c396d0aca build/ios/ipa/flutter_app_kouyu.ipa
  SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(statusBarColor: Colors.transparent));

  WidgetsFlutterBinding.ensureInitialized();

  realRunApp();
}

void realRunApp() async {
  await CommonUtils.init();
  LogUtil.d('=======CommonUtils init success');
  EnvConfig.init(Env.develop);
  LogUtil.d('=======EnvConfig init success');
  WXSDK.initSDK();
  LogUtil.d('=======WXSDK init success');
  //延时1s
  await Future.delayed(const Duration(seconds: 1));
  LogUtil.d('=======runApp start');
  runApp(const MyApp());
  LogUtil.v("开发环境欢迎你");
}
