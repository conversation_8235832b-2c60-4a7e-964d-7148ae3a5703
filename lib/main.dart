import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_kouyu/common/config/env.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/common/utils/wx_sdk.dart';
import 'package:flutter_app_kouyu/my_app.dart';

void main() {
  //打包正式环境
  //flutter build ios --target=lib/main_.dart
  SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(statusBarColor: Colors.transparent));

  WidgetsFlutterBinding.ensureInitialized();

  realRunApp();
}

void realRunApp() async {
  await CommonUtils.init();
  EnvConfig.init(Env.product);
  WXSDK.initSDK();

  //延时1s
  await Future.delayed(const Duration(seconds: 1));
  runApp(const MyApp());
  LogUtil.v("线上环境欢迎你");
}
