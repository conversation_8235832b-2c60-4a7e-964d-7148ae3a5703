name: flutter_app_kouyu
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.8+14

environment:
  sdk: ">=3.3.0 <4.0.0"

# fluwx:
  # debug_logging: true # Logging in debug mode.
  # android:
    # interrupt_wx_request: true # Defaults to true.
    # flutter_activity: 'MainActivity' # Defaults to app's launcher
  # ios:
    # scene_delegate: true # Defaults to false.
    # no_pay: true # Set to true to disable payment.
    # ignore_security: true # Set to true to disable security seetings.

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  # 网络库相关
  dio: ^5.4.1
  flutter_easyloading: ^3.0.5
  common_utils: ^2.1.0
  shared_preferences: ^2.2.2
  image_gallery_saver: ^2.0.3  # 请检查最新版本
  screenshot: ^2.5.0 # 截图, 报告页面截长图
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  flutter_bloc: ^8.1.5
  formz: ^0.7.0
  equatable: ^2.0.0
  sqflite: ^2.3.3
  carousel_slider: ^5.0.0
    # git:
    #   url: https://github.com/sampaio96/flutter_carousel_slider.git
    #   ref: master
  path: ^1.9.0
  asr_plugin:
    # 该路径根据asr_plugin存放路径改变
    path: ./plugin/asr_plugin
  # just_audio:
  #   path: ./plugin/just_audio-0.9.40
  path_provider: ^2.1.3
  in_app_purchase: ^3.2.0
  package_info_plus: ^8.0.0
  url_launcher: ^6.2.6
  webview_flutter: ^4.7.0
  card_swiper: ^3.0.1
  image_picker: ^1.1.1
  logger: ^2.2.0
  gif_view: ^0.3.1
  rxdart: ^0.27.7
  permission_handler: 10.4.5
  cached_network_image: ^3.3.1
  flutter_screenutil: ^5.9.3
  flutter_platform_alert: ^0.5.1
  flutter_udid: ^3.0.0
  encrypt: ^5.0.3
  pointycastle: ^3.9.1
  provider: ^6.1.2
  pull_to_refresh: ^2.0.0
  flutter_vlc_player: ^7.4.2
  audio_session: ^0.1.21
  flutter_slidable: ^3.1.1
  fluttertoast: ^8.2.0
  camera: ^0.10.1
  lottie: ^3.0.0
  alipay_kit: ^6.0.0
  audioplayers: ^4.1.0
  wechat_kit: ^6.0.2
  # video_player: ^2.9.1
  # just_audio: ^0.9.39
  flutter_keyboard_visibility: ^5.4.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - images/
    - lottie/
    - assets/audio/mission_complete.mp3
    - assets/audio/success_result.mp3
    - assets/audio/wrong_answer.mp3
    - assets/audio/correct_answer.wav
    - assets/audio/call_connected.mp3
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # fonts:
  #   - family: puhui
  #     fonts:
  #       - asset: fonts/AlibabaPuHuiTi-3-115-Black.ttf

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
wechat_kit:
#  ios: no_pay # 默认 pay
  app_id: wx8a882167ca93bd37
  universal_link: https://www.xinquai.com/app/